package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.PageResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @Description:【商户物料管理】查询结果
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantMaterialPageResultDTO extends PageResponseDTO {

    /**
     * 商户物料信息列表
     */
    List<MerchantMaterialInfoDTO> List;

}
