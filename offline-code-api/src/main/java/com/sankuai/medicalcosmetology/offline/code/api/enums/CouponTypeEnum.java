package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chen<PERSON>yang02
 * @Date: 2024/9/26
 * @Description:
 */
@Getter
public enum CouponTypeEnum {

    OFFLINE_CODE(1, "线下码渠道券", 2),
    MAGICAL_MEMBER(2, "神会员券", 99),
    OFFLINE_CODE_POST_PURCHASE(3, "线下码购后券", 3),
    WECOM(4, "企微渠道券", 1);

    public final int code;
    public final String desc;
    public final int priority;

    CouponTypeEnum(int code, String desc, int priority) {
        this.code = code;
        this.desc = desc;
        this.priority = priority;
    }

    public static CouponTypeEnum getByCode(Integer code) {
        for (CouponTypeEnum couponTypeEnum : CouponTypeEnum.values()) {
            if (couponTypeEnum.code == code) {
                return couponTypeEnum;
            }
        }
        return null;
    }

    public static boolean validateCode(Integer code) {
        return getByCode(code) != null;
    }
}
