package com.sankuai.medicalcosmetology.offline.code.api.request.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/7
 * @Description: 落地页券信息查询入参
 */
@Data
public class LandingCouponRequest extends LandingBaseRequest implements Serializable {

    /**
     * 领券活动id
     */
    @Deprecated
    private Long activityId;

    /**
     * 是否执行抽奖逻辑
     */
    private Boolean execDraw;

    /**
     * 标识map的json格式
     */
    @Deprecated
    private String flagMapStr;

    /**
     * 前端神券券包通用组件版本
     */
    private String magicMemberComponentVersion;
}
