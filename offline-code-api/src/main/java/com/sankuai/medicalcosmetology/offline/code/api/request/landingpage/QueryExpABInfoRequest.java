package com.sankuai.medicalcosmetology.offline.code.api.request.landingpage;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpDecrypt;
import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageScanSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.EnvRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/3
 * @Description:
 */
@Data
public class QueryExpABInfoRequest extends EnvRequest implements Serializable {

    /**
     * 实验ID
     */
    private String expId;

    /**
     * 美团门店id
     */
    private Long shopId;

    /**
     * 美团门店id加密字段
     */
    @MdpDecrypt(target="shopId")
    private String shopIdEncrypt;

    /**
     * 扫码来源，默认为线下码
     * @see LandingPageScanSourceEnum
     */
    private Integer source = 0;

}
