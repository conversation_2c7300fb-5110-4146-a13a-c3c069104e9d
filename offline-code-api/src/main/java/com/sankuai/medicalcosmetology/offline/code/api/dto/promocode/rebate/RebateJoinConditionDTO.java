package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityJoinConditionCityTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityJoinConditionTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动参与条件DTO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RebateJoinConditionDTO implements Serializable {
    private static final long serialVersionUID = -4032354076557541442L;

    /**
     * 参与条件类型：1 点评门店白名单；2 点评门店楼层id；3 新签商户；4 类目+城市
     *
     * @see RebateActivityJoinConditionTypeEnum
     */
    private Integer type;

    /**
     * 点评门店白名单列表
     */
    private List<Long> dpShopIds;

    /**
     * 点评门店楼层id
     */
    private Long dpShopItemId;
    /**
     * 报名生效持续时间（单位：天）
     */
    private Integer signUpDuration;
    /**
     * 可报名开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityDisplayStartTime;
    /**
     * 可报名结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityDisplayEndTime;
    /**
     * 商户入驻后能看到活动的时间（单位：天）
     */
    private Integer activityDisplayDuration;
    /**
     * 城市类型（直营、非直营、全部）
     *
     * @see RebateActivityJoinConditionCityTypeEnum
     */
    private Integer cityType;
    /**
     * 点评城市 id 列表
     */
    private List<Integer> dpCityIdList;

    /**
     * 平台限制，1-点评，2-美团，0-双平台
     */
    private Integer platform;
    /**
     * 新签约门店是否自动加入返利活动
     */
    private Boolean newShopAutoRegister;
    /**
     * 系统导入的门店楼层
     */
    private Long newShopImportFloor;

    /**
     * 新签门店后台一级类目
     */
    private List<Integer> newShopCategoryIds;
    /**
     * 新签门店后台二级类目
     */
    private List<Integer> newShopSecondCategoryIds;
}
