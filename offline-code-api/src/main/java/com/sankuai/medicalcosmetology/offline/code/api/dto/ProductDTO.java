package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12 14:19
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductDTO implements Serializable {
    /**
     * 商品id
     */
    private Long productId;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.ProductTypeEnum
     */
    private Integer productType;

    /**
     * 扩展字段
     */
    private Map<String,Object> extMap;


    public static ProductDTO init(Long productId, Integer productType) {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setProductId(productId);
        productDTO.setProductType(productType);
        return productDTO;
    }
}
