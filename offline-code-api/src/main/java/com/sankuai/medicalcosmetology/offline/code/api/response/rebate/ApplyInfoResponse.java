package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 账号参与活动信息
 */
@Data
public class ApplyInfoResponse implements Serializable {
    private static final long serialVersionUID = 7822363335643365953L;

    /**
     * 是否展示报名信息模块
     */
    private Boolean showApplyInfo;

    /**
     * 所有报名记录已过期
     */
    private boolean allApplyExpired;

    /**
     * 报名过的活动门店信息
     */
    private List<AppliedInfoResponse> appliedInfo;
}
