package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: <PERSON><PERSON>yu
 * @Date: 2024/5/30
 * @Description:
 */
@Getter
public enum RegistrationActivityTypeEnum {

    PROMO_CODE_SHOP_REBATE(1, "优惠码门店返利活动"),
    TECH_SHARE_REBATE(2, "职人分享赚");

    public int code;
    public String desc;

    private RegistrationActivityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RegistrationActivityTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RegistrationActivityTypeEnum activityTypeEnum : values()) {
            if (activityTypeEnum.getCode() == code) {
                return activityTypeEnum;
            }
        }
        return null;
    }
}
