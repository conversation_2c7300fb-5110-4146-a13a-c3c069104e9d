package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/13
 * @Description:
 */
@Data
public class UserScanRecordDTO implements Serializable {

    private Long userId;

    /**
     * 平台
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;

    private Long shopId;

    /**
     * 扫码来源
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PromoQRCodeScanSourceType
     */
    private Integer sourceType;

    /**
     * 门店码时，为mt门店id
     * 职人码时，为职人码id
     * 品牌码时，为统一客户id
     * 商品码时，为mt商品id
     */
    private Long sourceId;

    /**
     * 线下码时，为二维码加密code
     * 地推同步时，为兼职二维码code
     */
    private String codeKey;

    private Date createTime;

    private Date updateTime;
}
