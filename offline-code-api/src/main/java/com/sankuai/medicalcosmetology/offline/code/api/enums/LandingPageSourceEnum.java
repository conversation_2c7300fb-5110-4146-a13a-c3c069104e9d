package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chen<PERSON>yang02
 * @Date: 2024/7/2
 * @Description:
 */
@Getter
public enum LandingPageSourceEnum {

    DEFAULT(1, "默认", "default"),

    EMPTY(2, "空码", "wind_empty"),
    GROUND_PROMOTION_PART_TIME(3, "兼职地推", "ground_promotion_part_time"),
    AOI_EMPTY(4, "AOI码", "aoi_empty")
    ;

    private final int code;
    private final String desc;
    private final String key;

    LandingPageSourceEnum(int code, String desc, String key) {
        this.code = code;
        this.desc = desc;
        this.key = key;
    }

    public static LandingPageSourceEnum fromCode(Integer code) {
        if (code == null) {
            return DEFAULT;
        }
        for (LandingPageSourceEnum enumValue : LandingPageSourceEnum.values()) {
            if (code.equals(enumValue.getCode())) return enumValue;
        }
        return DEFAULT;
    }
}