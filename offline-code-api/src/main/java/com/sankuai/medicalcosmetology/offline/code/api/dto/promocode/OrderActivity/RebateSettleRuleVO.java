package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import lombok.Data;

import java.io.Serializable;


@Data
public class RebateSettleRuleVO implements Serializable {
    private static final long serialVersionUID = 7659962007137627382L;

    private Integer type;
    private Integer factorType;
    private RebateSettleConditionVO condition;
    private RebateSettleNormalRuleVO normalRule;
    private RebateSettleStepRuleVO stepRule;
    private RebatePaymentConfigVO paymentConfig;
    private RebateSettleCustomerStepRuleVO newCustomerStepRule;
    private RebateSettleCustomerStepRuleVO oldCustomerStepRule;
    private RebateOrderPriceProportionRuleVO priceProportionRule;
    private RebateSettleOrderPriceStepRuleVO orderPriceStepRule;
    private RebateSettleLimitRuleVO rebateSettleLimitRule;
    private Long dailyFirstOrderRebateAmount;

}
