package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15
 * @Description:
 */
@Data
public class AwardInfoDTO implements Serializable {

    /** 神券活动 */
    private List<AwardActivityDTO> magicAwardConfig = new ArrayList<>();

    /** 主发券活动 */
    private List<AwardActivityDTO> mainAwardConfig = new ArrayList<>();

    /** 交叉发券活动 */
    private List<List<AwardActivityDTO>> multiAwardConfig = new ArrayList<>();
}
