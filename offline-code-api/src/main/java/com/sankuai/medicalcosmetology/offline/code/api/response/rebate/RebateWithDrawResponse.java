package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 提现结果
 */
@Data
public class RebateWithDrawResponse implements Serializable {
    private static final long serialVersionUID = -6842387489226908649L;

    /**
     * 发起提现金额
     */
    private Long amount;
    /**
     * 实际提现金额，存在平账操作，实际金额可能小于amount
     */
    private Long actualAmount;

    /**
     * 提现的手机号[美团用户]数
     */
    private Integer mtUserIdCnt;

    /**
     * 备注
     */
    private String memo;

    /**
     * 兑换达到月额度上限的手机号列表
     */
    private List<String> limitedPhoneNums;

    public RebateWithDrawResponse() {
        this.amount = 0L;
        this.actualAmount = 0L;
        this.mtUserIdCnt = 0;
        this.limitedPhoneNums = Lists.newArrayList();
    }

    public void addAmount(Long amount) {
        this.amount += amount;
    }

    public void addActualAmount(Long actualAmount) {
        this.actualAmount += actualAmount;
    }

    public void appendMemo(String memo) {
        this.memo += (" " + memo);
    }

    public void incrMtUserIdCnt() {
        this.mtUserIdCnt += 1;
    }

    public void incrMtUserIdCnt(Integer count) {
        this.mtUserIdCnt += count;
    }

    public void addLimitedPhoneNums(String limitedPhoneNum) {
        if (StringUtils.isBlank(limitedPhoneNum)) {
            return;
        }
        if (!this.limitedPhoneNums.contains(limitedPhoneNum)) {
            this.limitedPhoneNums.add(limitedPhoneNum);
        }
    }

    public void addLimitedPhoneNums(List<String> limitedPhoneNums) {
        if (CollectionUtils.isEmpty(limitedPhoneNums)) {
            return;
        }
        for (String limitedPhoneNum : limitedPhoneNums) {
            this.addLimitedPhoneNums(limitedPhoneNum);
        }
    }
}
