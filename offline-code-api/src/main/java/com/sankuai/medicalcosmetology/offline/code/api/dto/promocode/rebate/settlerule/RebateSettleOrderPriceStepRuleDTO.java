package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateOrderPriceStepConfigDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单价格阶梯返利规则DTO
 */
@Data
public class RebateSettleOrderPriceStepRuleDTO implements Serializable {

    private static final long serialVersionUID = -6458729065386275187L;

    /**
     * 订单价格阶梯规则
     */
    private List<RebateOrderPriceStepConfigDTO> ruleStep;
}
