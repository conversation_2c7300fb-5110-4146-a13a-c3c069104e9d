package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
@Data
public class PromoCodeModelDTO implements Serializable {
    private Integer modelId;
    private String modelName;
    private List<Long> shopIdList;
    private List<Long> dealGroupIdList;
    private Date createTime;
    private Date updateTime;

    private List<Integer> tradeTypes;
}
