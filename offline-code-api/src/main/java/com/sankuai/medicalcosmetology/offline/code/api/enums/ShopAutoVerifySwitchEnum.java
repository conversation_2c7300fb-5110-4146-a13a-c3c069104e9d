package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * 功能描述: 门店自动核销开关状态
 *
 * <AUTHOR>
 * @date 2024/6/11
 **/
@Getter
public enum ShopAutoVerifySwitchEnum {

    UNKNOWN(0, "未知"),
    AUTO_VERIFY(1, "自动核销"),
    MANUAL_VERIFY(2, "手动核销");

    private final int code;

    private final String desc;

    ShopAutoVerifySwitchEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShopAutoVerifySwitchEnum fromCode(int code) {
        for (ShopAutoVerifySwitchEnum enumValue : ShopAutoVerifySwitchEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }
}
