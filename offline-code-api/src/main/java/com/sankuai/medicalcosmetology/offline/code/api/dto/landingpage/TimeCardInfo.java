package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lipengyu04
 * @Date: 2024/8/19
 * @Description:
 */
@Data
public class TimeCardInfo implements Serializable {

    /**
     * 销售数量
     */
    private Integer saleCount;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 单价
     */
    private String singlePrice;

    /**
     * 卡片价格
     */
    private String cardPrice;

    /**
     * 次数
     */
    private Integer times;

    /**
     * 小标题
     */
    private String smallTitle;

    /**
     * 特征信息
     */
    private String characteristicInfo;

    /**
     * 卡片图片
     */
    private String cardPic;

    /**
     * 活动信息
     */
    private String activityInfo;

    /**
     * 详情链接
     */
    private String detailUrl;

    /**
     * 折扣促销信息
     */
    private String disPromc;

    /**
     * 大背景图片
     */
    private String bigBackGroupPic;

    /**************************************以下字段是迁移未用到的字段********************
     * 做扩展使用*
     */
    private Long dealGroupId;
    private String subTitle;
    private String assistInfo;
    private Integer cardBizType;
    private Boolean isAssisted;
    private String assistButtonText;
    private String timesDesc;
    private String giftDesc;
    private String storeCardRefundAmount;
    private String storeCardSaleAmount;
    private Integer cardType;
    private String refundDesc;
    private String cardMarketPrice;
    private String promoDesc;
    private String dealPrice;
    private String marketPrice;
    private String title;
}
