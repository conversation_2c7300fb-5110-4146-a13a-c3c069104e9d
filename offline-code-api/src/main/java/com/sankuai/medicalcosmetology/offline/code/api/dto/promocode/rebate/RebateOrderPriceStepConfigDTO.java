package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 订单价格阶梯配置
 */
@Data
public class RebateOrderPriceStepConfigDTO implements Serializable {

    private static final long serialVersionUID = -5162074936818489886L;

    /**
     * 阶梯序号
     */
    private Integer stepIndex;

    /**
     * 订单价格限制
     */
    private Long limitOrderPrice;

    /**
     * 返利金额
     */
    private Long rebateAmount;
}
