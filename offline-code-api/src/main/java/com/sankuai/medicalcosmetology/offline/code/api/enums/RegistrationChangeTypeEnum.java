package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: <PERSON><PERSON>yu
 * @Date: 2024/5/30
 * @Description:
 */
@Getter
public enum RegistrationChangeTypeEnum {

    ADD(1, "报名"),
    UPDATE(2, "修改报名"),
    DELETE(3, "取消报名"),
    AUTO(4, "自动报名");

    public int code;
    public String desc;

    private RegistrationChangeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RegistrationChangeTypeEnum getByCode(Integer code) {
        for (RegistrationChangeTypeEnum typeEnum : RegistrationChangeTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }
        return null;
    }
}
