package com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AdvertiseActivityLiteDTO implements Serializable {

    private Long id;
    private String activityName;
    private Date startTime;
    private Date endTime;
    private String sourceName;
    private Long sourceId;
    private String creator;
    private Integer status;
}
