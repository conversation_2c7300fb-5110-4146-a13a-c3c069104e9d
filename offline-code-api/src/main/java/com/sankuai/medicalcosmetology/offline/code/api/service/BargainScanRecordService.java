package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.BargainScanRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.BargainScanRecordRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
public interface BargainScanRecordService {

    /**
     * 查询改价扫码记录
     *
     * @param request 入参
     * @return 扫码记录
     */
    RemoteResponse<List<BargainScanRecordDTO>> queryBargainScanRecord(BargainScanRecordRequest request);

    /**
     * 插入改价扫码记录
     *
     * @param scanRecordDTO 改价扫码记录
     * @return 结果
     */
    RemoteResponse<Boolean> addBargainScanRecord(BargainScanRecordDTO scanRecordDTO);
}