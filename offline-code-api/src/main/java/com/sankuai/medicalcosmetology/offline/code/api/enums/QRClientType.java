package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chen<PERSON>yang02
 * @Date: 2024/4/19
 * @Description:
 */
@Getter
public enum QRClientType {
    // 注：前端目前只有0、1、2、3。站外浏览器传兜底0
    OTHER(0, "兜底值"),
    DP_APP(1, "点评APP"),
    MT_APP(2, "美团APP"),
    MT_WE_CHAT_APPLET(3, "美团微信小程序"),
    DP_H5(4, "点评H5"),
    MT_H5(5, "美团H5");


    public final int code;
    public final String desc;

    QRClientType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validClientType(int code) {
        for (QRClientType type : QRClientType.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static QRClientType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (QRClientType type : QRClientType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static boolean isApp(int code) {
        return code == QRClientType.DP_APP.code || code == QRClientType.MT_APP.code;
    }

    public static boolean isMt(int code) {
        return code == QRClientType.MT_APP.code || code == QRClientType.MT_WE_CHAT_APPLET.code || code == QRClientType.MT_H5.code;
    }

    public static boolean isXcx(int code) {
        return code == QRClientType.MT_WE_CHAT_APPLET.code;
    }

    public static boolean isH5(int code) {
        return code == QRClientType.MT_H5.code || code == QRClientType.DP_H5.code;
    }
}
