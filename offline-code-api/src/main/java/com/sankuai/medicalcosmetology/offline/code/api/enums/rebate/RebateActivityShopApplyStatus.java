package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 返利活动商家报名状态
 */
public enum RebateActivityShopApplyStatus {
    NOT_APPLY(1,"未报名（不展示【修改报名信息】，展示【查看详情】、【报名活动】按钮）"),
    PART_APPLIED_NOT_START(2,"部分报名-未开始（未开始，展示【查看详情】、【修改报名信息】、【报名活动】按钮）"),
    ALL_APPLIED__NOT_START(3, "全部报名-未开始（未开始，展示【查看详情】、【修改报名信息】，不展示【报名活动】按钮）"),
    PART_APPLIED_START(4,"部分报名-已开始（已生效，展示【查看详情】、【修改报名信息】、【报名活动】按钮）"),
    ALL_APPLIED_START(5, "全部报名-已开始（已生效，展示【查看详情】、【修改报名信息】，不展示【报名活动】按钮）"),
    END(6, "已报名-已过期（只展示【查看详情】按钮）");

    public Integer code;
    public String desc;

    RebateActivityShopApplyStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
