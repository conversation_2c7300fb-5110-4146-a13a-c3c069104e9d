package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description: 扫码券信息
 */
@Data
public class ScanCouponInfo implements Serializable {

    /**
     * 面额
     */
    private String amount;

    /**
     * 门槛
     */
    private String limitAmount;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String desc;

    private Date startTime;

    private Date endTime;

    /**
     * 券适用范围文案
     */
    private String applicableText;

    /**
     * 券类型
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 券id
     */
    private String unifiedCouponId;

    /**
     * 券批次id
     */
    private String unifiedCouponGroupId;

    private MagicMemberCouponDTO magicMemberCouponDTO;

}
