package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 返利活动类型
 */
public enum RebatePaymentTypeEnum {
    USER(1, "用户手动提现"),
    SYSTEM(2, "系统账期自动提现");

    public Integer code;
    public String desc;

    RebatePaymentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validRebatePaymentType(int code) {
        for (RebatePaymentTypeEnum type : RebatePaymentTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebatePaymentTypeEnum getByCode(int code) {
        for (RebatePaymentTypeEnum type : RebatePaymentTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("error code for RebatePaymentType.");
    }

}
