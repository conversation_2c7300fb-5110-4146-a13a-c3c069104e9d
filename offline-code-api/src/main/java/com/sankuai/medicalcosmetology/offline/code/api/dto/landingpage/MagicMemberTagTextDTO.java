package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/18
 * @Description:神券标签
 */
@Data
public class MagicMemberTagTextDTO implements Serializable {

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 神券标签
     */
    private String magicalMemberCouponTag;

    /**
     * 膨胀文案
     */
    private String inflateShowText;

    /**
     * 优惠金额信息
     */
    private String reduceMoney;

    /**
     * 状态
     * EXPAND:膨胀
     * BUY:购买
     */
    private String status;

    /**
     * 标签展示类型,
     * com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum#findByValue
     */
    private Integer showType;

}
