package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/10
 * @Description: 购后券页面来源枚举
 */
public enum PostPurchaseCouponPageSourceEnum {

    HOMEPAGE(1, "端首页"),
    GROUP_CHANNEL(2, "团购频道页"),
    ORDER_DETAIL_PAGE(3, "订详页"),
    ORDER_VERIFY_PAGE(4, "核销页");

    private final int code;
    private final String desc;

    private PostPurchaseCouponPageSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PostPurchaseCouponPageSourceEnum fromCode(int code) {
        PostPurchaseCouponPageSourceEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            PostPurchaseCouponPageSourceEnum enumValue = var1[var3];
            if (enumValue.getCode() == code) {
                return enumValue;
            }
        }

        return null;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}