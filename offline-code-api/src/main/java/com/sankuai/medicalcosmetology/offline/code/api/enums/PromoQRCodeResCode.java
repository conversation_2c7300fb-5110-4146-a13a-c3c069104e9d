package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/17
 * @Description:
 */
public enum PromoQRCodeResCode {
    SUCCESS("000", "成功"),

    PARAM_ERROR("101", "参数错误"),
    CREATE_ERROR("102", "创建失败"),
    UPDATE_ERROR("103", "更新失败"),
    UPDATE_VERSION_ERROR("104", "版本冲突"),
    MOBILE_ENCRYPT_ERROR("105", "手机号加密失败"),
    MOBILE_DECRYPT_ERROR("106", "手机号解密失败"),
    APPLY_NUM_MAX("107", "您已申请2次，暂不可再次申请，如有特殊情况请联系销售"),
    CREATING("108", "存在创建中的优惠码，请勿重复创建"),
    COOLING("109", "您已成功申请，请等待物料邮寄，如有特殊情况请在14日后重新申请，并填写申请理由"),

    CAN_NOT_EDIT("110", "已开始审核，不可编辑"),
    SHOP_NOT_EXIST("111", "未查到门店信息"),
    NO_PERMISSION("112", "无权操作"),
    ACCOUNT_ERROR("113", "账号异常"),
    SMS_COUNT_NOT_ENOUGH("114", "剩余短信数量不足"),
    SHOP_COUPON_ACTIVITY_NOT_FOUND("115", "未查到商家券信息"),
    SHOP_COUPON_SOURCE_PLATFORM_INVALID("116", "商家券渠道异常"),
    SHOP_COUPON_ERROR("117", "商家券信息错误"),
    // TARGET_MARKET_ALREADY_CREATED("118", "今日已发送过，请明日再试"),
    TARGET_MARKET_PROCESSING("118", "有正常触达中的方案，不能新建方案"),

    OPERATE_TOO_FAST("119", "您点的太快了，请稍候再试"),

    EXPRESS_SUBSCRIBE_FAIL("120", "快递订阅失败"),
    EXPRESS_QUERY_FAIL("121", "快递查询失败"),
    NO_CUSTOMER_SERVICE("122", "没有对应的客服"),

    NO_APPLICABLE_SHOP("123", "抱歉，您所在的城市/行业尚未开通优惠码业务功能，敬请期待"),
    NULL_PARAM("124", "参数为空"),
    APPLY_TIME_MAX("125", "已申请2次，暂不可再次申请"),
    COOLING_PERIOD("126", "有申码记录，14日后再次申请"),
    CAPTCHA_ERROR("127", "验证码错误"),
    NO_THIS_ACTIVITY("128", "此活动不在线"),
    CAN_NOT_APPLY_REBATE("129", "暂时无法参加此活动"),
    NOT_ADMIN("131", "只有管理员账号才能操作此功能"),

    DOING("132", "有用户正在操作此功能"),
    OTHER_HAS_APPLIED("133", "该门店已经被其他管理员账号提报"),
    GOODS_INFO_ERROR("134", "商品信息错误"),
    NOT_OPEN("135", "即将开放，敬请期待"),
    OPERATE_COOL("136", "今日无法再上传"),

    NOT_THIS_CODE("140", "没有对应的码"),
    NONE_USEFUL_ACTIVITY("193", "不存在适用该门店的投放活动"),

    TASK_VIEW_ID_ILLEGAL("201", "任务展示id非法"),
    TASK_REWARD_RECEIVED("202", "任务奖励已领取"),
    TASK_STATUS_ILLEGAL("203", "任务状态异常"),

    FREE_COMMISSION_ACTIVITY_DATA_NULL("301", "免佣活动数据为空"),
    FREE_COMMISSION_PLATFORM_NOT_MATCH("302", "订单交易平台不匹配"),
    FREE_COMMISSION_ORDER_SCAN_TIME_NOT_MATCH("303", "订单扫码时间不匹配"),
    FREE_COMMISSION_ORDER_INFO_QUERY_FAILED("304", "订单详情查询失败"),
    ORDER_HAS_NO_COUPON("305", "查询优惠码订单关联券失败"),
    REPEAT_REQUEST("150", "重复请求"),

    NOT_ACTIVITY_TYPE("151", "活动类型不对"),

    NOT_CREATE_GOODS_CODE("152", "暂未创建该产品商品码"),
    EMPTY_CODE_BIND_FAIL("153", "空码绑定失败，请重试"),
    QUERY_MEDICAL_PROMO_CODE_FAIL("154", "查询医疗线下码失败，请重试"),

    UPLOAD_ERROR("161", "文件上传美团云失败"),
    QUERY_SWAN_DATA_ERROR("162", "查询数仓数据异常"),

    STAFF_ORDER_QUERY_FAIL("171", "职人订单查询失败"),
    STAFF_ORDER_REBATE_QUERY_FAIL("172", "职人订单返利查询失败"),
    STAFF_ORDER_UNIFIED_QUERY_FAIL("173", "职人统一订单查询失败"),

    SYSTEM_ERROR("130", "系统错误"),
    PARAM_OUT_BOUNDS("131", "参数越界");

    private String code;
    private String message;

    PromoQRCodeResCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
