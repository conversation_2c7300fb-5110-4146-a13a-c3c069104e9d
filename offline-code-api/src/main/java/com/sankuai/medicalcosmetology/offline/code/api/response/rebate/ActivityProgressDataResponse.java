package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动返利进度信息模型
 */
@Data
public class ActivityProgressDataResponse implements Serializable {


    private static final long serialVersionUID = -5209664603162728833L;
    /**
     * 进度名称
     */
    private String name;

    /**
     * 进度值
     */
    private String amount;

}
