package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/10/9
 * @Description:促评活动
 */
@Data
public class ReviewActivityDTO implements Serializable {

    private Integer reviewActivityType;

    private String shopActivityDescription;

    private String userActivityDescription;

    private String userEncourageContent;

    private String paymentLine;

    private String jumpUrl;

    private Integer prizeAmount;

    private BigDecimal minOrderAmount;

    private Integer operateType;

}
