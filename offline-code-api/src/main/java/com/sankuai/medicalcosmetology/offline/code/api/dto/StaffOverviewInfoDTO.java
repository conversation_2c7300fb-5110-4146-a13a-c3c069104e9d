package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StaffOverviewInfoDTO implements Serializable {
    private Long scanUserCnt;//扫码人数
    private Long reviewCnt;//扫码评论数
    private BigDecimal qrActualPayAmt;//售卖订单实付金额
    private BigDecimal qrActualConsumeAmt;//核销订单实付金额
    private Long qrOrderCnt;//售卖订单量
    private Long qrConsumeOrderCnt;//核销订单量
    private Long transTechNumber;//动销职人数
}
