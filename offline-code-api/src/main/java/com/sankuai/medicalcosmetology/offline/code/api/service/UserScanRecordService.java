package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.UserScanRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.UserScanRecordQueryDTO;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/13
 * @Description:
 */
public interface UserScanRecordService {

    RemoteResponse<Boolean> addUserScanRecord(UserScanRecordDTO userScanRecordDTO);

    RemoteResponse<Boolean> addUserSubscribeLog(UserScanRecordDTO userScanRecordDTO);

    RemoteResponse<UserScanRecordDTO> findUserScanRecord(UserScanRecordQueryDTO userScanRecordQueryDTO);

}
