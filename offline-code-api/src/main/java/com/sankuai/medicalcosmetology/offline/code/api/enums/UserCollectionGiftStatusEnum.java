package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/16
 * @Description: 用户收藏礼状态枚举
 */
@Getter
public enum UserCollectionGiftStatusEnum {

    UN_RECEIVE(0, "未领取"),
    RECEIVE_UN_USE(1, "已领取未使用"),
    RECEIVE_USE(2, "已领取已使用");

    public final int code;
    public final String desc;

    UserCollectionGiftStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UserCollectionGiftStatusEnum getByCode(Integer code) {
        for (UserCollectionGiftStatusEnum statusEnum : UserCollectionGiftStatusEnum.values()) {
            if (statusEnum.code == code) {
                return statusEnum;
            }
        }
        return null;
    }

    public static boolean validateCode(Integer code) {
        return getByCode(code) != null;
    }
}
