package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/7
 * @Description:
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ZdcTagIdEnum {

    AXINXUE(1, 21573L, "安心学");

    private Integer code;
    private Long tagId;
    private String desc;

    public static ZdcTagIdEnum getByCode(Integer code) {
        for (ZdcTagIdEnum value : ZdcTagIdEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
