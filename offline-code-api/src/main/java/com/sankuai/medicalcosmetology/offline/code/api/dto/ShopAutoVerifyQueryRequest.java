package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/6/13
 **/
@Data
@ToString
public class ShopAutoVerifyQueryRequest implements Serializable {

    /**
     * 平台 {@link  com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum}
     */
    private int platform;

    private long shopId;

    /**
     * 场景
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.ShopAutoVerifySceneEnum
     */
    private String scene;
}
