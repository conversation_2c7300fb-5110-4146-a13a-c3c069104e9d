package com.sankuai.medicalcosmetology.offline.code.api.request.operation;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/4
 * @Description: 发券活动查询请求
 */
@Data
public class OperationCouponRequest implements Serializable {

    /**
     * 点评门店id
     */
    private Long dpShopId;

    /**
     * 扫码客户端类型
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType
     */
    private Integer qrClientType;

    /**
     * 码类型
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType
     */
    private Integer codeType;
}
