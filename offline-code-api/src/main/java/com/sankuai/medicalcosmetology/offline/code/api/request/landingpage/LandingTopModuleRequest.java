package com.sankuai.medicalcosmetology.offline.code.api.request.landingpage;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/21
 * @Description: 落地页顶部模块request
 */
@Data
public class LandingTopModuleRequest extends LandingBaseRequest implements Serializable {

    /**
     * 码类型
     * @see PromoCodeType
     */
    private Integer codeType;

    /**
     * 手艺人id
     */
    private Long staffCodeId;

    /**
     * 标识map的json格式
     */
    private String flagMapStr;

    /**
     * 二维码key
     */
    private String codeKey;
}
