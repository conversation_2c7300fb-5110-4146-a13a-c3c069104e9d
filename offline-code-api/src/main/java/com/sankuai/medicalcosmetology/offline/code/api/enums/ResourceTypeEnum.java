package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/14
 * @Description:资源类型
 */
@Getter
public enum ResourceTypeEnum {

    COUPON(1, "发券活动配置"),
    PROMO_CODE(2, "优惠码落地页顶图"),
    POST_PURCHASE_COUPON(3, "购后发券活动配置");


    private final int code;

    private final String desc;

    ResourceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
