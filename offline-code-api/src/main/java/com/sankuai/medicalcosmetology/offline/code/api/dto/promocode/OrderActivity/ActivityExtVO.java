package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import com.dianping.gmkt.event.api.promoqrcode.dto.orderactivitymanage.ReviewTipDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class ActivityExtVO implements Serializable {
    private static final long serialVersionUID = -3371536072908101691L;

    /**
     * 活动详情介绍URL
     */
    private String activityDescUrl;

    private List<ReviewTipDTO> tipContents;

    /**
     * 商户活动介绍
     */
    private String shopActivityDesc;

    /**
     * 商户活动规则介绍，评价激励活动使用到
     */
    private String shopRuleDesc;
}
