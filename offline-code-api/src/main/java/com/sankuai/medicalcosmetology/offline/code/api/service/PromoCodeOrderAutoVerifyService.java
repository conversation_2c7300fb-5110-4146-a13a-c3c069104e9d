package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ShopAutoVerifyQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.AutoVerifyQueryRequest;

/**
 * 功能描述:优惠码订单自动核销判断服务
 *
 * <AUTHOR>
 * @date 2024/11/6
 **/
public interface PromoCodeOrderAutoVerifyService {

    /**
     * 判断订单是否自动核销（订单维度，卡控了商品类型，如团购预付不支持自动核销）
     * for 交易
     * 目前仅支持职人改价，医疗下单码后续需要迁移至该接口
     * @param request
     * @return
     */
    RemoteResponse<Boolean> isOrderAutoVerify(AutoVerifyQueryRequest request);

    /**
     * 判断门店是否开启自动核销（门店维度，没有卡控商品）
     * for 用增内部
     * @param request
     * @return
     */
    RemoteResponse<Boolean> isShopAutoVerify(ShopAutoVerifyQueryRequest request);
}
