package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

@Getter
public enum PromoCodeYBShowTypeEnum {
    UNKNOWN(0,"未知"),
    SHOW_TEXT(1,"展示文本文案");

    private int code;
    private String desc;

    PromoCodeYBShowTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PromoCodeYBShowTypeEnum fromCode(int code) {
        for (PromoCodeYBShowTypeEnum showTypeEnum : PromoCodeYBShowTypeEnum.values()) {
            if (showTypeEnum.getCode() == code) {
                return showTypeEnum;
            }
        }
        return UNKNOWN;
    }
}
