package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.EmptyCodeInfoDTO;

/**
 * @Author: fuchangming
 * @Date: 2024/4/7
 * @Description: 优惠码通用网关服务
 */
public interface PromoCodeCommonService {

    /**
     * 生成空码
     *
     * @param needLogo 是否需要logo
     * @param needPadding 是否需要填充
     * @return 空码信息
     */
    RemoteResponse<EmptyCodeInfoDTO> generateEmptyCode(Boolean needLogo, Boolean needPadding, Boolean h5EmptyCode);

    RemoteResponse<EmptyCodeInfoDTO> generateEmptyCode(Boolean needLogo, Boolean needPadding);

    RemoteResponse<EmptyCodeInfoDTO> generateEmptyCode(Integer bizType, Boolean needLogo, Boolean needPadding);
}
