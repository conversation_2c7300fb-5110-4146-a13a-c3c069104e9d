package com.sankuai.medicalcosmetology.offline.code.api.enums.fileload;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/9/20
 * @Description:文件上传场景枚举
 */
@Getter
public enum FileLoadSceneEnum {

    MATERIAL_EXPRESS(1, "商户物料管理-上传-快递单号"),
    MATERIAL_STATS(2, "商户物料管理-上传-优惠码报表"),
    MATERIAL_PROMOCODE_EXPORT(3,"商户物料管理-导出-优惠码报表"),
    MATERIAL_EMPTYCODE_EXPORT(4,"商户物料管理-导出-空码"),
    MATERIAL_AOI_EMPTYCODE_EXPORT(5,"商户物料管理-导出-AOI空码"),
    MATERIAL_EMPTYCODE_BIND_STATUS(6,"商户物料管理-上传空码key并导出空码绑定状态-空码"),
    MATERIAL_EMPTYCODE_EXPORT_ZIP(7,"商户物料管理-导出-空码ZIP"),
    MATERIAL_MEDICAL_EMPTYCODE_IMAGE_EXPORT(8,"商户物料管理-导出-医疗空码图片"),
    MATERIAL_MEDICAL_EMPTYCODE_LINK_EXPORT(9,"商户物料管理-导出-医疗空码链接");

    private final Integer type;

    private final String desc;

    FileLoadSceneEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static boolean doesSceneTypeExist(Integer type) {
        return Arrays.stream(FileLoadSceneEnum.values()).anyMatch(e -> type != null && e.getType() == type);
    }

    public static FileLoadSceneEnum getByType(Integer type) {
        return Arrays.stream(FileLoadSceneEnum.values()).filter(e -> type != null && e.getType() == type).findFirst().orElse(null);
    }
}
