package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024/5/30
 * @Description: 报名变更状态
 */
@Getter
public enum RegistrationChangeStatus {

    SUCCESS(1, "success"),
    FAILURE(0, "failure");

    public int code;
    public String desc;

    private RegistrationChangeStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
