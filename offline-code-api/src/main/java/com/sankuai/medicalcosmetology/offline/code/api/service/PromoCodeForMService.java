package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.common.DictDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.BusinessDepartmentConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8
 * @Description:优惠码M端对外服务
 */
public interface PromoCodeForMService {

    /**
     * 根据场景查询业务部门列表(scene为空返回全部)
     *
     * @param scene 场景标识，用于区分不同的业务场景
     * @return RemoteResponse<List<DictDTO>> 返回远程响应，包含业务部门列表，每个部门以DictDTO形式表示
     */
    RemoteResponse<List<BusinessDepartmentConfigDTO>> queryBusinessDepartmentInfoByScene(Integer scene);

    /**
     * 根据业务部门编码查询业务部门配置信息
     *
     * @param code 业务部门编码
     * @return RemoteResponse<BusinessDepartmentConfigDTO> 返回远程响应，包含业务部门配置信息
     */
    RemoteResponse<BusinessDepartmentConfigDTO> queryBusinessDepartmentInfoByCode(Integer code);

    /**
     * 根据事业部ID查询业务部门配置信息
     *
     * @param buId 事业部ID
     * @return RemoteResponse<BusinessDepartmentConfigDTO> 返回远程响应，包含业务部门配置信息
     */
    RemoteResponse<BusinessDepartmentConfigDTO> queryBusinessDepartmentInfoByBuId(Integer buId);

}
