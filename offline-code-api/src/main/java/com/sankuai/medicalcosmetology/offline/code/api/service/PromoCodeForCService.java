package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.BrandCodeNearestPoiDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PostpurchaseCouponDetailRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PostpurchaseCouponRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.QueryCouponResultInfo;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ScanCouponInfo;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ShelfLoadConfig;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.*;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.OperationCouponRequest;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/19
 * @Description: 优惠码C端rpc服务
 */
public interface PromoCodeForCService {

    /**
     * 根据经纬度查询距离最近门店
     * 用户首次进落地页时需要调用
     * 
     * @param request
     * @return
     */
    RemoteResponse<BrandCodeNearestPoiDTO> queryNearestBrandPoi(BrandCodePoiQueryRequest request);

    /**
     * 查询优惠码最终落地页地址
     * 
     * @param request
     * @return
     */
    RemoteResponse<String> queryPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request);

    RemoteResponse<PromoCodeLandingUrlDTO> queryAllPlatformPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request);


    RemoteResponse<PromoCodeLandingUrlDTO> queryAllPlatformPromoCodeLandingPageUrlBySecret(QueryAllLandingPageUrlRequest request);

    /**
     * 落地页置顶团单合法性校验
     * 已废弃，不要调用！
     * @param poiId
     * @param goodsId
     * @param codeType
     * @return
     */
    @Deprecated
    RemoteResponse<Boolean> checkTopGoodsIdValid(Long poiId, Long goodsId, Integer codeType);


    RemoteResponse<ShelfLoadConfig> queryShopShelfLandConfig(LandingInfoRequest request);

    /**
     * 查询门店的发券活动配置
     * @param request
     * @return 发券活动信息
     */
    RemoteResponse<OperationInfoDTO> queryOperationConfig(OperationCouponRequest request);

    /**
     * 根据操作ID查询操作配置信息
     *
     * @param operationId 操作ID
     * @return 包含操作信息的远程响应
     */
    RemoteResponse<OperationInfoDTO> queryOperationConfigById(Long operationId);

    /**
     * 查询用户购后券信息
     * @param request
     * @return
     */
    RemoteResponse<QueryCouponResultInfo> queryUserPostPurchaseCoupon(UserPostPurchaseCouponQueryRequest request);
}
