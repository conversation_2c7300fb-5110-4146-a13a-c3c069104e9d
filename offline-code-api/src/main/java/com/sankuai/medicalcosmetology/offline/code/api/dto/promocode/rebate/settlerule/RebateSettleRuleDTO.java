package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateOrderPriceProportionRuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebatePaymentConfigDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleRuleFactorTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleRuleTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 返利订单结算规则DTO
 */
@Data
public class RebateSettleRuleDTO implements Serializable {

    private static final long serialVersionUID = 4751827803033262419L;

    /**
     * 返利订单结算规则类型
     *
     * @see RebateSettleRuleTypeEnum
     */
    private Integer type;

    /**
     * 返利订单结算因子类型
     *
     * @see RebateSettleRuleFactorTypeEnum
     */
    private Integer factorType;

    /**
     * 返利订单结算条件
     */
    private List<RebateSettleConditionDTO> condition;

    /**
     * 普通返利规则
     */
    private RebateSettleNormalRuleDTO normalRule;

    /**
     * 阶梯返利规则配置
     */
    private RebateSettleStepRuleDTO stepRule;

    /**
     * 返利活动提现打款配置
     */
    private RebatePaymentConfigDTO paymentConfig;

    /**
     * 规则文案
     */
    private String ruleTxt;

    /**
     * 新用户数阶梯规则配置
     */
    private RebateSettleCustomerStepRuleDTO newCustomerStepRule;

    /**
     * 老用户数阶梯规则配置
     */
    private RebateSettleCustomerStepRuleDTO oldCustomerStepRule;

    /**
     * 订单金额返利规则
     */
    private RebateOrderPriceProportionRuleDTO priceProportionRule;

    /**
     * 订单金额阶梯规则
     */
    private RebateSettleOrderPriceStepRuleDTO orderPriceStepRule;

    /**
     * 返利订单结算限制规则
     */
    private RebateSettleLimitRuleDTO rebateSettleLimitRule;

    /**
     * 每日首单返利金额
     */
    private Long dailyFirstOrderRebateAmount;

    /**
     * 商家补贴返利规则
     */
    private RebateSettleSellerSubsidyProportionRuleDTO sellerSubsidyProportionRule;

}
