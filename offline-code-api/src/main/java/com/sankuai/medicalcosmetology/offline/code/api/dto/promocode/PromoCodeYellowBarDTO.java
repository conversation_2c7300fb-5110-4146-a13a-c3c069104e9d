package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PromoCodeYellowBarDTO implements Serializable {
    private static final long serialVersionUID = 2879034526892347841L;

    /**
     * 展示类型
     * see {@link com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeYBShowTypeEnum}
     */
    private int showType;

    //展示文案
    private String content;
}
