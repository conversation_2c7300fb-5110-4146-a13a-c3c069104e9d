package com.sankuai.medicalcosmetology.offline.code.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 审批URL查询请求
 */
@Data
public class ApprovalUrlQueryRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 审批类型：x1 或 finance
     */
    private String type;
    
    /**
     * 业务场景标识
     */
    private String sceneId;
    
    /**
     * 活动ID
     */
    private String activityId;
} 