package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 返利活动参加条件城市类型枚举
 */
public enum RebateActivityJoinConditionCityTypeEnum {

    DEFAULT_CITY(0, "默认不区分渠道、直营城市"),
    CHANNEL_CITY(1, "渠道"),
    DIRECTLY_CITY(2, "直营"),
    WHITE_LIST(3, "白名单城市");

    public int code;
    public String desc;

    RebateActivityJoinConditionCityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RebateActivityJoinConditionCityTypeEnum getByCode(int code) {
        for (RebateActivityJoinConditionCityTypeEnum cityType : RebateActivityJoinConditionCityTypeEnum.values()) {
            if (cityType.code == code) {
                return cityType;
            }
        }
        return null;
    }
}
