package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule;

import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleConditionKeyEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleConditionKeyTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleConditionValueTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 返利订单条件DTO
 */
@Data
public class RebateSettleConditionDTO implements Serializable {
    private static final long serialVersionUID = -4728611887464591288L;

    /**
     * 条件key：
     * @see RebateSettleConditionKeyEnum
     */
    private Integer key;
    /**
     * 条件类型：
     * @see RebateSettleConditionKeyTypeEnum
     */
    private Integer keyType;
    /**
     * 条件值
     */
    private String value;
    /**
     * 条件值类型：
     * @see RebateSettleConditionValueTypeEnum
     */
    private Integer valueType;
}
