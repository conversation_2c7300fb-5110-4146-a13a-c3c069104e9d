package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/22
 * @Description: 通用浮层
 */
@Data
public class CommonFloatingLayer implements Serializable {

    /**
     * 文案内容
     */
    private List<TextContent> textContentList;

    /**
     * 弹框内容
     */
    private List<PopContent> popContentList;

    /**
     * 按钮内容
     */
    private List<ButtonContent> buttonContentList;

    /**
     * 额外信息
     */
    private String extMapStr;
}
