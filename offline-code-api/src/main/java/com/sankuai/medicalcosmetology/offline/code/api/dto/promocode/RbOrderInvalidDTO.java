package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode;

import com.sankuai.medicalcosmetology.offline.code.api.enums.NotRebateRecordTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RbOrderInvalidDTO implements Serializable {

    private static final long serialVersionUID = -346578981234127681L;

    /**
     *   字段: id
     *   说明: 主键id
     */
    private Long id;

    /**
     *   字段: unified_order_id
     *   说明: 统一订单id
     */
    private String unifiedOrderId;

    /**
     *   字段: coupon_id
     *   说明: 券id
     */
    private String couponId;

    /**
     *   字段: record_type
     *   说明: 记录类型，1-门店，2-职人
     */
    private Integer recordType;

    /**
     *   字段: activity_record_id
     *   说明: 参加活动ID
     */
    private Long activityRecordId;

    /**
     *   字段: invalid_type
     *   说明: 无效类型
     *   @see com.sankuai.medicalcosmetology.offline.code.api.enums.NotRebateRecordTypeEnum
     */
    private Integer invalidType;

    /**
     *   字段: invalid_reason
     *   说明: 无效原因
     */
    private String invalidReason;

}
