package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 同步from Campaign-web项目
 * <AUTHOR>
 * @date 2025/1/20
 */
@Slf4j
@Getter
public enum QrCodeScanSceneEnum {

    UNKNOWN("unknown", "未知场景"),
    COIN("coin", "兑币机场景"),
    STAFF_CODE("staff", "员工码场景"),
    STAFF_SIGN_UP_CODE("signup", "员工码报名场景"),
    MEDICINE_SHOP_PROMO_CODE("promo", "医疗门店下单码"),
    BRAND_CODE("brand", "品牌码场景"),
    MT_FENFEN_MEDICINE_DISTRIBUTOR_WITHDRAW("fen_medicine_dis_draw", "美团纷纷链路，医疗分销场景"),
    MT_FENFEN_MEDICINE_GROUP_WITHDRAW("fen_medicine_group_draw", "美团纷纷链路，医疗分销场景"),
    PROMO_SHOP_CODE("promo_shop", "门店优惠码"),
    PROMO_GOODS_CODE("promo_goods", "商品码"),
    BRAND_VERIFY_CODE("brand_verify", "品牌验真码"),
    STAFF_BARGAIN_CODE("staff_bargain", "职人一客一价码"),
    OFFLINE_EMPTY_CODE("offline_empty", "线下码空码"),
    GROUND_PROMOTION_PART_TIME("ground_promotion_parttime", "兼职地推"),
    H5_STAFF_CODE("h5_staff", "h5员工码场景"),
    H5_PROMO_SHOP_CODE("h5_promo_shop", "h5门店优惠码"),
    H5_OFFLINE_EMPTY_CODE("h5_offline_empty", "h5线下码空码"),
    H5_AOI_EMPTY_CODE("h5_aoi_empty", "h5-AOI空码")

    ;

    private final String code;

    private final String desc;

    QrCodeScanSceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static QrCodeScanSceneEnum getByCode(String code) {
        for (QrCodeScanSceneEnum sceneEnum : QrCodeScanSceneEnum.values()) {
            if (sceneEnum.getCode().equals(code)) {
                return sceneEnum;
            }
        }
        return UNKNOWN;
    }
}
