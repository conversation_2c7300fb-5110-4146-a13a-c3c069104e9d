package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleRuleDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 返利活动规则
 */
@Data
public class RebateActivityRuleDTO implements Serializable {

    private static final long serialVersionUID = -2063399396023223286L;

    /**
     * 活动id
     */
    private Long activityConfigId;

    /**
     * 业务线
     */
    private String bizLine;

    /**
     * 返利订单结算规则
     */
    private RebateSettleRuleDTO rule;
}
