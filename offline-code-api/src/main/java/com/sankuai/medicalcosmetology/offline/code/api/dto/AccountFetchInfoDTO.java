package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description : 订单提现信息查询表
 * @date : 2024/12/16
 */
@Data
public class AccountFetchInfoDTO implements Serializable {
    /**
     * 总提现金额
     */
    private String totalAmount;
    /**
     * 提现金额
     */
    private String privateTotalAmount;
    /**
     * 对公提现金额
     */
    private String businessTotalAmount;
    /**
     * 提现时间
     */
    private Long fetchTime;
    /**
     * 提现批次outBizId
     */
    private String fetchOutBizId;

}
