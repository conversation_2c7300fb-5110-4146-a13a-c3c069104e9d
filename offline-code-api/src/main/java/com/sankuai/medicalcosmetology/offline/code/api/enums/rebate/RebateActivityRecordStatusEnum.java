package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/1/22 11:44 上午
 */
public enum RebateActivityRecordStatusEnum {

    IN_VALID((byte)0, "非法"),
    VALID((byte)1, "合法"),
    UN_BIND((byte)2, "解绑状态:查询，提现");

    public Byte code;
    public String desc;

    RebateActivityRecordStatusEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RebateActivityRecordStatusEnum getByCode(Byte code) {
        if (code == null) {
            return null;
        }
        for (RebateActivityRecordStatusEnum statusEnum : values()) {
            if (statusEnum.code.equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static boolean validate(Byte code) {
        return getByCode(code) != null;
    }

    public static boolean canWithDraw(Byte code) {
        return getByCode(code) != IN_VALID;
    }
}
