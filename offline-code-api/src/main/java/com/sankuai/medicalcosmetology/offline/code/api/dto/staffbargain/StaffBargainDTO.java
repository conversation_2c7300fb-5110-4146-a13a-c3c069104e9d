package com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain;

import com.sankuai.medicalcosmetology.offline.code.api.enums.StaffBargainBizTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/6 11:44
 */
@Data
public class StaffBargainDTO implements Serializable {
    /**
     * 职人一客一价订单id
     */
    private Long staffBargainId;

    /**
     * 职人id
     */
    private Long staffId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品类型
     *
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.ProductTypeEnum
     */
    private Integer productType;

    /**
     * 职人所属点评门店id
     */
    private Long dpShopId;

    /**
     * 最低限制金额
     */
    private BigDecimal minBargainPrice;

    /**
     * 最高限制金额
     */
    private BigDecimal maxBargainPrice;

    /**
     * 订单改价价格
     */
    private BigDecimal bargainPrice;

    /**
     * 服务内容图片
     */
    private List<String> servicePicList;

    /**
     * 服务内容文字
     */
    private String serviceText;

    private StaffBargainBizTypeEnum bizType;

    private Integer expiredTime;

    /**
     * 是否限制扫码次数  true：限制，且只能扫码一次  false：不限制
     */
    private Boolean limitScanTimes;
}
