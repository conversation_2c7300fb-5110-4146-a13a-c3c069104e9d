package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpEncrypt;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description: 落地页基础信息
 */
@Data
public class LandingPageBasicInfo implements Serializable {

    /**
     * 点评门店id
     */
    @MdpEncrypt(target = "dpShopIdEncrypt")
    private Long dpShopId;

    /**
     * 点评门店id加密字段
     */
    private String dpShopIdEncrypt;

    /**
     * 美团门店id
     */
    @MdpEncrypt(target = "mtShopIdEncrypt")
    private Long mtShopId;

    /**
     * 美团门店id加密字段
     */
    private String mtShopIdEncrypt;

    /**
     * shopuuid
     */
    @MdpEncrypt(target = "shopUuidEncrypt")
    private String shopUuid;

    /**
     * shopuuid加密字段
     */
    private String shopUuidEncrypt;

    /**
     * 手艺人id
     */
    private Integer techId;
}
