package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.sankuai.medicalcosmetology.offline.code.api.response.PromoQRCodeResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity.AdvertiseActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity.AdvertiseActivityPageRequest;
import com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity.AdvertiseActivityPageResponse;

public interface PromoQRCodeAdvertiseActivityMService {
    PromoQRCodeResponse<AdvertiseActivityPageResponse>
            queryAdvertiseActivityListWithCondition(AdvertiseActivityPageRequest request);

    PromoQRCodeResponse<Boolean> createPromoCodeAdvertiseActivity(AdvertiseActivityDTO activityDTO);

    PromoQRCodeResponse<AdvertiseActivityDTO> queryByAdvertiseActivityId(Long activityId);

    PromoQRCodeResponse<Boolean> updatePromoCodeAdvertiseActivity(AdvertiseActivityDTO activityDTO);

    PromoQRCodeResponse<Boolean> updatePromoCodeAdvertiseActivityStatus(Long activityId, Integer operateType);
}
