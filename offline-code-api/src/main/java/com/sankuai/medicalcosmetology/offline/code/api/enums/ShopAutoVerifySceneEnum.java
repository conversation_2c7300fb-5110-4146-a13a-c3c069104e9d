package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/6/11
 **/
@Getter
public enum ShopAutoVerifySceneEnum  implements BaseEnum<String> {

    UNKNOWN("unknown", "未知"),
    MEDICAL_PLACE_ORDER_CODE("MedicalPlaceOrderCode", "医疗下单码"),
    STAFF_BARGAIN("StaffBargain", "职人改价");

    private final String code;

    private final String desc;

    ShopAutoVerifySceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShopAutoVerifySceneEnum fromCode(String code) {
        for (ShopAutoVerifySceneEnum enumValue : ShopAutoVerifySceneEnum.values()) {
            if (enumValue.getCode().equalsIgnoreCase(code)) return enumValue;
        }
        return UNKNOWN;
    }
}
