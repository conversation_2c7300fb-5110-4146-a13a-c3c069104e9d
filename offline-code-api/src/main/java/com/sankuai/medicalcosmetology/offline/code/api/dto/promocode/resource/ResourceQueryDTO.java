package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource;

import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/14
 * @Description:发券活动配置信息
 */
@Data
public class ResourceQueryDTO implements Serializable {

    /**
     * 点评门店id
     */
    private Long dpShopId;

    /**
     * 扫码客户端类型
     * @see QRClientType
     */
    private Integer qrClientType;

}
