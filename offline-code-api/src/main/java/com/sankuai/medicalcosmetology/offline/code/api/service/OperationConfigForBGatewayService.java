package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.PaginationRemoteResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.RebateActivityStatementQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.RebateActivityStatementTraceQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.ApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.CancelApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.RebateWithdrawRequest;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.*;

import java.util.List;

/**
 * 活动-B端网关服务
 */
public interface OperationConfigForBGatewayService {

    /**
     * B端活动查询
     *
     * @param dpShopId     点评店铺id
     * @param activityType 活动类型
     * @param status       活动状态
     * @return
     */
    RemoteResponse<List<ActivityModuleResponse>> queryActivityList(Long dpShopId, int activityType, int status);

    /**
     * 查询活动报名门店信息
     *
     * @param activityViewId 加密活动id
     * @param activityType   活动类型
     * @return 活动报名门店信息
     */
    RemoteResponse<ApplyShopInfoResponse> queryApplyShopInfo(String activityViewId, Integer activityType);

    /**
     * 校验门店是否可报名
     *
     * @param applyActivityRequest 报名请求
     * @return 校验结果
     */
    RemoteResponse<ApplyShopCheckResultResponse> checkApplyShop(ApplyActivityRequest applyActivityRequest);

    /**
     * 报名活动
     *
     * @param applyActivityRequest 报名请求
     * @return 报名结果
     */
    RemoteResponse<List<Long>> batchApplyActivity(ApplyActivityRequest applyActivityRequest);

    /**
     * 查询活动详情
     *
     * @param activityConfigViewId 活动加密id
     * @return 活动详情
     */
    RemoteResponse<RebateActivityDetailResponse> queryActivityDetail(Integer activityType, String activityConfigViewId);

    /**
     * 分页查询账单
     *
     * @param request 查询参数
     * @return 账单列表
     */
    PaginationRemoteResponse<RebateActivityStatementResponse> pageQueryStatement(RebateActivityStatementQueryRequest request);

    /**
     * 分页查询账单流水
     *
     * @param request 查询参数
     * @return 账单流水列表
     */
    PaginationRemoteResponse<RebateActivityStatementTraceResponse> pageQueryStatementTrace(RebateActivityStatementTraceQueryRequest request);

    /**
     * 单个活动提现
     *
     * @return 提现结果
     */
    RemoteResponse<RebateWithDrawResponse> withdrawByActivity(RebateWithdrawRequest request);

    /**
     * 取消报名
     *
     * @param request 取消报名请求
     * @return 取消报名结果
     */
    RemoteResponse<Void> cancelApply(CancelApplyActivityRequest request);

}
