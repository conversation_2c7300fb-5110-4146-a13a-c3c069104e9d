package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityRuleDTO;

/**
 * 返利活动服务
 */
public interface RebateActivityService {

    /**
     * @param dpShopId     点评门店id
     * @param activityType 返利活动类型 {@link com.sankuai.medicalcosmetology.offline.code.api.enums.ActivityTypeEnum}
     */
    RemoteResponse<RebateActivityRecordDTO> queryOnlineRebateActivity(Long dpShopId, Integer activityType);

    /**
     * 根据返利活动报名记录ID查询返利活动规则
     *
     * @param recordId 返利活动报名记录ID
     * @return 返利活动规则信息
     */
    RemoteResponse<RebateActivityRuleDTO> queryRebateActivityRuleByRecordId(Long recordId);

    /**
     * 根据记录ID查询返利活动报名记录
     *
     * @param recordId 返利活动记录ID
     * @return 返利活动报名记录信息
     */
    RemoteResponse<RebateActivityRecordDTO> queryActivityRecordById(Long recordId);
}
