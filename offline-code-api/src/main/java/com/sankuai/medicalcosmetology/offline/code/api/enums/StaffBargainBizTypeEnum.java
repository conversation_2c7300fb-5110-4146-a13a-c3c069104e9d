package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/6/11
 * @Description: 职人一客一价业务类型枚举
 */
@Getter
public enum StaffBargainBizTypeEnum {

    MULTIPLE_ORDERS_ONE_USER(1, "多订单，单用户领取", "staff_bargain_code"),  // 对应团购预付类型，渠道为一客一价渠道
    SINGLE_ORDER_ONE_USER(2, "单订单，单用户领取", "discount_code"), // 对应普通团购类型，渠道为原职人码渠道
    PRE_ORDER_ONE_USER(3, "预订单，单用户领取", "pre_staff_bargain_code"),//对应全款预付、泛商品预付
    ;

    public final int code;
    public final String desc;
    // 订单所属分销渠道
    public final String channel;
    StaffBargainBizTypeEnum(int code, String desc, String channel) {
        this.code = code;
        this.desc = desc;
        this.channel = channel;
    }

    public static StaffBargainBizTypeEnum getByCode(Integer code) {
        for (StaffBargainBizTypeEnum bizTypeEnum : StaffBargainBizTypeEnum.values()) {
            if (bizTypeEnum.code == code) {
                return bizTypeEnum;
            }
        }
        return null;
    }

    public static boolean validateCode(Integer code) {
        return getByCode(code) != null;
    }
}
