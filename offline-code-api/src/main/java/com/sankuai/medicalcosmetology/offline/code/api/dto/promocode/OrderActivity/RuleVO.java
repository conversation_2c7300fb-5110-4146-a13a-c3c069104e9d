package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import lombok.Data;

import java.io.Serializable;


@Data
public class RuleVO implements Serializable {
    private static final long serialVersionUID = -4131274014373480537L;

    private FreeCommissionRuleVO freeCommissionRule;
    private RebateSettleRuleVO rebateRule;

    /**
     * 评论激励奖励金额
     */
    private Integer reviewAwardAmount;
    /**
     * 评论激励奖励门槛金额，单位元
     */
    private Integer orderThresholdPrice;
    /**
     * 评论激励打款业务线
     */
    private String reviewPaymentBizLine;
}
