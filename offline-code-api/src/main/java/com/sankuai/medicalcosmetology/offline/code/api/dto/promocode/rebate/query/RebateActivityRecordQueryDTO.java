package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RebateActivityRecordQueryDTO implements Serializable {

    private static final long serialVersionUID = -1378839282504295342L;

    /**
     * 活动id
     */
    private List<Long> activityConfigIdList;

    /**
     * 点评店铺id
     */
    private List<Long> dpShopIdList;

    /**
     * 点评账号id
     */
    private Long dpShopAccountId;

    /**
     * 状态0:删除 1:有效 2:已解除
     */
    private Byte status;
}
