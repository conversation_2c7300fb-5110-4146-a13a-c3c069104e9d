package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 神券返利膨胀类型枚举
 */
public enum GodCouponRebateExpansionTypeEnum {

    FIRST_ORDER_TYING_SALE(1, "搭售且使用付费膨胀券"),
    EVERY_ORDER_NON_TYING_SALE(2, "非搭售但使用付费膨胀券"),
    EVERY_ORDER_PAID_COUPON(3, "使用付费膨胀券（含搭售和非搭售）"),
    EVERY_ORDER_ALL_COUPON(4, "使用膨胀券（含搭售和非搭售，免费和付费膨胀券）"),
    ;

    public final int code;
    public final String desc;

    GodCouponRebateExpansionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validQRBizType(int code) {
        for (GodCouponRebateExpansionTypeEnum type : GodCouponRebateExpansionTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static GodCouponRebateExpansionTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GodCouponRebateExpansionTypeEnum type : GodCouponRebateExpansionTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
