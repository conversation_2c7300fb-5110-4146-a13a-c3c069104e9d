package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;

import java.util.List;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/4/17
 **/
public interface PromoCodeActivityManageService {


    RemoteResponse<Boolean> uploadFloorData(Integer itemId, List<String> ids, String operator);

    RemoteResponse<Boolean> getBizCooperateInfoDTO(Long mtShopId, Long customerId);

    RemoteResponse<Boolean> testVoucherMq(String message);
}
