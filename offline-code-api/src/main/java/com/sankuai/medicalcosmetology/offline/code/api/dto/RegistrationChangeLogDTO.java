package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 *
 *   表名: registration_change_log
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RegistrationChangeLogDTO implements Serializable {

    private static final long serialVersionUID = 5854283122129812912L;
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: rigistration_entity_id
     *   说明: 报名主体id 门店id、职人id、手艺人id等
     */
    private Long rigistrationEntityId;

    /**
     *   字段: rigistration_entity_type
     *   说明: 报名主体类型 门店 职人 手艺人
     */
    private Integer rigistrationEntityType;

    /**
     *   字段: activity_type
     *   说明: 报名活动类型 参考offlinecode枚举-RegistrationActivityTypeEnum
     */
    private Integer activityType;

    /**
     *   字段: activity_id
     *   说明: 报名活动ID
     */
    private Long activityId;

    /**
     *   字段: change_type
     *   说明: 修改类型 参考offlinecode枚举-RegistrationChangeType 报名1 取消报名2
     */
    private Integer changeType;

    /**
     *   字段: change_status
     *   说明: 状态 RegistrationChangeStatus 1-成功 0-失败 
     */
    private Integer changeStatus;

    /**
     *   字段: change_entity_identify
     *   说明: 变更主体id 门店id、职人id、手艺人id、开店宝账号id
     */
    private String changeEntityIdentify;

    /**
     *   字段: change_entity_type
     *   说明: 变更主体类型 参考offlinecode枚举-ChangeEntityType 销售手机 手艺人手机好 商家账号等
     */
    private Integer changeEntityType;

    /**
     *   字段: ext_info
     *   说明: 额外信息
     */
    private String extInfo;

    /**
     *   字段: create_time
     */
    private Date createTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}