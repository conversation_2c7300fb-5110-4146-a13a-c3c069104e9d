package com.sankuai.medicalcosmetology.offline.code.api.request;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PostPurchaseCouponPageSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/10
 * @Description:
 */
@Data
public class UserPostPurchaseCouponQueryRequest implements Serializable {

    /**
     * 用户id，区分平台
     */
    private Long userId;

    /**
     * 平台
     * @see PlatformEnum
     */
    private Integer platform;

    /**
     * 页面来源
     * 1-端首页 2-团购频道页 3-订详页 4-核销页
     * @see PostPurchaseCouponPageSourceEnum
     */
    private Integer pageSource;

    /**
     * 请求客户端类型
     * 1-点评app，2-美团app，3-美团小程序
     * @see QRClientType
     */
    private Integer qrClientType;
}