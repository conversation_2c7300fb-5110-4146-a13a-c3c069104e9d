package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 返利订单结算因子类型
 */
public enum RebateSettleRuleFactorTypeEnum {
    ORDER_COUNT(1, "订单数"),
    USER_COUNT(2, "用户数"),
    ORDER_PRICE(3, "订单实付金额"),
    SELLER_SUBSIDY_PRICE(4, "商家补贴金额");

    public Integer code;
    public String desc;

    RebateSettleRuleFactorTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validRebateSettleRuleFactorType(int code) {
        for (RebateSettleRuleFactorTypeEnum factoryType : RebateSettleRuleFactorTypeEnum.values()) {
            if (factoryType.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateSettleRuleFactorTypeEnum getByCode(int code) {
        for (RebateSettleRuleFactorTypeEnum type : RebateSettleRuleFactorTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("error code for RebateSettleRuleFactorType.");
    }
}
