package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/6/11
 **/
public interface MedicalPlaceOrderCodeBGatewayService {

    /**
     * 是否展示自动核销模块
     * @param dpShopId 点评门店id
     */
    RemoteResponse<Boolean> showAutoVerifyTab(long dpShopId);

    /**
     * 判断门店是否开启自动核销
     * @param dpShopId 点评门店id
     */
    RemoteResponse<Boolean> isShopOrderAutoVerify(long dpShopId);

    /**
     * 开启自动核销
     * @param dpShopId 点评门店id
     * @return 设置后状态 {@link com.sankuai.medicalcosmetology.offline.code.api.enums.ShopAutoVerifySwitchEnum}
     */
    RemoteResponse<Boolean> openAutoVerify(long dpShopId);

    /**
     * 关闭自动核销
     * @param dpShopId 点评门店id
     * @return 设置后状态 {@link com.sankuai.medicalcosmetology.offline.code.api.enums.ShopAutoVerifySwitchEnum}
     */
    RemoteResponse<Boolean> closeAutoVerify(long dpShopId);

    /**
     * 同步当前门店的自动核销状态到账号其它管理门店
     * @param dpShopId 点评门店id
     * @return 同步是否成功
     */
    RemoteResponse<Boolean> syncAutoVerifyStatus(long dpShopId);
}
