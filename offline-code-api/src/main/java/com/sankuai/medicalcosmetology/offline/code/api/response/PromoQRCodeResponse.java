package com.sankuai.medicalcosmetology.offline.code.api.response;

import com.sankuai.medicalcosmetology.offline.code.api.dto.exceptions.PromoQRCodeException;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoQRCodeResCode;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:wangdingxiong
 * @date: 2020-04-14
 * @time: 16:24
 */
@Data
public class PromoQRCodeResponse<T> implements Serializable {

    /**
     * 返回代码
     * 
     * @see PromoQRCodeResCode
     */
    private String code;

    private String msg;

    private T data;

    public PromoQRCodeResponse() {}

    public PromoQRCodeResponse(String code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> PromoQRCodeResponse<T> success(T data) {
        return new PromoQRCodeResponse<T>(PromoQRCodeResCode.SUCCESS, data);
    }

    public static <T> PromoQRCodeResponse<T> fail(PromoQRCodeResCode resCode) {
        return new PromoQRCodeResponse<T>(resCode, null);
    }

    public static <T> PromoQRCodeResponse<T> fail(PromoQRCodeException e) {
        return new PromoQRCodeResponse<T>(e.getCode(), e.getMsg(), null);
    }

    public static <T> PromoQRCodeResponse<T> fail(String code, String msg, T data) {
        return new PromoQRCodeResponse<T>(code, msg, data);
    }

    public static <T> PromoQRCodeResponse<T> success() {
        return new PromoQRCodeResponse<T>(PromoQRCodeResCode.SUCCESS, null);
    }

    public PromoQRCodeResponse(PromoQRCodeResCode resCode) {
        this.code = resCode.getCode();
        this.msg = resCode.getMessage();
    }

    public PromoQRCodeResponse(PromoQRCodeResCode resCode, T data) {
        this.code = resCode.getCode();
        this.msg = resCode.getMessage();
        this.data = data;
    }

    public boolean isSuccess() {
        return this.code.equals(PromoQRCodeResCode.SUCCESS.getCode());
    }

}
