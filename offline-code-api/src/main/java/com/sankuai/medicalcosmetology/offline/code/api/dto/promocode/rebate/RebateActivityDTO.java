package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 返利活动DTO
 */
@Data
public class RebateActivityDTO implements Serializable {

    private static final long serialVersionUID = -8818753539155695965L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动配置ID
     */
    private Long operationId;

    /**
     * 返利活动类型
     * @see RebateActivityTypeEnum
     */
    private Byte rebateActivityType;

    /**
     * 返利活动类型描述
     */
    private String rebateActivityTypeDesc;

    /**
     * 活动规则描述
     */
    private String activityRuleDesc;

    /**
     * 返利提现打款配置
     */
    private String paymentConfig;

    /**
     * 活动参与条件
     */
    private RebateJoinConditionDTO condition;

    /**
     * 返利规则
     */
    private RuleDTO rule;

    /**
     * 财务审批链接
     */
    private String financeAuditUrl;

    /**
     * X1审批链接
     */
    private String x1AuditUrl;
}
