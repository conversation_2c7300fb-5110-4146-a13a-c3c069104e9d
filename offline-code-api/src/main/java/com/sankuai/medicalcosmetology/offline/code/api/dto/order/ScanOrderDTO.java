package com.sankuai.medicalcosmetology.offline.code.api.dto.order;

import com.sankuai.medicalcosmetology.offline.code.api.enums.DealTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/10
 * @Description:
 */
@Data
public class ScanOrderDTO implements Serializable {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 下单平台
     * @see PlatformEnum
     */
    private Integer platform;

    /**
     * 平台商品id
     * 其中团购，通常指美团团购id
     */
    private Long productId;

    /**
     * 业务商品id
     * 其中团购，通常指点评团购id
     */
    private Long bizProductId;

    /**
     * 下单门店id，区分平台
     */
    private Long orderShopId;

    /**
     * 商户前台一级类目id，区分平台
     */
    private Integer frontFirstCateId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * deal类型
     * 1-团购 2-泛商品 3-标品
     * @see DealTypeEnum
     */
    private Integer dealType;

    /**
     * 下单门店对应的buId
     */
    private Integer buId;
}
