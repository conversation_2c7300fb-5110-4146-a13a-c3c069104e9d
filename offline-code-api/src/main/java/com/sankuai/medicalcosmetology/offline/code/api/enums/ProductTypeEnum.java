package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * <AUTHOR>
 * @Date 2024/6/7 15:17
 */
public enum ProductTypeEnum {
    UNKNOWN(0, "未知"),
    TIME_CARD(1, "次卡"),
    TUAN_DEAL(2, "点评团单"),
    STANDARD_PRODUCT(3, "标品"),
    PAY_PER_CARD(4, "储值卡"),
    PREPAY(5, "预付"),
    BOOK(6, "预订"),
    MT_DEAL_GROUP(7, "美团团单"),
    MULTI(99, "混合类型");

    private final int code;
    private final String desc;

    private ProductTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductTypeEnum fromCode(int code) {
        ProductTypeEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ProductTypeEnum enumValue = var1[var3];
            if (enumValue.getCode() == code) {
                return enumValue;
            }
        }

        return UNKNOWN;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
