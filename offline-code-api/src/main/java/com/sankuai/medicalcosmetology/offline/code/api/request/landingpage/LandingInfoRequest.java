package com.sankuai.medicalcosmetology.offline.code.api.request.landingpage;

import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageScanSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description:
 */
@Data
public class LandingInfoRequest extends LandingBaseRequest implements Serializable {

    /**
     * 团单id
     */
    private Long goodsId;

    /**
     * 优惠码来源标识 品牌码时为customerId 职人码时为codeId
     */
    private String sourceIdentifier;

    /**
     * 码类型
     * @see PromoCodeType
     */
    private Integer codeType;

    /**
     * 码key
     */
    private String codeKey;

    /**
     * 扫码来源，默认为线下码
     * @see LandingPageScanSourceEnum
     */
    private Integer source = 0;
}
