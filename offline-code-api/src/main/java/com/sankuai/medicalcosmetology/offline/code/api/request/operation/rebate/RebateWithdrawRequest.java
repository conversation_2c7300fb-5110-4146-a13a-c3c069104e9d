package com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 提现请求
 */
@Data
public class RebateWithdrawRequest implements Serializable {

    private static final long serialVersionUID = -5196736668257299346L;

    /**
     * 返利活动配置加密id
     */
    private String activityConfigViewId;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum
     * 活动类型
     */
    private Integer activityType;
}
