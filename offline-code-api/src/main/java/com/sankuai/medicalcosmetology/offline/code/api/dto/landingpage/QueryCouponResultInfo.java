package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description: 券信息模块
 */
@Data
public class QueryCouponResultInfo implements Serializable {

    /**
     * 券包卡片
     */
    private CouponGroupCardDTO couponGroupCardDTO;

    /**
     * 券信息
     */
    private List<ScanCouponInfo> couponList;

    /**
     * 神券包id列表
     */
    private List<String> maigicCouponIdList;

    /**
     * 神券标签文案
     */
    private MagicMemberTagTextDTO magicMemberTagText;

    /**
     * 神券包名称灰度开关
     */
    private Boolean magicMemberPackageGray;

    /**
     * 神券命中实验
     */
    private Set<Integer> magicMemberExperimentIdSet;

    /**
     * 所有券中最近的过期时间
     */
    private Date latestExpireTime;
}
