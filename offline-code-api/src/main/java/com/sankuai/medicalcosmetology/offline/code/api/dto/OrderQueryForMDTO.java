package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderQueryForMDTO implements Serializable {
    private static final long serialVersionUID = 43256871384191021L;
    //订单id
    private String orderId;
    //点评门店id
    private Long dpShopId;
    //点评门店名称
    private String dpShopName;
    //返利金额
    private String rebateCent;
    //提现账户
    // 格式：对公（手机号）/对私（美团账号）
    private String fetchAccount;
    //订单含劵数量
    private String includeCoupon;
    //订单来源
    private String orderResource;
    //是否提现
    private String isFetch;
    //订单类型
    private int orderType;

    private Long techId;
    private String techName;

    private Long activityRecordId;
    private Integer settleType;


    // 格式：返利活动名称（返利活动Id)
    private Long activityId;

}
