package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageRequestDTO;
import lombok.Data;

import java.io.Serializable;


@Data
public class OrderActivityPageQueryVO extends PageRequestDTO implements  Serializable {
    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 活动状态
     */
    private Integer status;

    /**
     * 报名类型，默认0
     */
    private Integer signUpType = 0;

    /**
     * 一级类目ID
     */
    private Integer categoryId;

    /**
     * 二级类目ID
     */
    private Integer categoryId2;

    /**
     * 活动类型列表
     */
    private Integer activityTypes;

}
