package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/20
 * @Description: copy自PostpurchaseCouponDetailRecordDO，仅测试用，上线后需删除
 */
@Data
public class PostpurchaseCouponDetailRecordDTO implements Serializable {

    /**
     *   字段: user_id
     *   说明: 用户id，区分平台
     */
    private Long userId;

    /**
     *   字段: platform
     *   说明: 领券平台 1-点评 2-美团
     */
    private Integer platform;

    /**
     *   字段: order_id
     *   说明: 领券对应订单id
     */
    private String orderId;

    /**
     *   字段: unified_coupon_id
     *   说明: 券id
     */
    private String unifiedCouponId;

    /**
     *   字段: unified_coupon_group_id
     *   说明: 券批次id
     */
    private String unifiedCouponGroupId;

    /**
     *   字段: coupon_used
     *   说明: 券是否被使用 0-未被使用 1-被使用
     */
    private Integer couponUsed;

    /**
     *   字段: coupon_amount
     *   说明: 立减金额
     */
    private BigDecimal couponAmount;

    /**
     *   字段: coupon_limit_amount
     *   说明: 使用门槛
     */
    private BigDecimal couponLimitAmount;

    /**
     *   字段: coupon_title
     *   说明: 券标题
     */
    private String couponTitle;

    /**
     *   字段: coupon_desc
     *   说明: 券描述
     */
    private String couponDesc;

    /**
     *   字段: begin_use_time
     *   说明: 开始使用时间
     */
    private Date beginUseTime;

    /**
     *   字段: end_use_time
     *   说明: 结束使用时间
     */
    private Date endUseTime;

    /**
     *   字段: operation_config_id
     *   说明: 发券活动配置id
     */
    private Long operationConfigId;
}
