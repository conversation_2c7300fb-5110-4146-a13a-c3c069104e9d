package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

import lombok.Getter;

/**
 * 功能描述: 返利类型
 *
 * <AUTHOR>
 * @date 2023/3/22
 **/
@Getter
public enum RebateSettleTypeEnum {

    UNKNOWN(0, "未知"),
    PRIVACY(1, "对私账号，满天星"),
    BUSINESS(2, "对公账号");

    private final int code;

    private final String desc;

    RebateSettleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RebateSettleTypeEnum fromCode(int code) {
        for (RebateSettleTypeEnum enumValue : RebateSettleTypeEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }
}
