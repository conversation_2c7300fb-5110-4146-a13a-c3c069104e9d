package com.sankuai.medicalcosmetology.offline.code.api.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/4
 * @Description: 线下码扫码来源渠道
 */
public enum PromoQRCodeScanSourceType {

    SHOP_CODE(1, "门店码"),
    GOODS_CODE(2, "商品码"),
    STAFF_CODE(3, "员工码"),
    BRAND_CODE(4, "品牌码"),
    GROUND_PROMOTION_SYNC(5, "地推扫码同步"),
    MEDICAL_PROMO_CODE(6, "医疗下单码"),
    STAFF_CODE_TECHNICIAN_GOOD(7, "职人码-手艺人商品二维码"),
    PRIVATE_LIVE_PRODUCT_BARGAIN_CODE(8, "私域直播-商品改价码");

    public final int code;
    public final String desc;

    PromoQRCodeScanSourceType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PromoQRCodeScanSourceType getByCode(Integer code) {
        for (PromoQRCodeScanSourceType promoQRCodeScanSourceType : PromoQRCodeScanSourceType.values()) {
            if (promoQRCodeScanSourceType.code == code) {
                return promoQRCodeScanSourceType;
            }
        }
        return null;
    }

    private static Map<PromoQRCodeScanSourceType, PromoCodeType> sourceType2CodeTypeMap = Maps.newHashMap();

    private static Map<PromoQRCodeScanSourceType, Integer> sourceType2OldSourceTypeMap = Maps.newHashMap();

    private static Map<PromoCodeType, PromoQRCodeScanSourceType> codeType2SourceTypeMap = Maps.newHashMap();

    static {
        sourceType2CodeTypeMap.put(SHOP_CODE, PromoCodeType.SHOP_CODE);
        sourceType2CodeTypeMap.put(GOODS_CODE, PromoCodeType.GOODS_CODE);
        sourceType2CodeTypeMap.put(STAFF_CODE, PromoCodeType.STAFF_CODE);
        sourceType2CodeTypeMap.put(BRAND_CODE, PromoCodeType.BRAND_CODE);
        sourceType2CodeTypeMap.put(GROUND_PROMOTION_SYNC, PromoCodeType.SHOP_CODE);
        sourceType2CodeTypeMap.put(MEDICAL_PROMO_CODE, PromoCodeType.SHOP_CODE);
        sourceType2CodeTypeMap.put(STAFF_CODE_TECHNICIAN_GOOD, PromoCodeType.STAFF_CODE);

        // 定义见com.dianping.gmkt.event.web.scan.enums.PromoQRCodeSourceType
        sourceType2OldSourceTypeMap.put(GOODS_CODE, 1);
        sourceType2OldSourceTypeMap.put(STAFF_CODE, 5);
        sourceType2OldSourceTypeMap.put(BRAND_CODE, 6);
        sourceType2OldSourceTypeMap.put(GROUND_PROMOTION_SYNC, 7);
        sourceType2OldSourceTypeMap.put(STAFF_CODE_TECHNICIAN_GOOD, 5);

        codeType2SourceTypeMap.put(PromoCodeType.SHOP_CODE, SHOP_CODE);
        codeType2SourceTypeMap.put(PromoCodeType.GOODS_CODE, GOODS_CODE);
        codeType2SourceTypeMap.put(PromoCodeType.STAFF_CODE, STAFF_CODE);
        codeType2SourceTypeMap.put(PromoCodeType.BRAND_CODE, BRAND_CODE);
    }

    public static PromoCodeType sourceType2CodeType(Integer code) {
        PromoQRCodeScanSourceType promoQRCodeScanSourceType = PromoQRCodeScanSourceType.getByCode(code);
        if (promoQRCodeScanSourceType == null) {
            return null;
        }
        return sourceType2CodeTypeMap.getOrDefault(promoQRCodeScanSourceType, null);
    }

    public static int sourceType2OldSourceType(Integer code) {
        PromoQRCodeScanSourceType promoQRCodeScanSourceType = PromoQRCodeScanSourceType.getByCode(code);
        if (promoQRCodeScanSourceType == null) {
            return 0;
        }
        return sourceType2OldSourceTypeMap.getOrDefault(promoQRCodeScanSourceType, 0);
    }

    public static PromoQRCodeScanSourceType codeType2SourceType(Integer code) {
        PromoCodeType promoCodeType = PromoCodeType.getByCode(code);
        if (promoCodeType == null) {
            return null;
        }
        return codeType2SourceTypeMap.getOrDefault(promoCodeType, null);
    }
}
