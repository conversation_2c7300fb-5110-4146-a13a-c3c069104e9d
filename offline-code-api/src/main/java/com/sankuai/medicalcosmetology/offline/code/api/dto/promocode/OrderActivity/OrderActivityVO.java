package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠码订单免佣/返利活动配置VO
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class OrderActivityVO implements Serializable {
    private static final long serialVersionUID = -2711389781560127548L;

    private Long id;
    private String outBizId;
    /**
     * @see com.dianping.gmkt.event.api.promoqrcode.enums.QRSignUpTypeEnum
     */
    private Integer signUpType;
    private Integer activityType;
    private Integer subType;
    private Integer bizType;
    private Integer priority;
    /**
     * 一级类目列表
     */
    private Integer categoryId;
    /**
     * 二级类目列表
     */
    private List<Integer> categoryId2;
    private SpecialTagVO specialTag;
    private String activityName;
    private Long startTime;
    private Long endTime;
    private String ruleDesc;
    private String activityDesc;
    private ApplyConditionVO condition;
    private Boolean autoRegister;
    private RuleVO rule;
    private ActivityExtVO activityExt;
    private String approvalDesc;
    private String creator;
    private String updater;
    private Integer status;
    private Long createTime;
    private Long updateTime;
    /**
     * 商户渠道类型
     */
    private List<Integer> channels;

}
