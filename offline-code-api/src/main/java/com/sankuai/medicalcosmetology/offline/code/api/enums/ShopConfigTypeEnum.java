package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/12
 * @Description: 优惠码活动点评门店白名单配置类型枚举类
 */
@Getter
public enum ShopConfigTypeEnum {

    SHOP_ID(1, "门店白名单"),

    FLOOR_ID(2, "点评门店楼层");

    private final int code;

    private final String desc;

    ShopConfigTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShopConfigTypeEnum getByCode(int code) {
        for (ShopConfigTypeEnum e: values()) {
            if (e.code == code) {
                return e;
            }
        }
        return SHOP_ID;
    }

}
