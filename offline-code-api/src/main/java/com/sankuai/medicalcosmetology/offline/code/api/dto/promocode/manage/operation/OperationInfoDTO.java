package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/26
 * @Description:优惠码活动配置信息
 */
@Data
public class OperationInfoDTO implements Serializable {

    private static final long serialVersionUID = 8560902148834335558L;
    /** 活动id */
    private Long activityId;

    /** 业务类型，用于区分不同的活动业务场景
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.ActivityTypeEnum
     */
    private Integer activityType;

    /** 活动名称 */
    private String activityName;

    /** 业务类型，用于区分不同的活动业务场景
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.BizTypeEnum
     */
    private Integer bizType;

    /** 活动开始时间，时间戳格式 */
    private Long startTime;

    /** 活动结束时间，时间戳格式 */
    private Long endTime;

    /** 活动规则描述 */
    private String rules;

    /** 活动简介 */
    private String description;

    /** 活动标签，用于对活动进行分类或标记 */
    private List<String> tags;

    /**
     * 活动支持的平台，用于区分活动适用的不同平台，1:点评、2:美团、3:双平台
     */
    private Integer platform;

    /** 活动优先级，数值越大优先级越高 */
    private Integer priority;

    /** 活动状态，用于标识活动的当前状态
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.OperationStatusEnum
     */
    private Integer status;

    /**
     * 活动状态描述，用于展示活动状态的文字描述
     */
    private String statusDesc;

    /** 扩展信息，用于存储活动的额外信息 */
    private Map<String, Object> extInfo;

    /** 活动创建者，记录创建活动的用户 */
    private String creator;

    /** 活动更新者，记录最后更新活动信息的用户 */
    private String updator;

    /** 活动创建时间，时间戳格式，记录活动的创建时间 */
    private Long createTime;

    /** 活动更新时间，时间戳格式，记录活动最后一次更新的时间 */
    private Long updateTime;

    /** 投放限制 */
    private DeliveryRestrictionDTO deliveryRestriction;

    /** 资源配置 */
    private ResourceDTO resourceConfig;

    /**
     * 促评活动信息
     */
    private ReviewActivityDTO reviewActivityInfo;

    /**
     * 返利活动信息
     */
    private RebateActivityDTO rebateActivityInfo;

    /**
     * 当前登录用户id
     */
    private Long loginUserId;

}
