package com.sankuai.medicalcosmetology.offline.code.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/19
 * @Description: 通用环境req
 */
@Data
public class EnvRequest implements Serializable {

    /**
     * 城市id，区分平台，站内为首页城市id，站外为定位城市id
     */
    private Integer cityId;

    /**
     * 定位城市id，区分平台
     */
    private Integer locationCityId;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 平台 1-点评 2-美团
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;

    /**
     * 扫码客户端类型
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType
     */
    private Integer qrClientType;

    /**
     * 设备的appId
     * https://mobile.sankuai.com/apps/info
     */
    private Integer appId;

    /**
     * 客户端版本
     */
    private String version;

    /**
     * 1:iOS 2:Android
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.MobileOSEnum
     */
    private Integer mobileOS;

    /**
     * 客户端类型
     * APP(1, "APP"),
     * M_SITE(2, "I站或者M站"),
     * MINI_PROGRAM(3, "小程序"),
     * WEIXIN(4, "微信"),
     * PC(5, "PC")
     */
    private Integer clientType;
}
