package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 返利活动参与条件类型
 *
 * <AUTHOR>
 * @date 2020年11月25日 11:47:13
 */
public enum RebateJoinConditionTypeEnum {
    DP_SHOPID_WHITELIST(1, "点评门店白名单"),

    DP_SHOP_ITEM_ID(2, "点评门店楼层id"),

    NEW_MERCHANT(3, "新签商户"),
    BU_CATEGORY_CITY(4, "业务部+类目+城市");

    public Integer code;
    public String desc;

    RebateJoinConditionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validRebateJoinConditionType(int code) {
        for (RebateJoinConditionTypeEnum type : RebateJoinConditionTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateJoinConditionTypeEnum getByCode(int code) {
        for (RebateJoinConditionTypeEnum type : RebateJoinConditionTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("error code for RebateJoinConditionType.");
    }

}
