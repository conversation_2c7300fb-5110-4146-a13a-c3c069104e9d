package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/22
 * @Description: 订单评价内容
 */
@Data
public class OrderReviewContent implements Serializable {

    /**
     * 标题
     */
    private String title;

    /**
     * 头图
     */
    private String imageUrl;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 订单下单时间
     */
    private Long orderCreateTime;

    /**
     * 引导文案
     */
    private String guideText;

    /**
     * 按钮内容
     */
    private ButtonContent buttonContent;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商品id
     */
    private String productId;
}
