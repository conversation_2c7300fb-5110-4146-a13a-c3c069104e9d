package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 已报名活动账号信息
 */
@Data
public class AppliedInfoResponse implements Serializable {

    private static final long serialVersionUID = 1650958468444281420L;

    /**
     * 报名脱敏手机号
     */
    private String applyPhone;

    /**
     * 开店宝账户名称
     */
    private String accountName;

    /**
     * 返利账号类型
     * {@link RebateSettleTypeEnum}
     */
    private Integer settleType;

    /**
     * 所有门店的报名记录是否过期
     */
    private boolean allShopApplyExpired;

    /**
     * 已报名门店信息
     */
    private List<AppliedShopInfoResponse> applyShops;

}
