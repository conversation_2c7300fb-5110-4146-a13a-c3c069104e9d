package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @Description:【商户物料管理】查询商户物料信息
 */
@Data
public class MerchantMaterialInfoDTO implements Serializable {
    
    /**
     * 优惠码id
     */
    private Long promoQRCodeId;

    /**
     * 美团门店id
     */
    private Long mtShopId;

    /**
     * 点评门店id
     */
    private Long dpShopId;

    /**
     * 门店名称
     */
    private String dpShopName;

    /**
     * 城市id
     */
    private Integer dpCityId;

    /**
     * 城市名称
     */
    private String dpCityName;

    /**
     * 所在地区，省市区
     */
    private String regionName;

    /**
     * 一级类目名称
     */
    private String firstCategoryName;

    /**
     * 二级类目Id
     */
    private Integer secondCategoryId;

    /**
     * 二级类目名称
     */
    private String secondCategoryName;

    /**
     * 优惠码申请入口来源
     * 1:商家端，2:apollo端
     */
    private Integer sourceType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 申请次数
     */
    private Integer applyTimes;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 运营填写备注
     */
    private String memo;

    /**
     * 一级标签
     */
    private String firstTag;

    /**
     * 二级标签
     */
    private String secondTag;

    /**
     * 城市类型
     * 
     * @see com.dianping.gmkt.event.api.promoqrcode.enums.QRCityTypeEnum
     */
    private Integer cityType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 业务部门ID
     */
    private Integer businessDepId;

    /**
     * 业务部门名称
     */
    private String businessDepName;
}
