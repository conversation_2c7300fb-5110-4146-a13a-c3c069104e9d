package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chen<PERSON>yang02
 * @Date: 2024/12/30
 * @Description:
 */
@Getter
public enum LandingPageScanSourceEnum {

    OFFLINE_CODE(0, "线下码"),
    GROUND_PROMOTION_PART_TIME(1, "兼职地推");

    private final int code;
    private final String desc;

    LandingPageScanSourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LandingPageScanSourceEnum fromCode(Integer code) {
        if (code == null) {
            return OFFLINE_CODE;
        }
        for (LandingPageScanSourceEnum enumValue : LandingPageScanSourceEnum.values()) {
            if (code.equals(enumValue.getCode())) return enumValue;
        }
        return OFFLINE_CODE;
    }
}
