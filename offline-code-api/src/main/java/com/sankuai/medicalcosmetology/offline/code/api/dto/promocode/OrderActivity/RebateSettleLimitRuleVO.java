package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class RebateSettleLimitRuleVO implements Serializable {

    private static final long serialVersionUID = -473476685981215992L;

    private Long distributorAmountLimit;
    private Integer userOrderCountLimit;
    private Long shopAmountLimit;
    private List<Long> groupDealIds;
}
