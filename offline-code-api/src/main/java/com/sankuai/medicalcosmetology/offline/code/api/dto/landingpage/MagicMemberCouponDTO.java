package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/10/14
 * @Description:
 */
@Data
public class MagicMemberCouponDTO implements Serializable {

    /**
     * 膨胀状态，1：已膨胀，0：未膨胀
     */
    private Integer inflatedStatus;

    /**
     * 最高可膨胀金额（注意：与营销侧不同，此处单位为元）
     */
    private String maxInflateAmount;

    /**
     * 1-免费券，2-付费券
     */
    private Integer mmcCategory;

    /**
     * 是否可膨胀
     */
    private Boolean canInflate;

    /**
     * 到家券id
     */
    private String tspCouponId;

    /**
     * 到家券批次
     */
    private String tspCouponGroupId;

    /**
     * 到店券id
     */
    private String couponId;

    /**
     * 到店券批次
     */
    private String couponGroupId;

    /**
     * 券金额,单位为元
     */
    private String couponAmount;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券门槛,单位为元
     */
    private String threshold;

    /**
     * 过期时间（毫秒时间戳）
     */
    private Long couponEndTime;

    /**
     * 膨胀预览使用加密串
     */
    private String bizToken;

    /**
     * 资产类型 1：到家券类型（包括到家、家店通用券）、2到店券类型、3到家通用券
     */
    private int assetType;

    /**
     * 券数量
     */
    private Integer couponNum;
}
