package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: <PERSON><PERSON>yu
 * @Date: 2024/5/30
 * @Description:变更主体类型
 */
@Getter
public enum ChangeEntityTypeEnum {

    SHOP_ID(1, "商户id"),
    TECH_ID(2, "职人/手艺人id"),
    SHOP_ACCOUNT(3, "开点宝账号"),
    CUTOMER_ID(4, "账号id"),
    SALER_ID(5, "销售id"),
    SYSTEM(99, "系统");

    public int code;
    public String desc;

    private ChangeEntityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChangeEntityTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ChangeEntityTypeEnum entityTypeEnum : ChangeEntityTypeEnum.values()) {
            if (entityTypeEnum.getCode() == code) {
                return entityTypeEnum;
            }
        }
        return null;
    }
}
