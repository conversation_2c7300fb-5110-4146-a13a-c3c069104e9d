package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleRuleDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 活动规则DTO
 */
@Data
public class RuleDTO implements Serializable {
    private static final long serialVersionUID = -5593272246624936431L;

    /**
     * 免佣规则
     */
    private FreeCommissionRuleDTO freeCommissionRule;
    /**
     * 返利规则
     */
    private RebateSettleRuleDTO rebateRule;
    /**
     * 优惠码用户评价激励规则
     */
    private ReviewRuleDTO reviewRule;

}
