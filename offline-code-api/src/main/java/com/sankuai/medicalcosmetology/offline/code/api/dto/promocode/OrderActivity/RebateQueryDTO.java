package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import lombok.Data;

import java.io.Serializable;


@Data
public class RebateQueryDTO implements Serializable {
    private static final long serialVersionUID = -18337679835671203L;

    //门店id
    private Long dpShopId;
    //框架id
    private Long frameId;
    //订单id
    private String orderId;
    //门店名称
    private String shopName;
    //商户类型
    private String ShopType;
    //框架名称
    private String frameName;
    //团单id
    private Long groupOrderId;
    //采用佣金率
    private String commissionAmount;
    //团单名称
    private String groupOrderName;
    //行业类目
    private String industryCategory;
    //框架有效期
    private String frameValidityPeriod;
    //框架佣金率
    private String frameCommissionRate;
}
