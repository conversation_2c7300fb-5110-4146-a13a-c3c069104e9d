package com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageRequestDTO;
import lombok.Data;

import java.util.List;

/**
 * 账单查询DTO
 */
@Data
public class RebateActivityStatementQueryRequest extends PageRequestDTO {
    private static final long serialVersionUID = 5549966764046854788L;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 加密活动id
     */
    private String activityViewId;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 账单id
     */
    private List<Integer> statusList;

    /**
     * 点评账号id
     */
    private Long dpAccountId;
}
