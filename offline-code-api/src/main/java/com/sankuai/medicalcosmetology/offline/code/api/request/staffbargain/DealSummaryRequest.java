package com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpDecrypt;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/6 14:29
 */
@Data
public class DealSummaryRequest implements Serializable {
    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店id密文字段
     */
    @MdpDecrypt(target = "shopId")
    private String shopIdEncrypt;

    /**
     * 团购id
     */
    private Long dealGroupId;

    /**
     * 平台 1-点评 2-美团
     *
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;

    /**
     * 场景
     *
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.BargainStaffTypeEnum
     */
    private String scene;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品类型
     *
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.ProductTypeEnum
     */
    private Integer productType;
}
