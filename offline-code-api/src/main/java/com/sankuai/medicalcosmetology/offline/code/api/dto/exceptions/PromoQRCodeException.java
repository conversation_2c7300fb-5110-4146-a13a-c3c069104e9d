package com.sankuai.medicalcosmetology.offline.code.api.dto.exceptions;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoQRCodeResCode;
import lombok.Data;

/**
 * @author:wangdingxiong
 * @date: 2020-04-14
 * @time: 17:14
 */
@Data
public class PromoQRCodeException extends RuntimeException {

    private String code;
    private String msg;

    public PromoQRCodeException(PromoQRCodeResCode promoQRCodeResCode) {
        this.code = promoQRCodeResCode.getCode();
        this.msg = promoQRCodeResCode.getMessage();
    }

}
