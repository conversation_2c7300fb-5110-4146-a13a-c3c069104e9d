package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8
 * @Description:业务部配置DTO对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BusinessDepartmentConfigDTO implements Serializable {

    /**
     * code
     * 99:其他
     */
    private Integer code;

    /**
     * 业务部id
     */
    private Integer buId;

    /**
     * 业务部名称
     */
    private String name;

    /**
     * 适用场景列表
     *
     * 部分场景代码中无对应枚举，故部分值定义在lion配置描述中，除下面枚举外，其它情况只需要通过修改lion来增加适配场景
     * @see com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum
     */
    private List<Integer> scene;
}
