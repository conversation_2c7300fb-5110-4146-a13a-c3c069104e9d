package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ShopAutoVerifyQueryRequest;

/**
 * 功能描述: 医疗下单码
 *
 * <AUTHOR>
 * @date 2024/6/11
 **/
public interface MedicalPlaceOrderCodeShopService {

    /**
     * 判断门店是否开启自动核销
     * @param platform 平台 {@link  com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum}
     * @param shopId 门店id
     */
    RemoteResponse<Boolean> isShopOrderAutoVerify(int platform, long shopId);

    /**
     * 判断门店是否开启自动核销
     */
    RemoteResponse<Boolean> isShopOrderAutoVerify(ShopAutoVerifyQueryRequest request);

    /**
     * 设置门店自动核销状态
     * @param platform 平台 {@link  com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum}
     * @param shopId 门店id
     * @param verifySwitch {@link com.sankuai.medicalcosmetology.offline.code.api.enums.ShopAutoVerifySwitchEnum}
     * @return 设置后状态 {@link com.sankuai.medicalcosmetology.offline.code.api.enums.ShopAutoVerifySwitchEnum}
     */
    RemoteResponse<Integer> setShopOrderAutoVerify(int platform, long shopId, int verifySwitch);
}
