package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DealGroupInfoDTO implements Serializable {
    /**
     * 团单头图
     */
    private String imageUrl;

    /**
     * 团单标题
     */
    private String dealGroupName;

    /**
     * 点评团单id
     */
    private long dpDealGroupId;

    /**
     * 美团团单id
     */
    private long mtDealGroupId;

    /**
     * 团购价格
     */
    private String salePrice;

    /**
     * 门市价
     */
    private String marketPrice;

    /**
     * 售卖结束时间
     */
    private String saleEndTime;

    /**
     * 状态
     * @see LocalDealGroupStatusEnum
     */
    private int dealGroupStatus;

    /**
     * 状态
     * @see QROfflineDealGroupStatusEnum
     */
    private int status;
}
