package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * B端查询活动列表页DTO
 */
@Data
public class ActivityModuleResponse implements Serializable {

    private static final long serialVersionUID = 7413375229158118148L;
    /**
     * 活动id
     */
    private String id;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 活动说明
     */
    private String description;

    /**
     * 商家状态
     */
    private Integer shopStatus;

    /**
     * 展示查看详情按钮
     */
    private Integer showButton;

    /**
     * 详情跳转链接，如果后端返回需要读后端数据*
     */
    private String detailJumpUrl;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 权益头图
     */
    private String headPic;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 报名门店信息
     */
    private String tips;

    /**
     * 报名门店列表
     */
    private ApplyInfoResponse applyInfo;

    /**
     * 活动进度数据
     */
    private List<ActivityProgressDataResponse> progressDataList;

    /**
     * 达标条件
     */
    private List<String> requirements;

    /**
     * 权益示意信息
     */
    private ExampleResponse examples;

    /**
     * 优先级
     */
    private Integer order;

    /**
     * DB中真实状态
     */
    private Integer realStatus;

    /**
     * 可报名的门店id
     */
    private List<Long> appliableShopIdList;
}
