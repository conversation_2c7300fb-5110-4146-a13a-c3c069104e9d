package com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 投放活动分页查询请求
 *
 * <AUTHOR>
 * @date 2021年03月03日 17:02:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AdvertiseActivityPageRequest extends PageRequestDTO implements Serializable {

    private Long activityId;
    private String activityName;
    private String creator;
    /**
     * 资源位id
     */
    private Long sourceId;

}
