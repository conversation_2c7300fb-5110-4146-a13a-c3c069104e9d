package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/13
 * @Description:
 */
@Data
public class StaffInfo implements Serializable {

    /**
     * 头像图片
     */
    private String avatarUrl;

    /**
     * 名称
     */
    private String name;

    /**
     * 职位
     */
    private String title;

    /**
     * 所属门店名称
     */
    private String shopName;

    /**
     * 是否收藏门店
     */
    private Boolean collectShop=false;

    /**
     * 服务人数
     */
    private Integer serviceTimes;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 好评数
     */
    private Integer goodReviewCount;

    /**
     * 工作年限
     */
    private Integer workYears;
}
