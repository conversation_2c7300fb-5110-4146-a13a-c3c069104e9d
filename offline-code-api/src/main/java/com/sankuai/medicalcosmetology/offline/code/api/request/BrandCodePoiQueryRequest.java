package com.sankuai.medicalcosmetology.offline.code.api.request;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpDecrypt;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/19
 * @Description: 品牌码查询req
 */
@Data
public class BrandCodePoiQueryRequest extends EnvRequest implements Serializable {

    /**
     * 统一客户id
     */
    private Long customerId;

    /**
     * 门店名称（用于模糊查询）
     */
    private String poiName;

    /**
     * 已选择门店id，需要置顶
     */
    private Long selectedPoiId;

    /**
     * 已选择门店id加密字段
     */
    @MdpDecrypt(target="selectedPoiId")
    private String selectedPoiIdEncrypt;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("customerId=").append(customerId);
        sb.append(", poiName=").append(poiName);
        sb.append(", selectedPoiId=").append(selectedPoiId);
        sb.append(", cityId=").append(super.getCityId());
        sb.append(", lng=").append(super.getLng());
        sb.append(", lat=").append(super.getLat());
        sb.append(", platform=").append(super.getPlatform());
        sb.append(", qrClientType=").append(super.getQrClientType());
        sb.append(", version=").append(super.getVersion());
        sb.append(", mobileOS=").append(super.getMobileOS());
        sb.append("]");
        return sb.toString();
    }
}
