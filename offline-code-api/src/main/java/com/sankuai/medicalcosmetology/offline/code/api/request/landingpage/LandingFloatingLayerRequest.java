package com.sankuai.medicalcosmetology.offline.code.api.request.landingpage;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/22
 * @Description: 落地页浮层查询req
 */
@Data
public class LandingFloatingLayerRequest extends LandingBaseRequest implements Serializable {

    private String codeKey;

    /**
     * 码类型
     * @see PromoCodeType
     */
    private Integer codeType;
}
