package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

@Getter
public enum RebateInvalidTypeEnum {

    UNKNOWN(0, "未知"),

    NOT_SCAN_ORDER(1, "非扫码返利订单"),
    REPEAT_ORDER(2, "重复订单"),

    ENJOYED_OTHER_DISCOUNT(3, "已享受其他优惠"),
    TECH_REBATE_SHOP_NOT_REBATE(4, "职人返利门店不返利"),


    SHOP_IN_BLACK_LIST(5, "门店在黑名单中"),
    ORDINARY_REBATE(6, "普通返利"),
    STEP_REBATE(7, "阶梯返利"),
    STEP_NEW_OLD_CUSTOMER_REBATE(8, "阶梯返利-新客老客"),
    STEP_DAILY_FIRST_ORDER_REBATE(9, "阶梯返利-新客老客-每日首单返利"),
    ORDER_AMOUNT_PROPORTIONAL_REBATE(10, "订单金额-按比例返利"),

    NO_REBATE_ACTIVITY_RECORD(11, "无返利活动实例"),
    NO_REBATE_ACTIVITY_CONFIG(12, "无返利活动配置"),

    NO_REBATE_ACTIVITY_RULE(13, "该返利结算类型中无返利规则"),
    NOT_MATCH_REBATE_TYPE(14, "没有匹配的返利结算类型"),

    NOT_SATISFY_REBATE_RULE(15, "不符合返利规则"),

    /**
     * 双反卡控，已经享受了别的平台活动返利
     */
    DOUBLE_REBATE(16, "双反卡控"),
    /**
     * 佣金击穿卡控
     */
    REBATE_BREAK_DOWN_CONTROL(17,"佣金击穿防控"),
    ;

    private final int code;

    private final String desc;

    RebateInvalidTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RebateInvalidTypeEnum fromCode(int code) {
        for (RebateInvalidTypeEnum enumValue : RebateInvalidTypeEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }
}
