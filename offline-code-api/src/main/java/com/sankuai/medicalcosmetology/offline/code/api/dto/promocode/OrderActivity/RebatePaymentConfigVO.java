package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import lombok.Data;

import java.io.Serializable;


@Data
public class RebatePaymentConfigVO implements Serializable {
    private static final long serialVersionUID = 68271369714755202L;

    /**
     * 提现类型：1用户手动提现，2系统账期自动提现 see @com.dianping.gmkt.event.api.rebate.enums.RebatePaymentType
     */
    private Integer paymentType;
    /**
     * 账期：paymentType = 2 时，返利订单结算后N天自动打款
     */
    private Integer paymentDays;
    /**
     * 向日葵打款业务线参数
     */
    private String bizLine;

}
