package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum {

    DP_USER(1, "点评用户"),
    MT_USER(2, "美团用户");
    ;


    public final int type;
    public final String desc;

    public static AccountTypeEnum getByCode(Integer type) {
        return Arrays.stream(AccountTypeEnum.values())
                .filter(e -> Objects.equals(e.getType(), type))
                .findFirst()
                .orElse(null);
    }
}