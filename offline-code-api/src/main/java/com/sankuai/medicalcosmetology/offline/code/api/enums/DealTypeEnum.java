package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/10
 * @Description: deal类型枚举
 */
@Getter
public enum DealTypeEnum {

    GROUP(1, "团购"),
    FUN(2, "泛商品"),
    STANDARD_PRODUCT(3, "标品");

    private final int code;
    private final String desc;

    private DealTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DealTypeEnum fromCode(int code) {
        DealTypeEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            DealTypeEnum enumValue = var1[var3];
            if (enumValue.getCode() == code) {
                return enumValue;
            }
        }

        return null;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static int productType2DealType(int productType) {
        switch (ProductTypeEnum.fromCode(productType)) {
            case TUAN_DEAL:
            case MT_DEAL_GROUP:
                return GROUP.getCode();
            case TIME_CARD:
            case BOOK:
            case PREPAY:
            case PAY_PER_CARD:
                return FUN.getCode();
            case STANDARD_PRODUCT:
                return STANDARD_PRODUCT.getCode();
            default:
                return GROUP.getCode();
        }
    }
}