package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/22
 * @Description:
 */
@Getter
public enum BaseResponseEnum {

    SUCCESS(200, "success"),
    FAILURE(400, "failure"),
    INVALID_PARAM(401, "illegal_parameter"),
    NOT_LOGIN(402, "not_login"),
    NOT_AUTH(403, "not_auth"),
    INNER_FAILURE(500, "inner_failure"),
    BRAND_CODE_NO_POI(10001, "品牌码无有效门店");

    public int code;
    public String desc;

    private BaseResponseEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
