package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 已报名门店信息
 */
@Data
public class AppliedShopInfoResponse implements Serializable {
    private static final long serialVersionUID = 630288607466811580L;

    /**
     * 活动实例加密id
     */
    private String activityRecordViewId;

    /**
     * 点评店铺id
     */
    private Long dpShopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 报名记录是否超过时效
     */
    private boolean applyExpired;

    /**
     * 报名账号Id*
     */
    private Long dpAccountId;
}
