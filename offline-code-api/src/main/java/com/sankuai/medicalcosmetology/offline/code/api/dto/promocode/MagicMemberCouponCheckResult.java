package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode;

import com.sankuai.medicalcosmetology.offline.code.api.enums.MagicMemberCouponScenarioEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 神券开关校验结果
 *
 * <AUTHOR>
 * @date 2024年09月26日 16:49:52
 */
@Data
public class MagicMemberCouponCheckResult implements Serializable {

    /**
     * 神券场景标识
     */
    private String magicFlag;

    private int canUse;
    private int canInflate;
    private int canBuyMMCPackage;
    private int canGetFreeMMC;

    /**
     * 获取神券场景
     * 
     * @return GodCouponScenario 神券场景枚举
     */
    public MagicMemberCouponScenarioEnum getScenario() {
        return MagicMemberCouponScenarioEnum.fromMagicFlag(this.magicFlag);
    }

    public void setMagicFlag(String magicFlag) {
        this.magicFlag = magicFlag;
        MagicMemberCouponScenarioEnum scenarioEnum = getScenario();
        this.canUse = scenarioEnum.getCanUse();
        this.canInflate = scenarioEnum.getCanInflate();
        this.canBuyMMCPackage = scenarioEnum.getCanBuyMMCPackage();
        this.canGetFreeMMC = scenarioEnum.getCanGetFreeMMC();
    }
}
