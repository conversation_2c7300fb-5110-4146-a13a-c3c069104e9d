package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * 优惠码活动状态枚举
 *
 * <AUTHOR>
 * @date 2020年10月13日 21:13:08
 */
public enum QRActivityStatus {
    OFFLINE(0, "已下线"),
    NOT_START(1, "未开始"),
    ONGOING(2, "进行中"),
    END(3, "已过期");

    public final int code;
    public final String desc;

    QRActivityStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validActivityStatus(int code) {
        for (QRActivityStatus status : QRActivityStatus.values()) {
            if (status.code == code) {
                return true;
            }
        }
        return false;
    }

    public static QRActivityStatus getByCode(int code) {
        for (QRActivityStatus status : QRActivityStatus.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

}
