package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebatePaymentTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 返利活动提现打款配置DTO
 */
@Data
public class RebatePaymentConfigDTO implements Serializable {
    private static final long serialVersionUID = 372085335276368001L;

    /**
     * 提现类型：1用户手动提现，2系统账期自动提现
     *
     * @see RebatePaymentTypeEnum
     */
    private Integer paymentType;
    /**
     * 账期：paymentType = 2 时，返利订单结算后N天自动打款
     */
    private Integer paymentDays;
    /**
     * 向日葵打款业务线参数
     */
    private String bizLine;

}
