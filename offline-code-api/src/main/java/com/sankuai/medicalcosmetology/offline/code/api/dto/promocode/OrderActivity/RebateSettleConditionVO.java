package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;
import lombok.Data;

import java.io.Serializable;


@Data
public class RebateSettleConditionVO implements Serializable {
    private static final long serialVersionUID = 2398145964319306810L;

    private Boolean limitOrderPrice = false;
    private Long orderPrice;

    private Boolean limitDeal = false;
    private Long dealItemId;

    private Boolean limitVerifyTime = false;
    /**
     * 返利核销时间
     * 单位：天
     */
    private Integer verifyTimeLimit;

}
