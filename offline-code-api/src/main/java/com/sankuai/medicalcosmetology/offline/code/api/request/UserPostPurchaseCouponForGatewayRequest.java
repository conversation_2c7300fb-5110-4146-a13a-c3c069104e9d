package com.sankuai.medicalcosmetology.offline.code.api.request;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PostPurchaseCouponPageSourceEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/10
 * @Description:
 */
@Data
public class UserPostPurchaseCouponForGatewayRequest extends EnvRequest implements Serializable {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 页面来源
     * 1-端首页 2-团购频道页 3-订详页 4-核销页
     * @see PostPurchaseCouponPageSourceEnum
     */
    private Integer pageSource;

}
