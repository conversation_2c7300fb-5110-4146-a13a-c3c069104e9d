package com.sankuai.medicalcosmetology.offline.code.api.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/6/20
 * @Description:
 */
@Data
public class EmptyCodeBindInfoRequest extends EnvRequest implements Serializable {

    private List<String> secrets;

    private Long dpShopId;

    private Long mtShopId;

    @Deprecated
    private Integer dpDealId;

    private Long longDpDealId;

    private Long staffCodeId;

    private Long userId;

    /**
     * 操作人id
     * B端时为商家账号id，C端时为用户id
     */
    private Long operatorId;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType
     */
    private Integer codeType;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.application.enums.PromoCodeBindTypeEnum
     */
    private Integer bindType;
}
