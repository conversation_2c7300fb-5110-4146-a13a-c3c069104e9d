package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.BrandCodeDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.TitleAndDataDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity.AdvertiseActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.*;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeModelPageRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.StaffSaleRequest;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/7
 * @Description: 优惠码B端网关服务
 */
public interface PromoCodeForBGatewayService {

    /**
     * 是否展示品牌码模块
     * @param dpShopId
     * @return
     */
    RemoteResponse<Boolean> isShowBrand(Long dpShopId);

    /**
     * 查询品牌码
     * @param dpShopId
     * @return
     */
    RemoteResponse<BrandCodeDTO> queryBrandQRCodeByAccountId(Long dpShopId);

    RemoteResponse<String> downloadStaffSaleOrderInfo(StaffSaleRequest request);

    RemoteResponse<String> queryDownloadUrl(String fileKey);

    RemoteResponse<String> queryStaffOverviewInfoList(StaffSaleRequest request);

    RemoteResponse<String> downloadDetailedData(StaffSaleRequest request);

    RemoteResponse<List<TitleAndDataDTO>> queryOverviewData(String key);

    /**
     * 下载门店提现明细
     */
    RemoteResponse<String> downloadStoreWithdrawalDetail(String activityConfigViewId);

    /**
     * 下载门店返利明细
     */
    RemoteResponse<String> downloadStoreRebateDetail(String activityConfigViewId);

    /**
     * 查询B端落地页企微配置
     * @param dpShopId
     * @return
     */
    RemoteResponse<AdvertiseActivityDTO> queryWeComConfigByAccountId(Long dpShopId);

    /**
     * 条件查询B端优惠码货架管理模版
     * @param mtUserId 美团用户id
     */
    RemoteResponse<PromoCodeModelPageDTO> queryPromoCodeModelWithCondition(PromoCodeModelPageRequest request,Long mtUserId);

    /**
     * B端创建或更新模版（根据modelId判定）
     */
    RemoteResponse<Boolean> createOrUpdatePromoCodeModel(PromoCodeModelDTO modelDTO, Long mtUserId);

    /**
     * B端查询模版适用门店列表
     */
    RemoteResponse<DpShopPageDTO> queryPromoCodeModelShopList(PromoCodeModelPageRequest request,Long mtUserId);

    /**
     * B端删除优惠码货架管理模版
     */
    RemoteResponse<Boolean> deletePromoCodeModel(Integer modelId, Long mtUserId);

    /**
     * B端查询模版绑定的团单列表
     */
    RemoteResponse<DealGroupInfoPageDTO> queryPromoCodeModelBoundedDealGroup(String keyWord,Integer modelId,Integer pageNo,Integer pageSize, Long mtUserId);

    RemoteResponse<DealGroupInfoPageDTO> queryPromoCodeModelSelectList(String keyword,Integer modelId, Integer pageNo,Integer pageSize,Long mtUserId);

    /**
     * B端查询模版可选团单列表
     */
    RemoteResponse<DealGroupInfoPageDTO> queryPromoCodeModelSelectList(String keyword,Integer modelId, Integer pageNo,Integer pageSize,Long mtUserId, List<Long> dpShopIds, List<Integer> tradeTypes);

    /**
     * B端查询模版可选团单列表
     */
    RemoteResponse<PromoCodeModelDetailDTO> queryPromoCodeModelDetail(Integer modelId, Long mtUserId);

    /**
     * B端小黄条展示
     */
    RemoteResponse<PromoCodeYellowBarDTO> showYellowBar(Long dpShopId, String tabTag);

    /**
     * B端判断门店是否命中白名单
     */
    RemoteResponse<Boolean> hitShopWhiteList(String sceneCode,Long dpShopId);

    /**
     * 根据门店ID列表和ZDC标签代码检查门店是否具有指定的ZDC标签
     * @return 返回是否所有指定门店都具有该ZDC标签的布尔值
     */
    RemoteResponse<Boolean> checkZdcTagByMtShopIds(ZdcTagCheckRequestDTO request);

    /**
     * 更新二维码扩展信息
     *
     * @param secretKey
     * @param extendInfoDTO
     * @return
     */
    RemoteResponse<Boolean> updateQrCodeExtendInfoBySecretKey(String secretKey, QRCodeExtendInfoDTO extendInfoDTO);

}
