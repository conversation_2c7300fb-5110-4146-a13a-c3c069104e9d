package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 返利结算限制规则
 */
@Data
public class RebateSettleLimitRuleDTO implements Serializable {

    private static final long serialVersionUID = -1738856070127188577L;

    /**
     * 单个职人返利上限
     */
    private Long distributorAmountLimit;

    /**
     * 同一用户返利订单上限
     */
    private Integer userOrderCountLimit;

    /**
     * 商户总返利上限
     */
    private Long shopAmountLimit;

    /**
     * 返利团单配置
     */
    private List<Long> groupDealIds;
}
