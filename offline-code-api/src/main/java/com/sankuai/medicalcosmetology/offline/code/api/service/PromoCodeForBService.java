package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.MagicMemberCouponCheckResult;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.QRCodeExtendInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeCreateRequest;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/25
 * @Description: 优惠码B端rpc服务
 */
public interface PromoCodeForBService {

    RemoteResponse<Boolean> initOrUpdateBrandPoiByCustomerId(Long customerId);

    RemoteResponse<Boolean> initOrUpdateBrandPoiByAccountId(Long accountId);

    RemoteResponse<String> queryOrCreateBrandCodeFromMushroom(Long accountId, Long topDealGroupId);

    /**
     * 查询门店神券使用限制结果
     * @param dpShopId
     * @return
     */
    RemoteResponse<MagicMemberCouponCheckResult> queryMagicMemberCouponSwitch(Long dpShopId);

    /**
     * B端判断门店是否命中白名单
     */
    RemoteResponse<Boolean> hitShopWhiteList(String sceneCode,Long dpShopId);

    /**
     * 创建新的优惠码
     * @param request
     * @return
     */
    RemoteResponse<PromoCodeInfoDTO> createAndGetPromoCode(PromoCodeCreateRequest request);
}
