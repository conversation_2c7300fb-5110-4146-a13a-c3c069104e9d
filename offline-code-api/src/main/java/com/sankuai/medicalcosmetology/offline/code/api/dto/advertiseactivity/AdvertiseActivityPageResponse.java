package com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 活动列表查询response
 *
 * <AUTHOR>
 * @date 2020年10月09日 15:48:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AdvertiseActivityPageResponse extends PageResponseDTO implements Serializable {

    private List<AdvertiseActivityLiteDTO> activityList;
}
