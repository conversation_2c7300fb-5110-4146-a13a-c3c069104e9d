package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/13
 * @Description:
 */
@Data
public class UserScanRecordQueryDTO implements Serializable {

    private Long userId;

    /**
     * 平台
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;

    private Long shopId;

    /**
     * 扫码来源
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PromoQRCodeScanSourceType
     */
    private Integer sourceType;

    private Long sourceId;

    private String codeKey;

    private Date startTime;

    private Date endTime;
}
