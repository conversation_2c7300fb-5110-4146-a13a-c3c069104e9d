package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/22
 * @Description: 文案内容
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TextContent implements Serializable {

    /**
     * 文案
     */
    private String text;

    /**
     * 是否高亮
     */
    @Builder.Default
    private Boolean highlight = false;
}
