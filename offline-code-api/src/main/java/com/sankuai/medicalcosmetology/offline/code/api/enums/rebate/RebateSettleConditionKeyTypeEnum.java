package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 订单返利条件类型枚举
 */
public enum RebateSettleConditionKeyTypeEnum {
    BETWEEN(1, "between", "介于"),
    CONTAINED(2, "contained", "包含"),
    NOT_CONTAINED(3, "notContained", "不包含"),
    EQUALS(4, "equals", "等于"),
    LESS_THAN(5, "lessThan", "小于"),
    LESS_OR_EQUALS(6, "lessOrEquals", "小于等于"),
    GREATER_THAN(7, "greaterThan", "大于"),
    GREATER_OR_EQUALS(8, "greaterOrEquals", "大于等于"),
    NOT_EQUALS(9, "notEquals", "不等于");

    public Integer code;
    public String value;
    public String message;

    RebateSettleConditionKeyTypeEnum(Integer code, String value, String message) {
        this.code = code;
        this.value = value;
        this.message = message;
    }

    public static boolean validRebateSettleConditionKeyType(int code) {
        for (RebateSettleConditionKeyTypeEnum  type : RebateSettleConditionKeyTypeEnum .values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateSettleConditionKeyTypeEnum  getByCode(int code) {
        for (RebateSettleConditionKeyTypeEnum  type : RebateSettleConditionKeyTypeEnum .values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("error code for RebateSettleConditionKeyType.");
    }

}
