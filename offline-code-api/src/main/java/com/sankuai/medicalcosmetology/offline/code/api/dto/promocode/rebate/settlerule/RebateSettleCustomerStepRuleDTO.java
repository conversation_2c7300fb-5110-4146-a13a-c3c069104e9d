package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 返利结算客户阶梯规则DTO
 */
@Data
public class RebateSettleCustomerStepRuleDTO implements Serializable {

    private static final long serialVersionUID = 6646264188600012207L;

    /**
     * 阶梯规则
     */
    private List<RebateSettleRuleStepConfigDTO> ruleStep;

    /**
     * 返利结算条件
     */
    private RebateSettleConditionDTO conditionDTO;

}
