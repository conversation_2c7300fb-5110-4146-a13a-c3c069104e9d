package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PromoCodeModelDetailDTO implements Serializable {
    private Integer modelId;
    private String modelName;
    private List<DpShopInfoDTO> selectShopList;
    private List<DealGroupInfoDTO> selectDealGroupList;
    private Date createTime;
    private Date updateTime;
}
