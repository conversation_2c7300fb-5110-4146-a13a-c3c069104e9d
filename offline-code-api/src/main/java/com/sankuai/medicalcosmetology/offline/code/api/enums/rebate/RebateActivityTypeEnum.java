package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 返利活动类型枚举
 */
public enum RebateActivityTypeEnum {
    FREE_COMMISSION(11, "【免佣】优惠码订单免佣"),
    REDUCE_COMMISSION(12, "【免佣】优惠码订单降佣"),
    NORMAL_REBATE_ORDER_CNT(21, "【返利】按订单数返利"),
    NORMAL_REBATE_USER_CNT(22, "【返利】按交易用户数返利"),
    STEP_REBATE_USER_CNT(23, "【返利】阶梯返利-按用户数"),
    NORMAL_REBATE_DEAL_ORDER_CNT(24, "【返利】团单-按订单数返利"),
    STEP_REBATE_NEW_OLD_CUSTOMER_CNT(25, "【返利】阶梯返利-按新老用户数"),

    USER_REVIEW(31, "优惠码用户评价激励"),

    ORDER_REBATE_PRICE_PROPORTION(26, "【返利】按订单金额比例返现"),
    ORDER_REBATE_PRICE_FIXED(27, "【返利】按订单金额固定返现"),
    STEP_REBATE_DAILY_FIRST_ORDER(28, "【返利】按新老用户数-每日首单用户奖励"),

    STEP_REBATE_ORDER_CNT(29, "【返利】阶梯返利-按订单数"),
    SET_REBATE_VERIFY_GTV(30, "【返利】阶梯返利-按订单总交易额"),

    SELLER_SUBSIDY_PRICE_PROPORTION(32, "【返利】商补金额-按比例返现")
    ;

    public final int code;
    public final String desc;

    RebateActivityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validOrderActivitySubType(int code) {
        for (RebateActivityTypeEnum type : RebateActivityTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateActivityTypeEnum getByCode(int code) {
        for (RebateActivityTypeEnum type : RebateActivityTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
