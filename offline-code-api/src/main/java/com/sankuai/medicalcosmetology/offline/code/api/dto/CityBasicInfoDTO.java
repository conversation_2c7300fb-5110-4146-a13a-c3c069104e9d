package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/24
 * @Description: 城市基础信息dto
 */
@Data
public class CityBasicInfoDTO implements Serializable {

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市名称拼音
     */
    private String cityPyName;

    /**
     * 城市名称拼音首字母
     */
    private String cityPyRank;

    /**
     * 平台
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;
}
