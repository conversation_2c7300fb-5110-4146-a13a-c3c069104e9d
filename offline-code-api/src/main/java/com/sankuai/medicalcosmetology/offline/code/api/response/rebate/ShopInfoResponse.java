package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 店铺信息
 */
@Data
public class ShopInfoResponse implements Serializable {
    private static final long serialVersionUID = 7677370189024103619L;
    private Long dpShopIdLong;


    private String shopName;

    private String accountName;

    private Long dpAccountId;

    public ShopInfoResponse() {

    }
    public ShopInfoResponse(Long dpShopIdLong, String shopName) {
        this.dpShopIdLong = dpShopIdLong;
        this.shopName = shopName;
    }

    public ShopInfoResponse(Long dpShopIdLong, String shopName, String accountName) {
        this.dpShopIdLong = dpShopIdLong;
        this.shopName = shopName;
        this.accountName = accountName;
    }

    public ShopInfoResponse(Long dpShopIdLong, String shopName, Long dpAccountId) {
        this.dpShopIdLong = dpShopIdLong;
        this.shopName = shopName;
        this.dpAccountId = dpAccountId;
    }
}
