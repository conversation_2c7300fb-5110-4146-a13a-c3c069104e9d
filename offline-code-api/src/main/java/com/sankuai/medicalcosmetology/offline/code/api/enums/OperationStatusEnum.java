package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * <AUTHOR>
 * @date 2024/9/30
 * @Description:活动状态
 */
public enum OperationStatusEnum {
    ON_APPROVE(1, "", "审批中"),
    UNAPPROVED(2, "", "审批未通过"),
    OFFLINE(3, "已下线", "已下线"),
    NOT_START(4, "未开始", "未开始: 已通过审批，但未到活动时间"),
    ONGOING(5, "进行中", "活动在线: 已通过审批，且当前在活动时间"),
    END(6, "已过期", "已结束: 活动时间过期");

    public final int code;
    public final String noNeedApproveDesc;
    public final String needApproveDesc;

    OperationStatusEnum(int code, String noNeedApproveDesc, String needApproveDesc) {
            this.code = code;
            this.noNeedApproveDesc = noNeedApproveDesc;
            this.needApproveDesc = needApproveDesc;
        }

    public static boolean validActivityStatus(int code) {
        for (OperationStatusEnum status : OperationStatusEnum.values()) {
            if (status.code == code) {
                return true;
            }
        }
        return false;
    }

    public static OperationStatusEnum getByCode(int code) {
        for (OperationStatusEnum status : OperationStatusEnum.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

}
