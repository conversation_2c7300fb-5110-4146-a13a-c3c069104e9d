package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class RebateActivityRecordDTO implements Serializable {

    private static final long serialVersionUID = -1419006311804375456L;
    /**
     * 说明: 报名记录id
     */
    private Long id;

    /**
     * 说明: 活动ID
     */
    private Long activityId;

    /**
     * 说明: 报名类型，1-商户 2-职人
     */
    private Byte registType;

    /**
     * 说明: 业务类型
     */
    private Byte bizType;

    /**
     * 说明: 业务线
     */
    private String bizLine;

    /**
     * 说明: 参与人美团userId
     */
    private Long mtUserId;

    /**
     * 说明: 返利打款类型，1-对私，2-对公
     */
    private Byte settleType;

    /**
     * 说明: 点评店铺id
     */
    private Long dpShopId;

    /**
     * 说明: 点评账号id
     */
    private Long dpAccountId;

    /**
     * 说明: 职人类型 1-手艺人 2-点评用户
     */
    private Integer distributorType;

    /**
     * 说明: 职人id
     */
    private Long distributorId;

    /**
     * 说明: 活动累计返利订单数
     */
    private Integer rebateOrderCnt;

    /**
     * 说明: 活动累计返利用户数
     */
    private Integer rebateUserCnt;

    /**
     * 说明: 活动累计返利金额（分）
     */
    private Long rebateAmount;

    /**
     * 说明: 有效期开始时间，NULL表示长期有效
     */
    private Date validStartTime;

    /**
     * 说明: 有效期结束时间，NULL表示长期有效
     */
    private Date validEndTime;

    /**
     * 说明: 状态：0软删除，1有效，2取消报名
     */
    private Byte status;

    /**
     * 说明: 创建时间
     */
    private Date addTime;

    /**
     * 说明: 修改时间
     */
    private Date updateTime;

    /**
     * 说明: 返利规则
     */
    private RuleDTO rule;

    /**
     * 活动信息
     */
    private RebateActivityDTO rebateActivity;
}
