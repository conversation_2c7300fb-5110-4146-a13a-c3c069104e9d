package com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 取消报名活动请求
 */
@Data
public class CancelApplyActivityRequest implements Serializable {
    private static final long serialVersionUID = -685642191800291305L;

    /**
     * 活动加密id
     */
    private String activityViewId;

    /**
     * 店铺id
     */
    private List<Long> dpShopIds;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum
     * 活动类型
     */
    private Integer activityType;
}
