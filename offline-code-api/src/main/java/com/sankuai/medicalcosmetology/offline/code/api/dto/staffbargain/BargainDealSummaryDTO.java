package com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/6 14:28
 */
@Data
public class BargainDealSummaryDTO implements Serializable {
    /**
     * 展示门店名称
     */
    private String shopName;

    /**
     * 团购头图url
     */
    private String dealHeadPic;

    /**
     * 团购标题
     */
    private String dealTitle;

    /**
     * 团购门市价格
     */
    private String dealOriginPrice;

    /**
     * 团购售价
     */
    private String dealPrice;

    /**
     * 团购最低设置金额
     */
    private String minBargainPrice;

    /**
     * 团购最高设置金额
     */
    private String maxBargainPrice;
}
