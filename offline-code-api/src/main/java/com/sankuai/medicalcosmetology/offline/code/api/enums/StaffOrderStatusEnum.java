package com.sankuai.medicalcosmetology.offline.code.api.enums;
import lombok.Getter;
import java.util.Arrays;
/**
 * 职人分享赚订单状态枚举
 */
@Getter
public enum StaffOrderStatusEnum {
    ALL(0, -1,"全部","全部"),
    REDEEMED(1, 1,"已核销","已完成"),
    TO_BE_REDEEMED(2, 0,"待核销","待消费"),
    REFUNDED(3, 2,"已退款","已退款");
    public final int code;
    /**
     * 数仓中订单的状态
     */
    public final int swanStatus;
    public final String desc;
    public final String swanDesc;
    StaffOrderStatusEnum(int code, int swanStatus, String desc,String swanDesc) {
        this.code = code;
        this.swanStatus = swanStatus;
        this.desc = desc;
        this.swanDesc = swanDesc;
    }
    public static boolean validateCode(int code) {
        return Arrays.stream(values()).anyMatch(t -> t.code == code);
    }
    /**
     * 返回code对应的收益枚举
     *
     * @param code code
     * @return 返回code对应的订单枚举，没有的话则返回为null
     */
    public static StaffOrderStatusEnum getStaffOrderStatus(int code) {
        return Arrays.stream(values()).filter(t -> t.code == code).findFirst().orElse(null);
    }
    /**
     * 返回code对应的swanStatus
     *
     * @param code code
     * @return  code对应的swanStatus，如果没有匹配的code，则返回-1
     */
    public static int getSwanStatus(int code) {
        return Arrays.stream(values()).filter(t -> t.code == code).findFirst().map(StaffOrderStatusEnum::getSwanStatus).orElse(-1);
    }
    public static String getSwanDescStr(Object swanStatus) {
        if (swanStatus instanceof Integer) {
            int swanStatusInt = (int) swanStatus;
            return Arrays.stream(values()).filter(t -> t.swanStatus == swanStatusInt).findFirst().map(StaffOrderStatusEnum::getSwanDesc).orElse("");
        }
        return "";
    }
}