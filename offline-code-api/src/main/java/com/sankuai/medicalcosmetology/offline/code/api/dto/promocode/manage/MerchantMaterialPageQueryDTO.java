package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageRequestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @Description:【商户物料管理】分页查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantMaterialPageQueryDTO extends PageRequestDTO implements Serializable {

    /**
     * 后台一级类目
     */
    private Integer firstCategory;

    /**
     * 后台二级类目
     */
    private Integer secondCategory;

    /**
     * 城市类型,0:全部
     * @see com.dianping.gmkt.event.api.promoqrcode.enums.QRCityTypeEnum
     */
    private Integer cityType;

    /**
     * 起始时间毫秒时间戳
     */
    private Long startTime;

    /**
     * 结束时间毫秒时间戳
     */
    private Long endTime;

    /**
     * dpShopId列表，以英文逗号分隔，最多100个dpShopId
     */
    private String dpShopIds;

    /**
     * 业务部门ID列表
     */
    private List<Integer> businessDepIds;

    /**
     * 申请次数，0：全部，1：首次申请，2：二次申请
     */
    private Integer applyTimesType;

}
