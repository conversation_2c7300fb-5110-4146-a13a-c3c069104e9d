package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpEncrypt;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description: 门店信息模块
 */
@Data
public class LandingPageShopInfo implements Serializable {

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 门店id，区分平台
     */
    @MdpEncrypt(target = "shopIdEncrypt")
    private Long shopId;

    /**
     * 门店id加密字段
     */
    private String shopIdEncrypt;

    /**
     * shopuuid
     */
    @MdpEncrypt(target = "shopUuidEncrypt")
    private String shopUuid;

    /**
     * shopuuid加密字段
     */
    private String shopUuidEncrypt;

    /**
     * 默认头图
     */
    private String defaultPic;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 距离，单位m
     */
    private Double distance;

    /**
     * 地址
     */
    private String address;

    /**
     * 是否收藏门店
     */
    private Boolean collectShop=false;

    /**
     * 门店后台一级类目id
     */
    private Integer mainCategoryId;

    /**
     * 门店前台城市id，区分平台
     */
    private Integer shopCityId;

    /**
     * 商详页跳链
     */
    private String shopUrl;

    /**
     * 星级评分，5分制，精确到小数点后1位
     */
    private String starStr;

    /**
     * 营业状态
     */
    private String businessState;

    /**
     * 营业时间
     */
    private String businessHours;
}
