package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 返利活动信息
 */
@Data
public class RebateActivityDetailResponse implements Serializable {
    private static final long serialVersionUID = -8818753539155695965L;

    /**
     * 返利活动配置加密id
     */
    private String activityConfigViewId;
    /**
     * 返利活动规则类型: 1 无门槛返利, 2 阶梯返利
     */
    private Integer ruleType;
    /**
     * 返利活动指标类型: 1 订单数, 2 用户数
     */
    private Integer factorType;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 活动开始时间时间戳
     */
    private Long startTime;
    /**
     * 活动结束时间时间戳
     */
    private Long endTime;
    /**
     * 返利规则文案
     */
    private String ruleTxt;
    /**
     * ruleType = 2 时，展示阶梯返利规则
     */
    private List<RuleStepResponse> stepRule;
    /**
     * 活动报名提示文案
     */
    private String tips;
    /**
     * 活动介绍文案
     */
    private String activityDesc;
    /**
     * 活动介绍页面跳转URL
     */
    private String activityDescUrl;
    /**
     * 活动返利信息
     */
    private RebateInfoResponse activityRebateInfo;
    /**
     * 返利活动状态, see @com.dianping.gmkt.event.api.promoqrcode.enums.RebateActivityStatus
     */
    private Integer status;

    /**
     * 隐藏详情按钮，不为空且==1时隐藏 （门店管理员查看活动&& 当前门店已报名 && 报名的不是该账户时）
     */
    private Integer hideDetailButton;

    /**
     * 管理员账号参与活动信息
     */
    private ApplyInfoResponse applyInfo;

    /**
     * 是否可提现
     */
    private boolean canWithDraw;

}
