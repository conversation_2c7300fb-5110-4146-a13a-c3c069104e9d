package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule;

import lombok.Data;

import java.io.Serializable;

/**
 * 阶梯返利规则-阶梯配置DTO
 */
@Data
public class RebateSettleRuleStepConfigDTO implements Serializable {
    private static final long serialVersionUID = 9076927852381910938L;

    /**
     * 阶梯序号
     */
    private Integer stepIndex;

    /**
     * 阶梯开始值
     */
    private Long stepStart;

    /**
     * 阶梯结束值
     */
    private Long stepEnd;

    /**
     * 返现金额
     */
    private Long rebateAmount;

    /**
     * 返现比例
     * 单位：万分之一
     */
    private Long rebateProportion;

}
