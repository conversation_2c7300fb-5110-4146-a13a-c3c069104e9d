package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.RbOrderInvalidDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.order.ScanOrderDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.UserLastOrderQueryRequest;

import java.util.List;

/**
 * 功能描述:优惠码订单服务
 *
 * <AUTHOR>
 * @date 2024/10/15
 **/
public interface PromoCodeOrderService {

    /**
     * 查询订单不返利原因
     * @param unifiedOrderId
     * @return
     */
    RemoteResponse<RbOrderInvalidDTO> queryNoRebateReason(String unifiedOrderId);

    /**
     * 添加订单不返利原因
     * @param rbOrderInvalidDto
     * @return
     */
    RemoteResponse<Boolean> addNoRebateReason(RbOrderInvalidDTO rbOrderInvalidDto);

    /**
     * 添加订单不返利原因
     * @param rbOrderInvalidDTOList
     * @return
     */
    RemoteResponse<Integer> addNoRebateReasonList(List<RbOrderInvalidDTO> rbOrderInvalidDTOList);

    /**
     * 查询用户最近一笔线下码订单
     * @param request
     * @return
     */
    RemoteResponse<ScanOrderDTO> queryUserLastOrder(UserLastOrderQueryRequest request);

    /**
     * 校验扫码订单是否需要被过滤
     * @param orderId
     * @return true：需要过滤 false：不需要过滤
     */
    RemoteResponse<Boolean> checkScanOrderFilter(String orderId);
}
