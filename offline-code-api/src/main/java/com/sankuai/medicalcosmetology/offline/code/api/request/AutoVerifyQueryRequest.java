package com.sankuai.medicalcosmetology.offline.code.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/11/6
 **/
@Data
public class AutoVerifyQueryRequest implements Serializable {

    /**
     * 平台 {@link  com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum}
     */
    private int platform;

    private long shopId;

    /**
     * 职人一客一价ID
     */
    private long staffBargainCodeId;

    private String orderId;

}
