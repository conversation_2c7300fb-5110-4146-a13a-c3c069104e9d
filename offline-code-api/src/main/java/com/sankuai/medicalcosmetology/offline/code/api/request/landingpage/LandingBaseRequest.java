package com.sankuai.medicalcosmetology.offline.code.api.request.landingpage;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpDecrypt;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.request.EnvRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/31
 * @Description: 落地页基础入参信息
 */
@Data
public class LandingBaseRequest extends EnvRequest implements Serializable {

    /**
     * 美团门店id
     */
    private Long shopId;

    /**
     * 美团门店id加密字段
     */
    @MdpDecrypt(target="shopId")
    private String shopIdEncrypt;
}
