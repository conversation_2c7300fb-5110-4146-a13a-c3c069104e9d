package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageResponseDTO;
import lombok.Data;

/**
 * 返利活动账单
 */
@Data
public class RebateActivityStatementResponse extends PageResponseDTO {
    private static final long serialVersionUID = -6295123645374854341L;

    /**
     * 账单ID
     */
    private String statementId;

    /**
     * 账单开始时间
     */
    private Long statementStartTime;

    /**
     * 账单结束时间
     */
    private Long statementEndTime;

    /**
     * 账单状态
     */
    private Integer statementStatus;

    /**
     * 账单状态描述
     */
    private String statementStatusDesc;

    /**
     * 账单总金额
     */
    private String totalAmount;
}
