package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/20
 * @Description: copy自PostpurchaseCouponRecordDO，仅测试用，上线后需删除
 */
@Data
public class PostpurchaseCouponRecordDTO implements Serializable {


    /**
     *   字段: user_id
     *   说明: 用户id，区分平台
     */
    private Long userId;

    /**
     *   字段: platform
     *   说明: 领券平台 1-点评 2-美团
     */
    private Integer platform;

    /**
     *   字段: order_id
     *   说明: 领券对应订单id
     */
    private String orderId;

    /**
     *   字段: bu_id
     *   说明: 订单门店对应buId
     */
    private Integer buId;

    private List<String> unifiedCouponIdList;

    /**
     *   字段: unified_coupon_group_id_list
     *   说明: 券批次id
     */
    private List<String> unifiedCouponGroupIdList;

    /**
     *   字段: coupon_all_unvalid
     *   说明: 购后券是否全部无效 0-有效 1-全部无效
     */
    private Integer couponAllUnvalid;

    /**
     *   字段: re_purchase_order_num
     *   说明: 领券后复购同bu订单数
     */
    private Integer rePurchaseOrderNum;

    /**
     *   字段: begin_use_time
     *   说明: 开始使用时间
     */
    private Date beginUseTime;

    /**
     *   字段: end_use_time
     *   说明: 结束使用时间
     */
    private Date endUseTime;

    /**
     *   字段: operation_config_id
     *   说明: 发券活动配置id
     */
    private Long operationConfigId;
}
