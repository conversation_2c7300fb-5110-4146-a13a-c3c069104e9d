package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 优惠码免佣/返利活动参与条件类型枚举
 */
public enum RebateActivityJoinConditionTypeEnum {
    DP_SHOP_ID(1, "点评门店白名单"),
    DP_SHOP_ITEM_ID(2, "点评门店楼层id"),
    NEW_MERCHANT(3, "新签商户"),

    CATEGORY_CITY(4, "类目+城市")
    ;

    public final int code;
    public final String desc;

    RebateActivityJoinConditionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validActivityApplyConditionType(int code) {
        for (RebateActivityJoinConditionTypeEnum type : RebateActivityJoinConditionTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateActivityJoinConditionTypeEnum getByCode(int code) {
        for (RebateActivityJoinConditionTypeEnum type : RebateActivityJoinConditionTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

}
