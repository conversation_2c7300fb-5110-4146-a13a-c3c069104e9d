package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation;

import com.sankuai.medicalcosmetology.offline.code.api.enums.ShopConfigTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商家限制-点评shopId白名单
 *
 * <AUTHOR>
 * @date 2024年10月14日 15:33:50
 */
@Data
public class ShopIdConfigLimitDTO implements Serializable {

    private Boolean limitShopId;

    private List<Long> shopIdsLong;

    /** @see ShopConfigTypeEnum
     * 默认为ShopConfigTypeEnum.SHOP_ID门店白名单
     * **/
    private int type = ShopConfigTypeEnum.SHOP_ID.getCode();

    private Long dpShopItemId;

}
