package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import com.dianping.gmkt.event.api.promoqrcode.enums.QROrderActivityApplyConditionType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ApplyConditionVO implements Serializable {
    private static final long serialVersionUID = -6552241759411403454L;

    /**
     * 参与条件类型：1 点评门店白名单
     *
     * @see QROrderActivityApplyConditionType
     */
    private Integer type;
    /**
     * 点评门店白名单列表
     */
    private List<String> dpShopIds;
    /**
     * 点评门店楼层id
     */
    private Long dpShopItemId;
    /**
     * @see com.dianping.gmkt.event.api.promoqrcode.enums.QRCityTypeEnum
     */
    private Integer cityType;
    /**
     * 点评城市 id 列表
     */
    private List<Integer> dpCityIdList;
    /**
     * 活动周期（报名后生效的时长，单位：天）
     */
    private Integer signUpDuration;
    /**
     * 报名周期（商户入驻后几天内能看到活动）
     */
    private Integer activityDisplayDuration;

    /**
     * 平台限制，1-点评，2-美团，0-双平台
     */
    private Integer platform;
    /**
     * 新签约门店是否自动加入返利活动
     */
    private Boolean newShopAutoRegister;
    /**
     * 系统导入的门店楼层
     */
    private Long newShopImportFloor;
    /**
     * 新签门店后台一级类目
     */
    private List<Integer> newShopCategoryIds;
    /**
     * 新签门店后台二级类目
     */
    private List<Integer> newShopSecondCategoryIds;
}
