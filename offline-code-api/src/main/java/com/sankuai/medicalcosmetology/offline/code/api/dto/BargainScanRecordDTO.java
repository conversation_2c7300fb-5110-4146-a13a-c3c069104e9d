package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
@Data
public class BargainScanRecordDTO implements Serializable {

    /**
     * bargain_id
     * 改价id,对应主体表主键id
     */
    private Long bargainId;

    /**
     * account_type
     * 账号类型
     */
    private Integer accountType;

    /**
     * scan_user_id
     * 扫码用户ID
     */
    private String scanUserId;

    /**
     * 扫码时间
     */
    private Date scanTime;
}