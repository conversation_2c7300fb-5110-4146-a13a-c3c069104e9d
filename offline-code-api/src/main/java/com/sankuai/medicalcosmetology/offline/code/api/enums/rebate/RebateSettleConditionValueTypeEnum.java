
package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 订单返利结算条件值类型
 */
public enum RebateSettleConditionValueTypeEnum {

    VALUE(1, "值"),
    VAL(2, "变量");

    public Integer code;
    public String message;

    RebateSettleConditionValueTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static boolean validRebateSettleConditionValueType(int code) {
        for (RebateSettleConditionValueTypeEnum type : RebateSettleConditionValueTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateSettleConditionValueTypeEnum getByCode(int code) {
        for (RebateSettleConditionValueTypeEnum type : RebateSettleConditionValueTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("error code for RebateSettleConditionValueType.");
    }

}
