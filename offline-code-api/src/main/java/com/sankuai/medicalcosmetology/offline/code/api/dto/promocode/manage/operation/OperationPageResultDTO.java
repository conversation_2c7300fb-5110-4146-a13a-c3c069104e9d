package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.PageResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @Description:【发券活动配置】查询结果
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperationPageResultDTO extends PageResponseDTO {

    /**
     * 发券活动信息
     */
    List<Object> list;

}
