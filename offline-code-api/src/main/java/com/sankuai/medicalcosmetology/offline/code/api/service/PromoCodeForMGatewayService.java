package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.AccountFetchInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.OrderQueryForMDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.common.DictDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.common.TreeNodeDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity.*;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.*;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationPageQueryDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationPageResultDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.ApprovalUrlQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.RebateQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.response.ApprovalUrlQueryResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateQueryResponse;

import java.nio.ByteBuffer;
import java.util.List;

public interface PromoCodeForMGatewayService {
    RemoteResponse<OrderQueryForMDTO> queryOrderForManager(String orderId);

    RemoteResponse<List<OrderQueryForMDTO>> queryOrderForManagerToList(String orderId);

    // ----- 商家物料管理 START -----//
    /**
     * 分页查询优惠码列表
     *
     * @param request 查询条件封装的DTO
     * @return 返回查询结果，包括分页信息和列表数据
     */
    RemoteResponse<MerchantMaterialPageResultDTO> pageQueryPromoCodeList(MerchantMaterialPageQueryDTO request);

    /**
     * 编辑优惠码备注信息
     *
     * @param request 包含促销码ID和新备注信息的DTO
     * @return 返回操作结果，成功为true，失败为false
     */
    RemoteResponse<Boolean> editPromoCodeMemo(EditMemoDTO request);

    /**
     * 查询优惠码详细信息
     *
     * @param promoQRCodeId 优惠码的ID
     * @return 返回优惠码详细信息的封装对象，包括优惠码的使用状态、有效期等信息
     */
    RemoteResponse<MerchantMaterialDetailDTO> queryPromoCodeDetail(Long promoQRCodeId);

    /**
     * 获取后端类别树
     *
     * @return 返回后端类别的树形结构数据，用于展示类别的层级关系
     */
    RemoteResponse<TreeNodeDTO> getBackendCategoryTree();

    /**
     * 获取点评城市列表
     *
     * @return 返回后端类别的树形结构数据，用于展示类别的层级关系
     */
    RemoteResponse<List<DictDTO>> getAllDpCities();

    /**
     * 上传文件
     *
     * @param byteBuffer 文件内容的字节缓冲区
     * @param sceneType 场景类型，用于区分不同的上传场景
     * @return RemoteResponse<String> 返回远程响应，包含上传后的文件URL或标识符
     */
    RemoteResponse<String> uploadFile(ByteBuffer byteBuffer, Integer sceneType);

    /**
     * 导出文件
     *
     * @param request 导出文件的上下文信息，包含导出的具体参数和设置
     * @return 返回远程响应，包含导出文件的URL或其他标识符
     */
    RemoteResponse<String> exportFile(FileExportContext request);
    // ----- 商家物料管理 END -----//

    // ----- 活动配置管理 START -----//
    /**
     * 活动创建
     *
     * @param activityInfo 活动信息
     * @return 返回操作结果，成功为true，失败为false
     */
    RemoteResponse<Boolean> activityCreate(OperationInfoDTO activityInfo);

    /**
     * 活动上下线
     * 1：上线 2：下线
     * TODO 临时，上线后删除
     * @return 返回操作结果，成功为true，失败为false
     */
    RemoteResponse<Boolean> activityUpdateOnlineStatus(Long activityId, Integer onlineStatus);

    /**
     * 活动上下线
     * 1：上线 2：下线
     *
     * @return 返回操作结果，成功为true，失败为false
     */
    RemoteResponse<Boolean> activityUpdateOnlineStatus(Long activityId, Integer activityType, Integer onlineStatus);

    /**
     * 活动编辑
     *
     * @param activityInfo
     * @return
     */
    RemoteResponse<Boolean> activityUpdate(OperationInfoDTO activityInfo);

    /**
     * 分页查询活动列表
     *
     * @param context 查询条件封装的DTO，包含分页参数及筛选条件
     * @return 返回分页查询结果，包括分页信息和活动列表数据
     */
    RemoteResponse<OperationPageResultDTO> activityPageQuery(OperationPageQueryDTO context);

    /**
     * 查询活动的详细信息
     * TODO 临时，上线后删除
     * @param activityId 活动ID，用于唯一标识一个发券活动
     * @return 返回远程响应，包含活动的详细信息，如活动名称、活动时间、参与条件等
     */
    RemoteResponse<OperationInfoDTO> activityDetailQuery(Long activityId);

    /**
     * 查询活动的详细信息
     *
     * @param activityId 活动ID，用于唯一标识一个发券活动
     * @return 返回远程响应，包含活动的详细信息，如活动名称、活动时间、参与条件等
     */
    RemoteResponse<OperationInfoDTO> activityDetailQuery(Long activityId, Integer activityType);

    /**
     * 查询门店的业务部、类目、城市等信息
     *
     * @param dpShopId 点评门店ID
     * @return 返回远程响应，包含门店类目信息
     */
    String queryShopCategoryInfoByDpShopId(Long dpShopId);
    // ----- 活动配置管理 END -----//

    // ----- BIZ -----//

    /**
     * 根据彩虹抽奖活动id获取活动信息
     *
     * @param eCode 抽奖活动id
     * @return 返回远程响应，包含奖励信息的封装对象，如奖励类型、奖励金额等
     */
    RemoteResponse<AwardActivityDTO> queryAwardInfoByECode(String eCode, Integer awardType);

    /**
     * 查询提现账号信息
     *
     * @param dpAccountId 点评账号id
     * @param activityId  活动id，这个为彩虹抽奖活动id
     */
    RemoteResponse<List<AccountFetchInfoDTO>> queryAccountFetchInfo(Long dpAccountId, Long activityId);


    /**
     * 根据场景查询业务部门列表
     *
     * @param scene 场景标识，用于区分不同的业务场景
     * @return RemoteResponse<List<DictDTO>> 返回远程响应，包含业务部门列表，每个部门以DictDTO形式表示
     */
    RemoteResponse<List<DictDTO>> queryBusinessDepartmentByScene(Integer scene);

    // ----- 职人返佣 & 促评活动 START -----//

    /**
     * 获取返佣活动的黑名单列表
     *
     * @param activityId 返佣活动ID，用于唯一标识一个返佣活动
     * @return 返回远程响应，包含该活动下的黑名单用户ID列表（Long类型）
     */
    RemoteResponse<List<Long>> getRebateActivityBlackList(Long activityId);

    /**
     * 更新返佣活动的黑名单列表
     *
     * @param activityId 返佣活动ID，用于唯一标识一个返佣活动
     * @param blackList  需要设置的黑名单用户ID列表，多个ID以逗号分隔的字符串形式传递
     * @return 返回远程响应，操作成功返回true，失败返回false
     */
    RemoteResponse<Boolean> updateRebateActivityBlackList(Long activityId, String blackList);

    /**
     * 创建订单返佣活动
     *
     * @param orderActivityDTO 订单返佣活动的数据传输对象，包含订单及返佣活动的相关信息
     * @return 返回远程响应，操作成功返回true，失败返回false
     */
    RemoteResponse<Boolean> createOrderActivity(OrderActivityVO orderActivityDTO);


    /**
     * 根据活动ID获取订单返佣活动的详细信息
     *
     * @param activityId 订单返佣活动ID
     * @return 返回远程响应，包含该活动的详细信息（OrderActivityVO）
     */
    RemoteResponse<OrderActivityVO> getOrderActivityById(Long activityId);

    /**
     * 根据查询条件分页获取订单返佣活动列表
     *
     * @param orderActivityPageQueryDTO 查询条件封装的DTO，包含分页参数及筛选条件
     * @return 返回远程响应，包含分页结果（OrderActivityPageResultDTO），包括活动列表及分页信息
     */
    RemoteResponse<OrderActivityPageResultVO> getOrderActivityPageWithCondition(OrderActivityPageQueryVO orderActivityPageQueryDTO);

    /**
     * 更新订单返佣活动信息
     *
     * @param activityId 订单返佣活动ID，用于唯一标识一个返佣活动
     * @param activityVO 订单返佣活动的数据传输对象，包含需要更新的活动信息
     * @return 返回远程响应，操作成功返回true，失败返回false
     */
    RemoteResponse<Boolean> updateOrderActivity(Long activityId, OrderActivityVO activityVO);


    /**
     * 更新订单返佣活动的状态
     *
     * @param activityId 订单返佣活动ID，用于唯一标识一个返佣活动
     * @param operateType 操作类型，例如1表示启用，2表示禁用等
     * @return 返回远程响应，操作成功返回true，失败返回false
     */
    RemoteResponse<Boolean> updateOrderActivityStatus(Long activityId, Integer operateType);


    /**
     * 获取打款业务线列表
     * @param activityType 活动类型
     * @return 返回远程响应，包含打款业务线列表
     */
    RemoteResponse<List<PaymentBizLineVO>> loadPaymentBizLine(Integer activityType);


    /**
     * 取后台类目树，仅包含一级类目和二级类目
     * @return 返回远程响应，包含后台类目树
     */
    RemoteResponse<BackCategoryTreeVO> getCategoryTree();

    /**
     * 查询返佣活动详情
     * @param request 请求参数
     * @return 佣金记录详情
     */
    RemoteResponse<RebateQueryResponse> rebateQuery(RebateQueryRequest request);

    // ----- 职人返佣 & 促评活动 END -----//

    /**
     * 查询审批URL
     * 接口路径：/offlinecode/m/approval-url/query
     * @param request 审批URL查询请求
     * @return 审批URL响应
     */
    RemoteResponse<ApprovalUrlQueryResponse> queryApprovalUrl(ApprovalUrlQueryRequest request);

}
