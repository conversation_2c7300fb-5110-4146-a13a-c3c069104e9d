package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description: 领券信息模块
 */
@Data
public class DrawCouponResultInfo implements Serializable {

    /**
     * 领券banner图
     */
    private String drawBannerPicUrl;

    /**
     * 券信息
     */
    private List<ScanCouponInfo> couponList;

    /**
     * 所有券中最近的过期时间
     */
    private Date latestExpireTime;
}
