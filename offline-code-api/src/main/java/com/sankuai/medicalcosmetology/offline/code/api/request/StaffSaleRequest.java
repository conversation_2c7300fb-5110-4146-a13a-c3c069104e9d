package com.sankuai.medicalcosmetology.offline.code.api.request;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;
/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/7
 * @Description:
 */
@NoArgsConstructor
@Data
public class StaffSaleRequest implements Serializable {
    /**
     * 点评门店id列表
     */
    private List<Long> dpShopIdList;
    private Long startTime;
    private Long endTime;
    /**
     * 职人id（手艺人id）
     */
    private List<Long> staffIdList;
    /**
     * 统一订单id
     */
    private String orderId;
    /**
     * 订单状态
     *
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.StaffOrderStatusEnum
     */
    private Integer orderStatus = 0;
}