package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class RebateOrderPriceProportionRuleVO implements Serializable {

    private static final long serialVersionUID = -1555736540125481622L;

    /**
     * 最高返现金额
     */
    private Long limitRebateAmount;

    /**
     * 返现比例
     */
    private Long rebateProportion;
    /**
     * 返现条件
     */
    private List<RebateSettlePriceProportionConditionVO> rebateConditions;
}
