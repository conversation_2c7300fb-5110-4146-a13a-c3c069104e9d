package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 奖励配置信息
 *
 * <AUTHOR>
 * @date 2024年10月14日 14:39:57
 */
@Data
public class AwardActivityDTO implements Serializable {

    /**
     * 活动code
     */
    private String code;

    /**
     * 活动对应的平台，1-点评，2-美团
     * @see PlatformEnum
     */
    private Integer platform;

    private Boolean valid;

    private String name;

    private Date startTime;

    private Date endTime;

}
