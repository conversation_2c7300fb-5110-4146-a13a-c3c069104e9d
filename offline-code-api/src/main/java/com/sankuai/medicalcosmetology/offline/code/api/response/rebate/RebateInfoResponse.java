package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 门店返利信息
 */
@Data
public class RebateInfoResponse implements Serializable {
    private static final long serialVersionUID = -8818753539155695965L;

    /**
     * 返利用户数
     */
    private Integer rebateUserCnt;
    /**
     * 返利订单数
     */
    private Integer rebateOrderCnt;
    /**
     * 返利金额（分）
     */
    private Long rebateAmount;
    /**
     * 待兑换收益（分）
     */
    private Long remainAmount;
    /**
     * 过期未提现收益（分）
     */
    private Long expiredAmount;

    public RebateInfoResponse init() {
        this.rebateUserCnt = 0;
        this.rebateOrderCnt = 0;
        this.rebateAmount = 0L;
        this.remainAmount = 0L;
        this.expiredAmount = 0L;
        return this;
    }

}
