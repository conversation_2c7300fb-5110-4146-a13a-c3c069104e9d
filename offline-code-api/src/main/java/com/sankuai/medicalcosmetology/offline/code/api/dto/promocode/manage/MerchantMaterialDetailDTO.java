package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/9/18
 * @Description:商家物料详情
 */
@Data
public class MerchantMaterialDetailDTO extends MerchantMaterialInfoDTO implements Serializable {

    /**
     * 美团门店名称
     */
    private String mtShopName;

    /**
     * 二次申请说明
     */
    private String applyReason;

    /**
     * 邮寄姓名
     */
    private String applyName;

    /**
     * 邮寄电话
     */
    private String applyMobileNo;

    /**
     * 所在地区
     */
    private String regionName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 快递公司code
     */
    private String express;

    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 快递状态
     */
    private String expressStatus;

    /**
     * 快递最新简要信息
     */
    private String expressContext;

}
