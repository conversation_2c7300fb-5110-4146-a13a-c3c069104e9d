package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@Data
public abstract class PageRequestDTO implements Serializable {
    private static final long serialVersionUID = -983442032678217925L;

    //分页查询-页数
    private Integer pageNum;
    //分页查询-页面大小
    private Integer pageSize;

    public Long getOffset() {
        Long offset = (long)pageSize * (long)(pageNum - 1);
        return offset > 0 ? offset : 0;
    }

}
