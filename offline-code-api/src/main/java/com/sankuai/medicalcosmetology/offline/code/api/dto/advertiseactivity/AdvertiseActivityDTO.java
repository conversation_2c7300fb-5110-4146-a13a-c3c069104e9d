package com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AdvertiseActivityDTO implements Serializable {

    private Long id;
    private String activityName;
    private String activityDesc;
    /**
     * 目前只需要1 表示投放活动 AdvertiseActivityTypeEnum
     */
    private Integer activityType;
    private String sourceName;
    /**
     * 资源位id 根据资源位名称唯一生成 无需传入
     */
    private Long sourceId;
    private Integer bizType;
    /**
     * 一级类目限制
     */
    private String categoryId;
    /**
     * 二级类目限制
     */
    private String categoryId2;
    /**
     * 内容类型 目前只需1，支持优惠码开店宝首页运营位
     */
    private Integer contentType;
    /**
     * 企微图片url
     */
    private String content;
    private Date startTime;
    private Date endTime;
    private CityConfigDTO cityConfig;
    private ShopIdConfigDTO shopIdConfig;
    private String link;
    private String creator;
    /**
     * com.dianping.gmkt.event.api.promoqrcode.enums.QRActivityStatus
     */
    private Integer status;
    private String title;
    private String subTitle;
    private PicConfigDTO picConfig;
    private PicConfigDTO bannerPicConfig;
    private Long createTime;
    private Long updateTime;
}
