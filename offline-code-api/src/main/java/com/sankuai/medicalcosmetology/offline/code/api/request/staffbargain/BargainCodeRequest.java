package com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain;

import com.sankuai.medicalcosmetology.offline.code.api.dto.ProductDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:47
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BargainCodeRequest implements Serializable {

    private Long staffId;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.BargainStaffTypeEnum
     */
    private Integer staffType;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;

    /**
     * 对应平台下的用户id
     */
    private Long userId;

    /**
     * 商品列表
     */
    private List<ProductDTO> productList;

    /**
     * 订单实际支付金额
     */
    private BigDecimal bargainPrice;

    /**
     * 服务内容图片列表
     */
    private List<String> picUrlList;

    /**
     * 服务内容文字
     */
    private String text;
}
