package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description: 落地页配置信息
 */
@Data
public class LandingPageConfigInfo implements Serializable {

    /**
     * 货架加载配置
     */
    private ShelfLoadConfig shelfLoadConfig;

    /**
     * 落地页banner图
     */
    private String bannerPicUrl;

    /**
     * 领券banner图
     */
    private String drawBannerPicUrl;

    /**
     * 是否需要查询品牌码附近门店
     */
    private Boolean needQueryNearby;

    /**
     * 置顶团单id
     */
    private Long topDealId;

    /**
     * 领券活动id
     */
    private Long activityId;

    /**
     * 标识map的json格式
     */
    private String flagMapStr;
}
