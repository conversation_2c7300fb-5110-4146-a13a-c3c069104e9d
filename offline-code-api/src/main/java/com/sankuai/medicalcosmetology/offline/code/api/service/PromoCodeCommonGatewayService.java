package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.BrandCodeDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.EmptyCodeInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.TitleAndDataDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.EmptyCodeBindInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.StaffSaleRequest;

import java.util.List;

/**
 * @Author: fuchangming
 * @Date: 2024/4/7
 * @Description: 优惠码通用网关服务
 */
public interface PromoCodeCommonGatewayService {

    /**
     * 空码绑定
     * 
     * @param request 绑定信息请求体
     * @return 绑定结果
     */
    RemoteResponse<Boolean> bindFromEmptyCode(EmptyCodeBindInfoRequest request);

}
