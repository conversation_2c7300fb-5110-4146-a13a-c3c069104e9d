package com.sankuai.medicalcosmetology.offline.code.api.enums;

import com.sankuai.medicalcosmetology.offline.code.api.constants.ApprovalConstant;

/**
 * 审批类型枚举
 */
public enum ApprovalTypeEnum {
    
    X1("x1", "X1审批", ApprovalConstant.ACTIVITY_X1_AUDIT_URL),
    FINANCE("finance", "财务审批", ApprovalConstant.ACTIVITY_FINANCE_BP_AUDIT_URL);
    
    private String code;
    private String desc;
    private String auditUrlKey;
    
    ApprovalTypeEnum(String code, String desc, String auditUrlKey) {
        this.code = code;
        this.desc = desc;
        this.auditUrlKey = auditUrlKey;
    }
    
    public static ApprovalTypeEnum fromCode(String code) {
        for (ApprovalTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public String getAuditUrlKey() {
        return auditUrlKey;
    }
} 