package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/25
 * @Description: 货架加载配置
 */
@Data
public class ShelfLoadConfig implements Serializable {

    /**
     * 是否加载手艺人模块
     */
    private Boolean loadCraftsman = false;

    /**
     * 手艺人模块优先级
     */
    private Integer craftsmanPriority = -1;

    /**
     * 是否加载次卡货架
     */
    private Boolean loadChanceCard = false;

    /**
     * 次卡货架优先级
     */
    private Integer chanceCardPriority = -1;

    /**
     * 是否加载团购货架
     */
    private Boolean loadGroupPurchase = false;

    /**
     * 是否加载新版团购货架
     */
    private Boolean loadNewGroupPurchase = false;

    /**
     * 是否加载预付货架
     */
    private Boolean loadPrePay = false;

    /**
     * 是否加载自定义团购货架
     */
    private Boolean loadCustomizeGroupPurchase = false;

    /**
     * 是否导入职人绑定的商品服务货架，丽人职人码定制
     */
    private Boolean loadStaffBindPurchase = false;
}
