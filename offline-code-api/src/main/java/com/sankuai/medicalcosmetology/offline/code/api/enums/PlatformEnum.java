package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: chen<PERSON>yang02
 * @Date: 2024/4/22
 * @Description:
 */
@Getter
public enum PlatformEnum {

    UNKNOWN(0, "未知类型"),

    DP(1, "点评"),

    MT(2, "美团");

    private final int code;
    private final String desc;

    PlatformEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PlatformEnum fromCode(int code) {
        for (PlatformEnum enumValue : PlatformEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }

    public static boolean isPlatformLegal(int platform) {
        for (PlatformEnum value : PlatformEnum.values()) {
            if (platform == value.getCode() && platform != UNKNOWN.getCode())
                return true;
        }
        return false;
    }
}
