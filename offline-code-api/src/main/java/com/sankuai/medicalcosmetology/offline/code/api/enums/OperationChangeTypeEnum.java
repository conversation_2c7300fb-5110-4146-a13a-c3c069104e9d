package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * @Author: fuchanmging
 * @Date: 2024/10/16
 * @Description:
 */
@Getter
public enum OperationChangeTypeEnum {

    ADD(1, "新增"),
    UPDATE(2, "编辑"),
    ONLINE(3, "上线"),
    OFFLINE(4, "下线");

    public int code;
    public String desc;

    private OperationChangeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
