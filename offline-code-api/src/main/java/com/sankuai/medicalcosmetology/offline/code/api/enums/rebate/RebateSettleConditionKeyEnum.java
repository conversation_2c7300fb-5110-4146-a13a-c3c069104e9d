
package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 订单返利条件key枚举
 */
public enum RebateSettleConditionKeyEnum {
    ORDER_PRICE(1, "orderPrice", "订单实付金额"),
    SCAN_TIME(2, "scanTime", "扫码时间（小时）"),
    DEAL_ITEM_ID(3, "dealItemId", "团单楼层id-限制团购订单团单"),

    /**
     * 膨胀类型具体值参考 {@link GodCouponRebateExpansionTypeEnum}
     */
    EXPANSION_TYPE(4, "expansionType", "膨胀类型"),
    ;

    public Integer code;
    public String value;
    public String message;

    RebateSettleConditionKeyEnum(Integer code, String value, String message) {
        this.code = code;
        this.value = value;
        this.message = message;
    }

    public static boolean validRebateSettleConditionKey(int code) {
        for (RebateSettleConditionKeyEnum key : RebateSettleConditionKeyEnum.values()) {
            if (key.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateSettleConditionKeyEnum getByCode(int code) {
        for (RebateSettleConditionKeyEnum key : RebateSettleConditionKeyEnum.values()) {
            if (key.code == code) {
                return key;
            }
        }
        throw new IllegalArgumentException("error code for RebateSettleConditionKey.");
    }

}
