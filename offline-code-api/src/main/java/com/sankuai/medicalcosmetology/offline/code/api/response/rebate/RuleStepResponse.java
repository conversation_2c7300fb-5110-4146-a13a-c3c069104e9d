package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 阶梯返利规则
 */
@Data
public class RuleStepResponse implements Serializable {
    private static final long serialVersionUID = 1686369734499970396L;
    /**
     * 阶梯返利区间index
     */
    private Integer index;
    /**
     * 阶梯返利区间start
     */
    private Long start;
    /**
     * 阶梯返利区间end
     */
    private Long end;
    /**
     * 返利金额（分）
     */
    private Long rebateAmount;
}
