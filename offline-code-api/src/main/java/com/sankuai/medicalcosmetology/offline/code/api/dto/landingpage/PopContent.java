package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/22
 * @Description: 弹框内容
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PopContent implements Serializable {

    /**
     * 弹框标题
     */
    private String title;

    /**
     * 文案内容
     */
    private List<TextContent> textContentList;
}
