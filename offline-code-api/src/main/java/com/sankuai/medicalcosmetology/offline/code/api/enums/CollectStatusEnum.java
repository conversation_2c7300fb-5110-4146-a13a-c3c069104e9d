package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/15
 * @Description: 收藏状态枚举
 */
public enum CollectStatusEnum {

    ADD(0, "添加收藏"),
    DEL(1, "删除收藏");

    public final int code;
    public final String desc;

    CollectStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CollectStatusEnum getByCode(Integer code) {
        for (CollectStatusEnum collectStatusEnum : CollectStatusEnum.values()) {
            if (collectStatusEnum.code == code) {
                return collectStatusEnum;
            }
        }
        return null;
    }

    public static boolean validateCode(Integer code) {
        return getByCode(code) != null;
    }
}
