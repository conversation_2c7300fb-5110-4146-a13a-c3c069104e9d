package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.BrandCodePoiDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.CityBasicInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.*;
import com.sankuai.medicalcosmetology.offline.code.api.request.BrandCodePoiQueryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.EnvRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.PvPointRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.UserPostPurchaseCouponForGatewayRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.*;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/19
 * @Description: 优惠码C端网关服务
 */
public interface PromoCodeForCGatewayService {

    /**
     * 查询品牌码门店列表
     * @param request
     * @return
     */
    RemoteResponse<List<BrandCodePoiDTO>> queryBrandPoiList(BrandCodePoiQueryRequest request);

    /**
     * 查询附近的品牌码门店列表（小于1.5km）
     * @param request
     * @return
     */
    RemoteResponse<List<BrandCodePoiDTO>> queryNearbyBrandPoiList(BrandCodePoiQueryRequest request);

    /**
     * 查询所有地级市城市列表，区分平台
     * @param request
     * @return
     */
    RemoteResponse<List<CityBasicInfoDTO>> getAllTopLevelCityInfo(EnvRequest request);

    /**
     * 查询所有开站城市列表（大陆），区分平台
     * @param request
     * @return
     */
    RemoteResponse<List<CityBasicInfoDTO>> getAllOpenCityInfo(EnvRequest request);

    /**
     * 根据cityId和平台查询地级市城市名称
     * @param request
     * @return
     */
    RemoteResponse<String> getCityName(EnvRequest request);

    /**
     * 线下码落地页信息查询
     * @param request
     * @return
     */
    RemoteResponse<LandingPageInfoDTO> queryLandingInfo(LandingInfoRequest request);

    /**
     * 领券并且查询券信息
     * @param request
     * @return
     */
    RemoteResponse<LandingPageCouponInfo> drawAndQueryCoupon(LandingCouponRequest request);

    /**
     * 收藏美团门店
     * @param request
     * @return
     */
    RemoteResponse<Boolean> collectMtShop(CollectShopRequest request);

    /**
     * 领取门店收藏礼
     * @param request
     * @return
     */
    RemoteResponse<Boolean> getCollectionGift(LandingBaseRequest request);

    /**
     * 使用门店收藏礼
     * @param request
     * @return
     */
    RemoteResponse<Boolean> useCollectionGift(LandingBaseRequest request);

    /**
     * 查询落地页顶部模块（门店信息、职人信息）
     * @return
     */
    RemoteResponse<LandingPageModuleInfoDTO> queryTopModule(LandingTopModuleRequest request);

    /**
     * 查询落地页次卡模块
     * 逻辑：
     *   1 查询职人的次卡信息
     *   2 查门店次卡信息
     *   3 去重
     *   *
     * @return
     */
    RemoteResponse<TimeCardModuleInfo> queryTimeCardModule(TimeCardModuleRequest request);

    /**
     * 查询落地页子模块（收藏礼、企微模块、wifi模块）
     * @param request
     * @return
     */
    RemoteResponse<LandingPageSubModuleInfoDTO> querySubModule(LandingSubModuleRequest request);

    /**
     * 查询落地页浮层（目前仅促评信息）
     * @param request
     * @return
     */
    RemoteResponse<LandingPageFloatingLayerDTO> queryFloatingLayer(LandingFloatingLayerRequest request);

    /**
     * 查询落地页ab测试结果
     * @param request
     * @return
     */
    RemoteResponse<QueryExpABInfoResultDTO> queryExpABInfo(QueryExpABInfoRequest request);

    /**
     * 查询落地页所有ab实验结果（前端无需传入实验id）
     * @param request
     * @return
     */
    RemoteResponse<List<QueryExpABInfoResultDTO>> queryAllExpABInfo(QueryExpABInfoRequest request);

    RemoteResponse<LandingPageCouponInfo> drawCouponByTechnician(LandingTechnicianRequest request);

    RemoteResponse<Boolean> recordScanInfoByTechnician(LandingTechnicianRequest request);

    RemoteResponse<Boolean> pvPoint(PvPointRequest request);

    RemoteResponse<QueryCouponResultInfo> drawOrQueryUserPostPurchaseCoupon(UserPostPurchaseCouponForGatewayRequest request);

    /**
     * 查询用户最近一次扫码记录,组装成落地页跳转地址
     */
    RemoteResponse<PromoCodeLandingUrlDTO> findUserRecentScanLandingUrl(Integer platform);
}
