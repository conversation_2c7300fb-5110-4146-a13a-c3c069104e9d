package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * 业务类型
 *
 * <AUTHOR>
 * @date 2024年10月14日 10:32:22
 */
public enum BizTypeEnum {
    PLATFORM(1, "平台"),
    ENTERTAINMENT(2, "休娱"),
    BEAUTY(3, "丽人"),
    MEDICAL_BEAUTY(4, "医美"),
    CHILD(5, "亲子"),
    LIFE_EVENT(6, "生活服务"),
    PET(7, "宠物"),
    PHOTOGRAPH(8, "快照"),
    EDUCATE(9, "学培"),
    CHANNEL(10, "渠道"),
    MEDICAL_TREATMENT(11, "医疗"),
    CAR(12, "爱车"),
    WEDDING(13, "结婚"),
    DECORATION(14, "家装");

    public final int code;
    public final String desc;

    BizTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validQRBizType(int code) {
        for (BizTypeEnum type : BizTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static BizTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BizTypeEnum type : BizTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

}
