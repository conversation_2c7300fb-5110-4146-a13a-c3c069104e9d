package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageResponseDTO;
import lombok.Data;

/**
 * 返利活动账单明细
 */
@Data
public class RebateActivityStatementTraceResponse extends PageResponseDTO {


    private static final long serialVersionUID = 6218584065081699714L;

    /**
     * 记录类型
     */
    private String traceType;

    /**
     * 记录类型描述
     */
    private String traceTypeDesc;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 券id
     */
    private String orderItemId;

    /**
     * 记录时间
     */
    private Long traceTime;

    /**
     * 佣金金额
     */
    private String commission;

    /**
     * 未返利原因
     */
    private String invalidReason;
}
