package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/11
 * @Description:扩展字段key枚举
 */
@Getter
@AllArgsConstructor
public enum OperationExtKeyEnum {

    MAGIC_TYPE("magicType", "神券接入方式");

    private final String key;

    private final String desc;

    public static OperationExtKeyEnum fromKey(String key) {
        for (OperationExtKeyEnum value : OperationExtKeyEnum.values()) {
            if (value.getKey().equals(key)) {
                return value;
            }
        }
        return null;
    }
}
