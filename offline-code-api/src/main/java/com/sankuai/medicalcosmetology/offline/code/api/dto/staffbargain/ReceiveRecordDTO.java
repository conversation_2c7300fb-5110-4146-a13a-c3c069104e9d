package com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/6/11
 * @Description:
 */
@Data
public class ReceiveRecordDTO implements Serializable {

    /**
     * 职人一客一价表主键id
     */
    private Long staffBargainId;

    /**
     * 领取用户id
     */
    private Long userId;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;

    /**
     * 领取时间
     */
    private Date receiveTime;
}
