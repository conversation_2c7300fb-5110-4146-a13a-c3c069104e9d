package com.sankuai.medicalcosmetology.offline.code.api.request;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/10
 * @Description:
 */
@Data
public class UserLastOrderQueryRequest implements Serializable {

    /**
     * 用户id，区分平台
     */
    private Long userId;

    /**
     * 平台
     * @see PlatformEnum
     */
    private Integer platform;

    /**
     * 用户美团设备唯一标识，ab实验用
     */
    private String uuId;
}