package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/9 16:38
 */
@Getter
@AllArgsConstructor
public enum BargainStaffTypeEnum {

    UNKNOWN(0, "", "未知"),
    STAFF(1, "staff", "职人"),
    CONSULTANT_TASK(2, "private_live", "咨询师任务"),
    ;


    public final int code;
    public final String scene;
    public final String desc;

    public static BargainStaffTypeEnum getByCode(Integer code) {
        return Arrays.stream(BargainStaffTypeEnum.values())
                .filter(e -> Objects.equals(e.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public boolean is(String scene) {
        return this.scene.equals(scene);
    }
}
