package com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpDecrypt;
import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpEncrypt;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ProductDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/6 14:29
 */
@Data
public class StaffBargainCodeRequest implements Serializable {
    /**
     * 职人id、分业务场景
     */
    private Long staffId;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.BargainStaffTypeEnum
     */
    private Integer staffType;

    /**
     * 平台 1-点评 2-美团
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private Integer platform;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 门店id密文字段
     */
    @MdpDecrypt(target="shopId")
    private String shopIdEncrypt;

    /**
     * 商品列表
     */
    private List<ProductDTO> productList;

    /**
     * 团购id
     */
    @Deprecated
    private Long dealGroupId;

    /**
     * 订单实际支付金额
     */
    @Deprecated
    private Integer bargainPrice;


    private BigDecimal bargainPriceBigDecimal;
    /**
     * 服务内容图片列表
     */
    private List<String> picUrlList;

    /**
     * 服务内容文字
     */
    private String text;

    /**
     * 二维码来源
     */
    private Integer qrCodeSource;

    /**
     * 二维码创建人，具体设置的值与qrCodeSource有关
     */
    private String createUser;
}
