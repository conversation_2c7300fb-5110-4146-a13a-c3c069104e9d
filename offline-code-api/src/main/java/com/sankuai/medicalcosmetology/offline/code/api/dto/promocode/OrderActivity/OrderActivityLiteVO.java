package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class OrderActivityLiteVO implements Serializable {
    private static final long serialVersionUID = -3333036485116061679L;

    private Long id;
    private Integer activityType;
    private Integer subType;
    private Integer bizType;
    private String activityName;
    private Long startTime;
    private Long endTime;
    private String approvalDesc;
    private String creator;
    private String updater;
    private Integer status;
    private Integer categoryId;
    private List<Integer> categoryId2;
    private Integer priority;
}
