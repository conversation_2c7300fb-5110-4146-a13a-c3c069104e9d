package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate;

import lombok.Data;

import java.io.Serializable;

/**
 * 促评规则
 **/
@Data
public class ReviewRuleDTO implements Serializable {

    /**
     * 是否为优惠码订单
     */
    private Boolean promoCodeOrder = true;
    /**
     * 是否已经完成评价
     */
    private Boolean hasComment = true;
    /**
     * 内容要求字数限制，>=15
     */
    private Integer contentSize = 15;
    /**
     * 奖品信息
     */
    private ReviewAwardDTO reviewAward;

    /**
     * 订单门槛价格，单位元
     */
    private Integer orderThresholdPrice;

}
