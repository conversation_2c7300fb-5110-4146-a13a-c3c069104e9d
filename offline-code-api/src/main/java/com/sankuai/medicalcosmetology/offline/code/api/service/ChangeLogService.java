package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.RegistrationChangeLogDTO;

import java.util.List;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2024/4/17
 **/
public interface ChangeLogService {

    /**
     * 批量插入报名变更日志
     *
     * @param registrationChangeLogDTOS 挂号变更日志DTO列表
     * @return 远程响应，表示是否成功插入挂号变更日志
     */
    RemoteResponse<Boolean> batchInsertRigistrationLog(List<RegistrationChangeLogDTO> registrationChangeLogDTOS);

    /**
     * 批量查询报名变更日志
     *
     * @param registrationEntityId
     * @param registrationEntityType
     * @return 远程响应，表示是否成功插入挂号变更日志
     */
    RemoteResponse<List<RegistrationChangeLogDTO>> batchGetRegistrationLog(Long registrationEntityId, Integer registrationEntityType);

    /**
     * 批量查询报名变更日志,机器人
     *
     * @param registrationEntityId
     * @param registrationEntityType
     * @return 远程响应，表示是否成功插入挂号变更日志
     */
    RemoteResponse<String> batchGetRegistrationLogForBot(String registrationEntityId, String registrationEntityType);

}
