package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardInfoDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/26
 * @Description:内容及资源配置
 */
@Data
public class ResourceDTO implements Serializable {

    /** 资源类型 */
    private Integer resourceType;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subTitle;

    /** 横幅图片 */
    private PicConfigDTO bannerPic;

    /** 背景图片 */
    private PicConfigDTO backgroundPic;

    /** 跳转链接 */
    private String jumpUrl;

    /** 二维码图片 */
    private String qrCodePicture;

    /** 交易配置 */
    private String dealConfig;

    /** 券信息 */
    private AwardInfoDTO awardInfo;

    /** 添加时间 */
    private Long addTime;

    /** 更新时间 */
    private Long updateTime;

}
