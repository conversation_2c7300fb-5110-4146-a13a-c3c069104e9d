package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

@Getter
public enum NotRebateRecordTypeEnum {

    UNKNOWN(0, "未知"),

    STORE_CODE(1, "门店返利"),
    STAFF_CODE(2, "职人返利");

    private final int code;

    private final String desc;

    NotRebateRecordTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static NotRebateRecordTypeEnum fromCode(int code) {
        for (NotRebateRecordTypeEnum enumValue : NotRebateRecordTypeEnum.values()) {
            if (enumValue.getCode() == code) return enumValue;
        }
        return UNKNOWN;
    }
}
