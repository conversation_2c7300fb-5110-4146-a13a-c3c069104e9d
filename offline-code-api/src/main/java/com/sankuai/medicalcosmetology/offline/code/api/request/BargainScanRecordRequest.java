package com.sankuai.medicalcosmetology.offline.code.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/26
 */
@Data
public class BargainScanRecordRequest implements Serializable {
    /**
     * 改价主体表主键id
     */
    private Long bargainId;

    /**
     * 扫码用户id
     */
    private String scanUserId;

    /**
     * account_type
     * 账号类型
     */
    private Integer accountType;
}