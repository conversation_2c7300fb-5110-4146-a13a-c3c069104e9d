package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation;

import com.sankuai.medicalcosmetology.offline.code.api.dto.common.DictDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26
 * @Description:活动投放限制
 */
@Data
public class DeliveryRestrictionDTO implements Serializable {

    /** 限制类型;1：类目限制，2：门店白名单 */
    private Integer type;

    /** 业务线 */
    private List<DictDTO> buLines;

    /** 一级类目列表 */
    private List<DictDTO> firstCategories;

    /** 二级类目列表 */
    private List<DictDTO> secondCategories;

    /** 城市;多个以","分隔 */
    private String cities;

    /** 城市字典列表 */
    private List<DictDTO> cityDict;

    /** 是否限制城市 */
    private boolean limitCity;

    /** 门店白名单类型;1：手动输入（shopIds），2：楼层导入（floorIds） */
    private Integer shopWhiteType;

    /** 门店id列表;多个以","分隔 */
    private String shopIds;

    /** 楼层id列表;多个以","分隔 */
    private String floorIds;

    /** 团单id列表;多个以","分隔 */
    private String dealIds;

}
