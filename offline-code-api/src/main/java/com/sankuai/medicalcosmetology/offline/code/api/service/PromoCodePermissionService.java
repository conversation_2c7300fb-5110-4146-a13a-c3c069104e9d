package com.sankuai.medicalcosmetology.offline.code.api.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;

import javax.sound.midi.SoundbankResource;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/07/09
 * @Description:优惠码特惠码彩虹后台权限校验
 */
public interface PromoCodePermissionService {
    /**
     * 根据用户名查询是否具有通用权限
     * @param username
     * @return
     */
    RemoteResponse<Boolean> hasPermission(String username);
    /**
     * 根据用户名查询是否具有特定权限
     * @param username
     * @param targetMisId
     * @return
     */
    RemoteResponse<Boolean> hasSpecialPermission(String username,String targetMisId);

    RemoteResponse<Boolean> checkNormalUserByUAC(long id);

    RemoteResponse<Boolean> checkSpecialUserByUAC(long id);
}
