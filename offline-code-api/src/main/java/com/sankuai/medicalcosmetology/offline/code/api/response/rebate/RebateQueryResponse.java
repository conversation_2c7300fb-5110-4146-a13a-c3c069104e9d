package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageResponseDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity.RebateQueryDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: yangji12
 * @Date: 2025/6/5
 * @Description:
 */

@Data
public class RebateQueryResponse extends PageResponseDTO {

    private List<RebateQueryDTO> results;
}
