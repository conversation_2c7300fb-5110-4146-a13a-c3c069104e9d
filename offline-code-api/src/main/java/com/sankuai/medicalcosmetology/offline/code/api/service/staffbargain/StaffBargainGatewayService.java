package com.sankuai.medicalcosmetology.offline.code.api.service.staffbargain;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.BargainDealSummaryDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.StaffBargainCodeDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.DealSummaryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.StaffBargainCodeRequest;

/**
 * <AUTHOR>
 * @Date 2024/6/6 11:49
 * @Description: 职人一客一价网关服务
 */
public interface StaffBargainGatewayService {
    /**
     * 查询团购基础信息
     * @param request
     * @return
     */
    RemoteResponse<BargainDealSummaryDTO> queryDealSummary(DealSummaryRequest request);

    /**
     * 生成一客一价职人特惠码
     * @param request
     * @return
     */
    RemoteResponse<StaffBargainCodeDTO> generateBargainCode(StaffBargainCodeRequest request);
}
