package com.sankuai.medicalcosmetology.offline.code.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/18
 * @Description: 品牌码dto
 */
@Data
@NoArgsConstructor
public class BrandCodeDTO implements Serializable {

    private String qrCodeUrl;

    private String qrCodeImageUrl;

    private Long accountId;

    private Long customerId;
}
