package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.OrderActivity;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.PageResponseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/28
 * @Description:【职人返佣 or 促评活动】查询结果
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderActivityPageResultVO extends PageResponseDTO {

    /**
     * 职人返佣 or 促评活动信息
     */
    private List<OrderActivityLiteVO> activityList;

}
