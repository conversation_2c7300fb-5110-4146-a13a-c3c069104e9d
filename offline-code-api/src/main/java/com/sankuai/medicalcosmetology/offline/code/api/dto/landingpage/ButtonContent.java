package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/22
 * @Description: 按钮内容
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ButtonContent implements Serializable {

    /**
     * 文案内容
     */
    private List<TextContent> textContentList;

    /**
     * 按钮文案
     */
    @Deprecated
    private String text;

    /**
     * 按钮跳转链接
     */
    private String jumpUrl;

    /**
     * 图片url
     */
    private String imgUrl;
}
