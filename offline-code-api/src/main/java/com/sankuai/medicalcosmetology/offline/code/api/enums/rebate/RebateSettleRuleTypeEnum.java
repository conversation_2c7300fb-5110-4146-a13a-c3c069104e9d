package com.sankuai.medicalcosmetology.offline.code.api.enums.rebate;

/**
 * 返利订单结算规则类型
 */
public enum RebateSettleRuleTypeEnum {
    NORMAL(1, "普通返利"),
    STEP(2, "阶梯返利"),
    USER_PORTRAIT(3, "用户画像"),
    STEP_NEW_OLD_CUSTOMER(4, "阶梯返利-新客老客"),
    ORDER_PRICE_PROPORTION(5, "订单金额-按比例返现"),
    ORDER_PRICE_STEP(6, "订单金额-阶梯固定返现"),
    STEP_DAILY_FIRST_ORDER(7, "阶梯返利-新客老客-每日首单"),
    STEP_ORDER_CNT_PROPORTION(8, "阶梯返利-订单数量-按比例返现"),
    STEP_ORDER_GTV_PROPORTION(9, "阶梯比例-订单总交易额-按比例返现"),
    SELLER_SUBSIDY_PRICE_PROPORTION(10, "商补金额-按比例返现"),
    ;

    public Integer code;
    public String desc;

    RebateSettleRuleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean validRebateSettleRuleType(int code) {
        for (RebateSettleRuleTypeEnum type : RebateSettleRuleTypeEnum.values()) {
            if (type.code == code) {
                return true;
            }
        }
        return false;
    }

    public static RebateSettleRuleTypeEnum getByCode(int code) {
        for (RebateSettleRuleTypeEnum type : RebateSettleRuleTypeEnum.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("error code for RebateSettleRuleType.");
    }

}
