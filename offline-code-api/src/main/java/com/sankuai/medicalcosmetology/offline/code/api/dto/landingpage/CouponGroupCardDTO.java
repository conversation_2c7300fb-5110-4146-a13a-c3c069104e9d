package com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/7
 * @Description:
 */
@Data
public class CouponGroupCardDTO implements Serializable {

    /**
     * 标题
     */
    private TextContent title;

    /**
     * 副标题
     */
    private List<TextContent> subTitleList;

    /**
     * 券包总金额
     */
    private String totalAmount;

    /**
     * 剩余可用金额
     */
    private String remainToUseAmount;

    /**
     * 券包标签图片
     */
    private String couponTagImage;

    /**
     * 券包背景图片
     */
    private String backgroundImage;

    /**
     * 券标题
     */
    private String couponTitle;

    /**
     * 按钮内容
     */
    private List<ButtonContent> buttonContentList;

    /**
     * 所有券中最近的过期时间
     */
    private Date latestExpireTime;

    /**
     * 卡片有效时间，for首页，单位s
     */
    private Long effectiveTime;

    /**
     * 扩展信息map,for后端
     */
    private Map<String, String> extMap;

    /**
     * 扩展信息map,for前端
     */
    private String extMapStr;
}
