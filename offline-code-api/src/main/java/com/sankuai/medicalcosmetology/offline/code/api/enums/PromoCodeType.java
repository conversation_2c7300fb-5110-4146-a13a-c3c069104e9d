package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/5/8
 * @Description:
 */
public enum PromoCodeType {

    SHOP_CODE(1, "门店优惠码"),
    GOODS_CODE(2, "商品优惠码"),
    STAFF_CODE(3, "员工码"),
    BRAND_CODE(4, "品牌码");

    public final int code;
    public final String desc;

    PromoCodeType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PromoCodeType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PromoCodeType promoCodeType : PromoCodeType.values()) {
            if (promoCodeType.code == code) {
                return promoCodeType;
            }
        }
        return null;
    }

    public static boolean validateCode(Integer code) {
        return getByCode(code) != null;
    }
}