package com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate;

import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 报名活动请求
 */
@Data
public class ApplyActivityRequest implements Serializable {
    private static final long serialVersionUID = -685642191800291305L;

    /**
     * 活动加密id
     */
    private String activityViewId;

    /**
     * 店铺id
     */
    private List<Long> dpShopIds;


    private String token;

    /**
     * 返利账号类型
     *
     * @see RebateSettleTypeEnum
     */
    private Integer type;

    /**
     * 账号id
     */
    private Long accountId;

    /**
     * @see com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum
     * 活动类型
     */
    private Integer activityType;
}
