package com.sankuai.medicalcosmetology.offline.code.api.dto.advertiseactivity;

import com.dianping.cat.Cat;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ShopConfigTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 商家限制-点评shopId白名单
 *
 * <AUTHOR>
 * @date 2020年10月09日 14:28:06
 */
@Data
public class ShopIdConfigDTO implements Serializable {

    private Boolean limitShopId;
    /**
     * @deprecated 已废弃，请使用{@link ShopIdConfigDTO#shopIdsLong}
     *             </p>
     *             兼容说明：如果shoId中无值，则从shopIdLong中获取，溢出则为0
     */
    @Deprecated
    private List<Integer> shopIds;
    private List<Long> shopIdsLong;

    /**
     * @see ShopConfigTypeEnum
     *      默认为ShopConfigTypeEnum.SHOP_ID门店白名单
     **/
    private int type = ShopConfigTypeEnum.SHOP_ID.getCode();
    private Long dpShopItemId;

    @Deprecated
    public List<Integer> getShopIds() {
        Cat.logEvent("IntShopIdPlus", "ShopIdConfigDTO");
        if (this.shopIds != null) {
            return shopIds;
        }
        if (this.shopIdsLong != null) {
            this.shopIds = new ArrayList<>();
            for (Long aLong : this.shopIdsLong) {
                this.shopIds.add(aLong <= Integer.MAX_VALUE && aLong >= Integer.MIN_VALUE ? aLong.intValue() : 0);
            }
            return this.shopIds;
        }
        return null;
    }

    @Deprecated
    public void setShopIds(List<Integer> shopIds) {
        Cat.logEvent("IntShopIdPlus", "ShopIdConfigDTO");
        this.shopIds = shopIds;
        if (shopIds == null) {
            this.shopIdsLong = null;
        } else {
            List<Long> longs = new ArrayList<>();
            for (Integer integer : shopIds) {
                longs.add(integer.longValue());
            }
            this.shopIdsLong = longs;
        }
    }

    public List<Long> getShopIdsLong() {
        if (this.shopIdsLong != null) {
            return this.shopIdsLong;
        }
        if (this.shopIds != null) {
            this.shopIdsLong = new ArrayList<>();
            for (Integer longNum : this.shopIds) {
                this.shopIdsLong.add(longNum.longValue());
            }
            return this.shopIdsLong;
        }
        return null;
    }

    public void setShopIdsLong(List<Long> shopIdsLong) {
        this.shopIdsLong = shopIdsLong;
        if (shopIdsLong == null) {
            this.shopIds = null;
        } else {
            List<Integer> longs = new ArrayList<>();
            for (Long longNum : shopIdsLong) {
                longs.add(longNum <= Integer.MAX_VALUE && longNum >= Integer.MIN_VALUE ? longNum.intValue() : 0);
            }
            this.shopIds = longs;
        }
    }

}
