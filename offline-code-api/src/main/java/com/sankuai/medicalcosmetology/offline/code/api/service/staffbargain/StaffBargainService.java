package com.sankuai.medicalcosmetology.offline.code.api.service.staffbargain;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.BargainDealSummaryDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.ReceiveRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.StaffBargainCodeDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.StaffBargainDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.DealSummaryRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.ReceiveRecordRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.StaffBargainCodeRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.StaffBargainRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/6 11:41
 * @Description: 职人一客一价rpc服务
 */
public interface StaffBargainService {
    /**
     * 查询职人商品一客一价码信息
     *
     * @param request
     * @return
     */
    RemoteResponse<StaffBargainDTO> queryStaffBargain(StaffBargainRequest request);

    /**
     * 查询职人商品一客一价领取记录
     *
     * @param request
     * @return
     */
    RemoteResponse<List<ReceiveRecordDTO>> queryReceiveRecord(ReceiveRecordRequest request);

    /**
     * 插入职人商品一客一价领取记录
     *
     * @param receiveRecordDTO
     * @return
     */
    RemoteResponse<Boolean> addReceiveRecord(ReceiveRecordDTO receiveRecordDTO);

    /**
     * 生成改价商品二维码
     *
     * @param request 请求参数
     * @return 返回结果
     */
    RemoteResponse<StaffBargainCodeDTO> generateBargainCode(StaffBargainCodeRequest request);

    /**
     * 查询团购基础信息
     * @param request
     * @return
     */
    RemoteResponse<BargainDealSummaryDTO> queryDealSummary(DealSummaryRequest request);
}
