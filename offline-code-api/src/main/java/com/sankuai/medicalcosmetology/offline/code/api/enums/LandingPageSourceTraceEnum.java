package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * @Author: lipengyu04
 * @Date: 2025/1/21
 * @Description:
 */
public enum LandingPageSourceTraceEnum {

    OFF_TO_ON_WX("offtoonwx", "H5平台中间页-WX小程序-落地页"),

    OFF_TO_ON_MT_APP("offtoonmtapp", "H5平台中间页-MTAPP-落地页"),

    MT_APP("mtapp", "MTAPP-落地页"),
    DP_APP("dpapp", "DPAPP-落地页"),
    WX_MINI("wxmini", "WX小程序-落地页"),
    H5("h5", "H5页面-落地页"),
    UNKNOWN("unkonwn", "未知"),

    ;

    private final String code;
    private final String msg;

    LandingPageSourceTraceEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static LandingPageSourceTraceEnum fromCode(String code) {
        LandingPageSourceTraceEnum[] values = values();
        for (LandingPageSourceTraceEnum sourceEnum : values) {
            if (sourceEnum.getCode().equals(code)) {
                return sourceEnum;
            }
        }
        return null;
    }
}
