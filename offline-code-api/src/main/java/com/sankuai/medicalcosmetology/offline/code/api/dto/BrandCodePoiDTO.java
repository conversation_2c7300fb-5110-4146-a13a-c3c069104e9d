package com.sankuai.medicalcosmetology.offline.code.api.dto;

import com.meituan.mdp.boot.starter.crypto.aop.anntation.MdpEncrypt;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/19
 * @Description:
 */
@Data
public class BrandCodePoiDTO implements Serializable {

    /**
     * 门店id
     */
    @MdpEncrypt(target = "poiIdEncrypt")
    private Long poiId;

    /**
     * 门店id加密字段
     */
    private String poiIdEncrypt;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 距离，单位：m
     */
    private Double distance;

    /**
     * 地址
     */
    private String address;

    /**
     * 营业状态 1-营业中 2-休息中
     */
    private Integer businessStatus;

    /**
     * 营业时间
     */
    private String businessTime;
}
