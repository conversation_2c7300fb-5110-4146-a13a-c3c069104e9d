package com.sankuai.medicalcosmetology.offline.code.api.response.rebate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 可报名门店信息
 */
@Data
public class ApplyShopInfoResponse implements Serializable {
    private static final long serialVersionUID = -4919134781841843895L;

    /**
     * cityName - > shopList
     * 包含可报名门店，剔除了已报名门店
     */
    private Map<String, List<ShopInfoResponse>> appliableShopMap;

    /**
     * cityName - > shopList
     * 已报名门店
     */
    private Map<String, List<ShopInfoResponse>> appliedShopMap;

}
