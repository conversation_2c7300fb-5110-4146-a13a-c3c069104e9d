package com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PageRequestDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.OperationStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/12
 * @Description:【发券活动配置】分页查询请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperationPageQueryDTO extends PageRequestDTO implements Serializable {

    /** 活动名称 */
    private String activityName;

    /** 活动ID */
    private String activityId;

    /** 活动类型
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.ActivityTypeEnum
     */
    private Integer activityType;

    /** 创建者 */
    private String creator;

    /** 业务类型
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.BizTypeEnum
     */
    private String bizType;

    /** 业务部 多个以英文,分隔*/
    private String buLines;

    /** 一级分类ID */
    private String firstCategory;

    /** 二级分类ID */
    private String secondCategory;

    /** 城市ID */
    private String city;

    /** 创建时间 */
    private String createTime;

    /**
     * 活动状态
     * @see OperationStatusEnum
     */
    private Integer status;

}
