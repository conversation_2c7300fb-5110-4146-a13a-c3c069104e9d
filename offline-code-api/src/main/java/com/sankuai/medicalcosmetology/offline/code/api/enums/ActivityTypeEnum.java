package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

@Getter
public enum ActivityTypeEnum {
    COUPON_RESOURCE_CONFIG(1, 1, "优惠券资源配置"),
    REVIEW_ACTIVITY_CONFIG(2, 2, "评论活动配置"),
    SHOP_REBATE_ACTIVITY_CONFIG(3, 3, "店铺返利活动配置"),
    STAFF_REBATE_ACTIVITY_CONFIG(4, 4, "员工返利活动配置"),
    NEW_SHOP_REBATE_ACTIVITY_CONFIG(5, 5, "新店返利活动配置"),
    ADVERTISE_RESOURCE_CONFIG(6, 6, "广告资源配置"),
    GOD_MEMBER_RESOURCE_CONFIG(7, 7, "神会员资源配置"),
    COUPON_LANDING_PAGE_PICTURE_CONFIG(8, 8, "优惠券落地页图片配置"),
    POST_PURCHASE_COUPON_CONFIG(9, 9, "购后发券活动配置"),
    GOD_COUPON_REBATE_ACTIVITY_CONFIG(10, 10, "神券返利活动配置");

    private final Integer code;
    private final Integer innerCode;
    private final String desc;

    ActivityTypeEnum(Integer code, Integer innerCode, String desc) {
        this.code = code;
        this.innerCode = innerCode;
        this.desc = desc;
    }

    public static ActivityTypeEnum getByCode(Integer code) {
        for (ActivityTypeEnum value : ActivityTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static ActivityTypeEnum getByInnerCode(Integer innerCode) {
        for (ActivityTypeEnum value : ActivityTypeEnum.values()) {
            if (value.getInnerCode().equals(innerCode)) {
                return value;
            }
        }
        return null;
    }

}
