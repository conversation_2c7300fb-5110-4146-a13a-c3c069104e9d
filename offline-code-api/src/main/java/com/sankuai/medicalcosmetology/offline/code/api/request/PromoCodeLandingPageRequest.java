package com.sankuai.medicalcosmetology.offline.code.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/5/8
 * @Description:
 */
@Data
public class PromoCodeLandingPageRequest extends EnvRequest implements Serializable {

    /**
     * 商户美团侧poiId，
     */
    private Long poiId;

    /**
     * 员工、职人id
     */
    private Long staffCodeId;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 码类型
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType
     */
    private Integer codeType;

    /**
     * 来源
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageSourceEnum
     */
    private Integer source;

    /**
     * 码key
     */
    private String codeKey;
}
