package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * 神券场景枚举
 */
public enum MagicMemberCouponScenarioEnum {
    OFFLINE_SWITCH_OFF("0000", 2, 2, 2, 2, "线下码 & 开关关闭"),
    ONLY_USE("1000", 1, 2, 2, 2, "线下码 & 开关打开 & 只可用"),
    NO_LIMIT("1111", 1, 1, 1, 1, "线下码 & 开关打开 & 无限制");

    private final String magicFlag;
    private final int canUse;
    private final int canInflate;
    private final int canBuyMMCPackage;
    private final int canGetFreeMMC;
    private final String description;

    MagicMemberCouponScenarioEnum(String magicFlag, int canUse, int canInflate, int canBuyMMCPackage, int canGetFreeMMC,
                                  String description) {
        this.magicFlag = magicFlag;
        this.canUse = canUse;
        this.canInflate = canInflate;
        this.canBuyMMCPackage = canBuyMMCPackage;
        this.canGetFreeMMC = canGetFreeMMC;
        this.description = description;
    }

    public static MagicMemberCouponScenarioEnum fromMagicFlag(String magicFlag) {
        for (MagicMemberCouponScenarioEnum scenario : values()) {
            if (scenario.magicFlag.equals(magicFlag)) {
                return scenario;
            }
        }
        throw new IllegalArgumentException("Invalid magic flag: " + magicFlag);
    }

    public String getMagicFlag() {
        return magicFlag;
    }

    public int getCanUse() {
        return canUse;
    }

    public int getCanInflate() {
        return canInflate;
    }

    public int getCanBuyMMCPackage() {
        return canBuyMMCPackage;
    }

    public int getCanGetFreeMMC() {
        return canGetFreeMMC;
    }
}