package com.sankuai.medicalcosmetology.offline.code.api.enums;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/19
 * @Description:
 */
public enum MobileOSEnum {

    ANDROID(1, "安卓"),

    IOS(2, "IOS"),

    OTHER(3, "其他"),

    UNKNOWN(4, "未知"),

    ;

    private final int code;
    private final String msg;

    MobileOSEnum(int code, String prefix) {
        this.code = code;
        this.msg = prefix;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static MobileOSEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        MobileOSEnum[] values = values();
        for (MobileOSEnum sourceEnum : values) {
            if (sourceEnum.getCode() == code) {
                return sourceEnum;
            }
        }
        return null;
    }
}
