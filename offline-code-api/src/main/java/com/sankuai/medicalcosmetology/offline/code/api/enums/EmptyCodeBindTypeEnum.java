package com.sankuai.medicalcosmetology.offline.code.api.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/7/17
 */
@Getter
public enum EmptyCodeBindTypeEnum {

    PROMO_CODE_SHOP(1, "优惠码门店码"),
    PROMO_CODE_GOODS(2, "优惠码商品码"),
    PROMO_CODE_STAFF(3, "优惠码职人码")
   ;

    private final Integer code;

    private final String desc;

    public static EmptyCodeBindTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EmptyCodeBindTypeEnum item : EmptyCodeBindTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }

    public static EmptyCodeBindTypeEnum getByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        for (EmptyCodeBindTypeEnum item : EmptyCodeBindTypeEnum.values()) {
            if (item.desc.equals(desc)) {
                return item;
            }
        }
        return null;
    }

    EmptyCodeBindTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
