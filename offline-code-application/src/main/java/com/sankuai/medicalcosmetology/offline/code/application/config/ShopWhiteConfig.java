package com.sankuai.medicalcosmetology.offline.code.application.config;

import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant.SHOP_WHITE_CONFIG;

@Slf4j
@Component
public class ShopWhiteConfig implements ConfigListener, Serializable {

    private final static ConcurrentHashMap<String, WhiteConfig> config = new ConcurrentHashMap<>();

    public ShopWhiteConfig() {
        ConfigRepository configRepository = Lion.getConfigRepository(Environment.getAppName());
        configRepository.addConfigListener(SHOP_WHITE_CONFIG, this);
        initConfig();
    }

    public static WhiteConfig getConfigBySceneCode(String sceneCode) {
        return config.get(sceneCode);
    }

    private static synchronized void initConfig() {
        Map<String, WhiteConfig> configMap = Lion.getConfigRepository().getMap(SHOP_WHITE_CONFIG,
                WhiteConfig.class);
        if (MapUtils.isNotEmpty(configMap)) {
            config.clear();
            config.putAll(configMap);
        }
    }

    /**
     * 属性配置了则校验，否则不校验
     */
    @Data
    public static class WhiteConfig implements Serializable {

        private List<Long> dpShopIds;

        private List<Integer> floorIds;

        private List<Integer> buIds;

        private List<Integer> firstCategories;

        private List<Integer> secondCategories;

        private List<Integer> cities;

        private Boolean switchOn;

        public boolean isSwitchOn() {
            return switchOn != null && switchOn;
        }

        public boolean needCheckDpShopId() {
            return CollectionUtils.isNotEmpty(dpShopIds);
        }

        public boolean needCheckFloorId() {
            return CollectionUtils.isNotEmpty(floorIds);
        }

        public boolean needCheckBuId() {
            return CollectionUtils.isNotEmpty(buIds);
        }

        public boolean needCheckFirstCategory() {
            return CollectionUtils.isNotEmpty(firstCategories);
        }

        public boolean needCheckSecondCategory() {
            return CollectionUtils.isNotEmpty(secondCategories);
        }

        public boolean needCheckCity() {
            return CollectionUtils.isNotEmpty(cities);
        }

        public boolean checkDpShopId(Long dpShopId) {
            if (!needCheckDpShopId()) {
                return true;
            }
            return dpShopIds.contains(dpShopId);
        }

        public boolean checkFloorId(Integer floorId) {
            if (!needCheckFloorId()) {
                return true;
            }
            return floorIds.contains(floorId);
        }

        public boolean checkBuId(Integer buId) {
            if (!needCheckBuId()) {
                return true;
            }
            return buIds.contains(buId);
        }

        public boolean checkFirstCategory(Integer firstCategory) {
            if (!needCheckFirstCategory()) {
                return true;
            }
            return firstCategories.contains(firstCategory);
        }

        public boolean checkSecondCategory(Integer secondCategory) {
            if (!needCheckSecondCategory()) {
                return true;
            }
            return secondCategories.contains(secondCategory);
        }

        public boolean checkCity(Integer cityId) {
            if (!needCheckCity()) {
                return true;
            }
            return cities.contains(cityId);
        }

    }

    @Override
    public void configChanged(ConfigEvent configEvent) {
        initConfig();
    }
}
