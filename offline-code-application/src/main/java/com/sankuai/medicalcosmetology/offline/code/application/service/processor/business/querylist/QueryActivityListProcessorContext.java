package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querylist;

import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ActivityModuleResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.ProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询活动列表处理器上下文
 */
@Data
public class QueryActivityListProcessorContext implements ProcessorContext {

    /**
     * 查询返利活动列表结果
     */
    private List<ActivityModuleResponse> activityModuleList;

    /**
     * 返利活动
     */
    private List<RebateActivityConfigDO> rebateActivityConfigDOList;

    /**
     * 账号关联的门店id
     */
    private List<Long> dpShopListByAccount;

    /**
     * 店铺报名信息,key：活动加密id， value：店铺报名信息
     */
    private Map<String, ApplyShopInfoResponse> applyShopInfoMap = new HashMap<>();

    /**
     * 点评账号id
     */
    private Long dpAccountId;

    /**
     * 点评店铺id
     */
    private Long dpShopId;

    /**
     * 活动状态
     */
    private Integer status;

    /**
     * 活动类型
     */
    private Integer activityType;
}
