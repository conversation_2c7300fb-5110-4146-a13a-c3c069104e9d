package com.sankuai.medicalcosmetology.offline.code.application.enums;

import lombok.Getter;

/**
 * @Author: yangji12
 * @Date: 2025/6/5
 * @Description:
 */
@Getter
public enum ActivityTypeEnum {
    /**
     * 降佣
     */
    COMMISSION_REDUCE(1, "降佣"),
    /**
     * 返利
     */
    REBATES(2, "返利"),
    /**
     * 评论激励
     */
    STIMULATE(3, "评论激励"),
    /**
     * 商户渠道
     */
    CHANNEL(4, "商户渠道");

    private final int code;
    private final String desc;

    ActivityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityTypeEnum getByCode(int code) {
        for (ActivityTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}