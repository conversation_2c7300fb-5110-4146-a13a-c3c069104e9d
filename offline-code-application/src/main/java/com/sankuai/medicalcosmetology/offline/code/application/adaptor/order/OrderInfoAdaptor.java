package com.sankuai.medicalcosmetology.offline.code.application.adaptor.order;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.sankuai.medicalcosmetology.offline.code.application.model.order.OrderInfoBO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 功能描述: 订单信息适配器
 *
 * <AUTHOR>
 * @date 2022/05/23
 **/
@Service
public class OrderInfoAdaptor {

    private final Cache<String, OrderInfoBO> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.SECONDS)
            .maximumSize(10000)
            .build();

    @Resource
    private List<OrderInfoAdaptorIface> adaptorList;

    public OrderInfoBO getOrder(int orderType, String orderId) {
        String cacheKey = buildCacheKey(orderType, orderId);
        OrderInfoBO cacheData = cache.getIfPresent(cacheKey);
        if (cacheData != null) {
            return cacheData;
        }
        OrderInfoBO orderInfoBO = adaptorList.stream()
                .filter(adaptor -> adaptor.support(orderType))
                .findFirst()
                .map(adaptor -> adaptor.getOrderInfo(orderId))
                .orElse(null);
        if (orderInfoBO != null) {
            cache.put(cacheKey, orderInfoBO);
        }
        return orderInfoBO;
    }

    private String buildCacheKey(int orderType, String orderId) {
        return String.format("%s_%s", orderType, orderId);
    }
}
