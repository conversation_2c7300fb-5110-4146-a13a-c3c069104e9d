package com.sankuai.medicalcosmetology.offline.code.application.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/11/5
 * @Description:运营活动配置改版切换配置
 */
@Data
public class OperationSwitchConfig implements Serializable {

    /**
     * 是否读老配置
     */
    private boolean readOld;

    /**
     * 是否读新配置
     */
    private boolean readNew;

    /**
     * 是否写新配置
     */
    private boolean writeNew;

    /**
     * 是否写老配置
     */
    private boolean writeOld;

    /**
     * 是否使用老ID
     */
    private boolean useOldId;

    /**
     * 是否同步处理图片配置
     */
    private boolean syncPicConfig;

}
