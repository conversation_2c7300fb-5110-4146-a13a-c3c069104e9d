package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB;

import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.application.enums.ExpABSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.DefaultLandingPageHandler;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.PromoCodeLandingPageIface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/11
 * @Description:
 */
@Service
public class ExpABHitExpHandler {

    @Autowired
    private List<ExpABHitExpIface> ifaceList;

    @Autowired
    private ExpABCommonHitExpHandler expABCommonHitExpHandler;

    public boolean hitExp(String strategyKey, ExpABSceneEnum expABSceneEnum) {
        return findIface(expABSceneEnum).hitExp(strategyKey);
    }

    public String hitStrategy(String strategyKey) {
        return strategyKey.substring(strategyKey.lastIndexOf('_') + 1);
    }

    private ExpABHitExpIface findIface(ExpABSceneEnum expABSceneEnum) {
        if (expABSceneEnum == null) {
            return expABCommonHitExpHandler;
        }
        ExpABHitExpIface selectedIface = ifaceList.stream()
                .filter(iface -> iface.support(expABSceneEnum))
                .findFirst()
                .orElse(expABCommonHitExpHandler);

        return selectedIface;
    }
}
