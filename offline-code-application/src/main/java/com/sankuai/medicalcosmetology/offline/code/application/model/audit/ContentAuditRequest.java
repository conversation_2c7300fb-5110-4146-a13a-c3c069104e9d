package com.sankuai.medicalcosmetology.offline.code.application.model.audit;

import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.ContentAuditRecords;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/6/17 17:45
 */
@Data
public class ContentAuditRequest {
    private List<Map<String, Object>> recordList;
    private long bizId;
    private int bizType;
    private String transId;
    private long userId;
    private int dataSource;
    private int source;
}
