package com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ButtonContent;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.MemberCardFloatingLayer;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.TextContent;
import com.sankuai.medicalcosmetology.offline.code.api.enums.MobileOSEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingFloatingLayerRequest;
import com.sankuai.medicalcosmetology.offline.code.application.model.LandingContext;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ImgUtils;
import com.sankuai.medicalcosmetology.offline.code.application.utils.PassParamBuildUtil;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.PlanAndMemberQueryFieldEnum;
import com.sankuai.mpmctmember.query.common.enums.PlatformClientApplicationEnum;
import com.sankuai.mpmctmember.query.common.enums.PlatformClientOSEnum;
import com.sankuai.mpmctmember.query.thrift.dto.*;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/11/20
 * @Description:
 */
public class MemberCardConverter {

    /**
     * 会员类型
     */
    @Getter
    private enum MemberCardUserTypeEnum {

        CHARGE_NOT_MEMBER(1, "付费未入会"),
        CHARGE_MEMBER(2, "付费已入会"),
        FREE_NOT_MEMBER(3, "免费未入会"),
        FREE_MEMBER(4, "免费已入会");

        private final int code;
        private final String desc;

        MemberCardUserTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static MemberCardUserTypeEnum fromCode(int code) {
            MemberCardUserTypeEnum[] values = values();
            for (MemberCardUserTypeEnum memberCardUserTypeEnum : values) {
                if (memberCardUserTypeEnum.getCode() == code) {
                    return memberCardUserTypeEnum;
                }
            }
            return null;
        }
    }

    public static QueryPlanAndUserIdentityDetailReq convert2MemberCardReq(LandingFloatingLayerRequest request, Long mtUserId, LandingContext context) {
        QueryPlanAndUserIdentityDetailReq req = new QueryPlanAndUserIdentityDetailReq();
        req.setMtShopId(request.getShopId());
        req.setMtUserId(mtUserId);
        req.setUserClientEnvDTO(convert2UserClientEnvDTO(request));
        req.setDistributionChannel(PassParamBuildUtil.buildLandingPagePassParam(request.getCodeKey(), request.getCodeType()));
        req.setPlanAndMemberQueryFieldEnumList(Lists.newArrayList(PlanAndMemberQueryFieldEnum.PLAN_BASIC_INFO, PlanAndMemberQueryFieldEnum.MEMBER_BASIC_INFO));
        return req;
    }

    public static MemberCardFloatingLayer convert2MemberCardFloatingLayer(QueryPlanAndUserIdentityDetailResp resp) {
        if (resp == null || resp.getMemberPlanDetailDTO() == null || resp.getUserIdentityDTO() == null ||
                StringUtils.isBlank(resp.getMemberPlanDetailDTO().getMemberPageLink())) {
            return null;
        }
        MemberPlanDetailDTO memberPlanDetailDTO = resp.getMemberPlanDetailDTO();
        UserIdentityDTO userIdentityDTO = resp.getUserIdentityDTO();

        MemberCardFloatingLayer memberCardFloatingLayer = new MemberCardFloatingLayer();
        memberCardFloatingLayer.setLogo(ImgUtils.transferImg2Webp(memberPlanDetailDTO.getBaseDTO().getCreateCardTemplate().getLogo()));
        MemberCardUserTypeEnum userTypeEnum = getUserType(memberPlanDetailDTO, userIdentityDTO);
        memberCardFloatingLayer.setTextContentList(transferTextFromMemberCard(userTypeEnum, memberPlanDetailDTO));
        memberCardFloatingLayer.setButtonContentList(Lists.newArrayList(transferButtonFromMemberCard(userTypeEnum, memberPlanDetailDTO, userIdentityDTO)));
        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("MemberCardUserType", String.valueOf(userTypeEnum.getCode()));
        memberCardFloatingLayer.setExtMapStr(JSON.toJSONString(extMap));
        return memberCardFloatingLayer;
    }

    private static UserClientEnvDTO convert2UserClientEnvDTO(LandingFloatingLayerRequest request) {
        UserClientEnvDTO userClientEnvDTO = new UserClientEnvDTO();
        userClientEnvDTO.setPlatformClient(getPlatformClient(request));
        userClientEnvDTO.setPlatformClientApplication(getPlatformClientApplicationEnum(request));
        userClientEnvDTO.setPlatformClientOS(getPlatformClientOS(request));
        return userClientEnvDTO;
    }

    private static int getPlatformClient(LandingFloatingLayerRequest request) {
        if (request.getPlatform().equals(PlatformEnum.MT.getCode())) {
            if (request.getClientType() != null && 3 == request.getClientType()) {
                // 美团微信小程序
                return com.sankuai.mpmctmember.query.common.enums.PlatformEnum.MT_APPLET.getPlatform();
            } else {
                return com.sankuai.mpmctmember.query.common.enums.PlatformEnum.MT_APP.getPlatform();
            }
        } else {
            if (request.getClientType() != null && 3 == request.getClientType()) {
                return com.sankuai.mpmctmember.query.common.enums.PlatformEnum.DP_APPLET.getPlatform();
            } else {
                return com.sankuai.mpmctmember.query.common.enums.PlatformEnum.DP_APP.getPlatform();
            }
        }
    }

    private static String getPlatformClientApplicationEnum(LandingFloatingLayerRequest request) {
        if (request.getClientType() == null) {
            return PlatformClientApplicationEnum.H5.getCode(); // 或其他默认值
        }
        switch (request.getClientType()) {
            case 1:
                return PlatformClientApplicationEnum.NATIVE.getCode();
            case 3:
                return PlatformClientApplicationEnum.MINI_PROGRAM.getCode();
            default:
                return PlatformClientApplicationEnum.H5.getCode();
        }
    }

    private static String getPlatformClientOS(LandingFloatingLayerRequest request) {
        if (request.getMobileOS() == null) {
            return PlatformClientOSEnum.ANDROID.getCode(); // 或其他默认值
        }
        if (MobileOSEnum.ANDROID.getCode() == request.getMobileOS()) {
            return PlatformClientOSEnum.ANDROID.getCode();
        } else if (MobileOSEnum.IOS.getCode() == request.getMobileOS()) {
            return PlatformClientOSEnum.IOS.getCode();
        } else {
            return PlatformClientOSEnum.ANDROID.getCode();
        }
    }

    private static MemberCardUserTypeEnum getUserType(MemberPlanDetailDTO memberPlanDetailDTO, UserIdentityDTO userIdentityDTO) {
        if (memberPlanDetailDTO.getBaseDTO().getChargeType().equals(MemberChargeTypeEnum.CHARGE.getCode())) {
            if (userIdentityDTO.isMember()) {
                return MemberCardUserTypeEnum.CHARGE_MEMBER;
            } else {
                return MemberCardUserTypeEnum.CHARGE_NOT_MEMBER;
            }
        } else {
            if (userIdentityDTO.isMember()) {
                return MemberCardUserTypeEnum.FREE_MEMBER;
            } else {
                return MemberCardUserTypeEnum.FREE_NOT_MEMBER;
            }
        }
    }

    private static List<TextContent> transferTextFromMemberCard(MemberCardUserTypeEnum userTypeEnum, MemberPlanDetailDTO memberPlanDetailDTO) {
        int effectiveMonths = Optional.ofNullable(memberPlanDetailDTO)
                .map(MemberPlanDetailDTO::getCardSalePlanPublish)
                .map(CardSalePlanDTO::getEffectiveMonths)
                .orElse(0);
        String discountValue = Optional.ofNullable(memberPlanDetailDTO)
                .map(MemberPlanDetailDTO::getCardSalePlanPublish)
                .map(CardSalePlanDTO::getDiscountValue)
                .orElse("");
        List<TextContent> textContentList = Lists.newArrayList();

        switch (userTypeEnum) {
            case CHARGE_MEMBER:
                textContentList.add(TextContent.builder().text("会员专享最低").build());
                textContentList.add(TextContent.builder().text(discountValue).highlight(true).build());
                textContentList.add(TextContent.builder().text("折").build());
                break;
            case CHARGE_NOT_MEMBER:
                String effectiveMonthStr = effectiveMonths + "个月";
                if (effectiveMonths == 6) {
                    effectiveMonthStr = "半年";
                } else if (effectiveMonths == 12) {
                    effectiveMonthStr = "一年";
                }
                textContentList.add(TextContent.builder().text("开通会员，" + effectiveMonthStr + "内会员商品享最低").build());
                textContentList.add(TextContent.builder().text(discountValue).highlight(true).build());
                textContentList.add(TextContent.builder().text("折").build());
                break;
            case FREE_NOT_MEMBER:
                textContentList.add(TextContent.builder().text(memberPlanDetailDTO.getBaseDTO().getCreateCardTemplate().getText()).build());
                break;
            default:
                break;
        }
        return textContentList;
    }

    private static ButtonContent transferButtonFromMemberCard(MemberCardUserTypeEnum userTypeEnum, MemberPlanDetailDTO memberPlanDetailDTO, UserIdentityDTO userIdentityDTO) {
        ButtonContent buttonContent = new ButtonContent();
        buttonContent.setJumpUrl(memberPlanDetailDTO.getMemberPageLink());
        List<TextContent> textContentList = Lists.newArrayList();
        switch (userTypeEnum) {
            case CHARGE_MEMBER:
                textContentList.add(TextContent.builder().text("累计已省").build());
                textContentList.add(TextContent.builder().text("￥" + intToBigDecimal(userIdentityDTO.getAccumulatedDiscounts())).highlight(true).build());
                break;
            case CHARGE_NOT_MEMBER:
            case FREE_NOT_MEMBER:
                textContentList.add(TextContent.builder().text("去开卡").build());
                break;
            case FREE_MEMBER:
                textContentList.add(TextContent.builder().text("欢迎回来，尊贵的会员，点击可查看会员权益").build());
                break;
        }
        buttonContent.setTextContentList(textContentList);
        return buttonContent;
    }

    /**
     * 将int格式的"分"，转化为bigdecimal格式的"元"  四舍五入法
     *
     * @param money
     * @return
     */
    private static BigDecimal intToBigDecimal(int money) {
        return new BigDecimal(money).divide(new BigDecimal("100"));
    }
}
