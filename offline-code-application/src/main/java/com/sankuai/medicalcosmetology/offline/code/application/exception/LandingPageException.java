package com.sankuai.medicalcosmetology.offline.code.application.exception;

import com.dianping.gmkt.event.api.enums.EventErrorCode;
import com.sankuai.medicalcosmetology.offline.code.api.enums.BaseResponseEnum;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/30
 * @Description:
 */
public class LandingPageException extends RuntimeException {

    private BaseResponseEnum response;

    public LandingPageException(BaseResponseEnum response, String message) {
        super(message);
        this.response = response;
    }

    public LandingPageException(BaseResponseEnum response) {
        super(response.getDesc());
        this.response = response;
    }

    public int getResponseCode() {
        return response.getCode();
    }

    public String getResponseDesc() {
        return response.getDesc();
    }
}
