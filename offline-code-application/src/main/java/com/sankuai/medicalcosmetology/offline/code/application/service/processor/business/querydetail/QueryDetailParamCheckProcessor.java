package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querydetail;

import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;

/**
 * B端查询活动详情参数校验
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, subScene = ProcessorSubSceneConstants.PARAM_CHECK)
public class QueryDetailParamCheckProcessor implements BaseProcessor<QueryDetailProcessorContext> {
    @Override
    public void execute(QueryDetailProcessorContext context) {
        AssertUtil.notNull(context, "上下文不能为空");
        String activityViewId = context.getActivityViewId();
        AssertUtil.notEmpty(activityViewId, "活动id不能为空");
        AssertUtil.isTrue(ResourceEncoder.checkValid(activityViewId), "活动id不合法");
    }
}
