package com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage;

import com.dianping.technician.common.api.domain.Environment;
import com.dianping.technician.common.api.domain.Operator;
import com.dianping.technician.common.api.enums.OperatorRoleEnum;
import com.dianping.technician.common.api.enums.SourceEnum;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.sankuai.dztheme.shop.enums.ClientType;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.StaffInfo;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.technician.category.enums.TechCategoryEnum;
import com.sankuai.technician.process.techdisplay.bo.*;
import com.sankuai.technician.process.techdisplay.query.TechDetailHeadInfoQuery;
import com.sankuai.technician.statistic.enums.StatisticCountTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/12
 * @Description:
 */
public class TechnicianConverter {

    private static Map<Integer, String> defaultTitleMap = Maps.newHashMap();
    static {
        defaultTitleMap.put(TechCategoryEnum.MASSAGE.getCategoryId(), "技师");
        defaultTitleMap.put(TechCategoryEnum.JOY_BAR_MANAGER.getCategoryId(), "营销经理");
        defaultTitleMap.put(TechCategoryEnum.FITNESS_COACH.getCategoryId(), "教练");
        defaultTitleMap.put(TechCategoryEnum.BEAUTY_HAIRSTYLIST.getCategoryId(), "技师");
    }

    public static TechDetailHeadInfoQuery convert2TechDetailHeadInfoQuery(Integer technicianId, Integer platform, Integer clientType) {
        if (technicianId == null || technicianId <= 0) {
            return null;
        }
        TechDetailHeadInfoQuery techDetailHeadInfoQuery = new TechDetailHeadInfoQuery();
        techDetailHeadInfoQuery.setTechnicianId(technicianId);
        completeOperator(techDetailHeadInfoQuery);
        completeEnvironment(techDetailHeadInfoQuery, platform, clientType);
        return techDetailHeadInfoQuery;
    }

    public static StaffInfo convert2StaffInfo(TechDetailHeadModuleBO techDetailHeadModuleBO, Integer categoryId) {
        StaffInfo staffInfo = new StaffInfo();
        switch (TechCategoryEnum.getByCode(categoryId)) {
            case MASSAGE:
                MassageHeadAttrValueBO massageHeadAttrValueBO = (MassageHeadAttrValueBO) techDetailHeadModuleBO
                        .getAttrValues();
                staffInfo.setAvatarUrl(massageHeadAttrValueBO.getPhotoUrl());
                staffInfo.setName(massageHeadAttrValueBO.getName());
                staffInfo.setTitle(StringUtils.isEmpty(massageHeadAttrValueBO.getTitle())
                        ? defaultTitleMap.get(categoryId) : massageHeadAttrValueBO.getTitle());
                staffInfo.setWorkYears(massageHeadAttrValueBO.getWorkYears());
                break;
            case JOY_BAR_MANAGER:
                PubHeadAttrValueBO pubHeadAttrValueBO = (PubHeadAttrValueBO) techDetailHeadModuleBO
                        .getAttrValues();
                staffInfo.setAvatarUrl(pubHeadAttrValueBO.getPhotoUrl());
                staffInfo.setName(pubHeadAttrValueBO.getName());
                staffInfo.setTitle(StringUtils.isEmpty(pubHeadAttrValueBO.getTitle())
                        ? defaultTitleMap.get(categoryId) : pubHeadAttrValueBO.getTitle());
                staffInfo.setWorkYears(pubHeadAttrValueBO.getWorkYears());
                break;
            case FITNESS_COACH:
                FitnessHeadAttrValueBo fitnessHeadAttrValueBo = (FitnessHeadAttrValueBo) techDetailHeadModuleBO
                        .getAttrValues();
                staffInfo.setAvatarUrl(fitnessHeadAttrValueBo.getPhotoUrl());
                staffInfo.setName(fitnessHeadAttrValueBo.getName());
                // 健身教练没有职位，取默认值
                staffInfo.setTitle(defaultTitleMap.get(categoryId));
                staffInfo.setWorkYears(fitnessHeadAttrValueBo.getWorkYears());
                break;
            case BEAUTY_HAIRSTYLIST:
                BeautyHeadAttrValueBo beautyHeadAttrValueBo = (BeautyHeadAttrValueBo) techDetailHeadModuleBO
                        .getAttrValues();
                staffInfo.setAvatarUrl(beautyHeadAttrValueBo.getPhotoUrl());
                staffInfo.setName(beautyHeadAttrValueBo.getName());
                staffInfo.setTitle(StringUtils.isEmpty(beautyHeadAttrValueBo.getTitle())
                        ? defaultTitleMap.get(categoryId) : beautyHeadAttrValueBo.getTitle());
                staffInfo.setWorkYears(beautyHeadAttrValueBo.getWorkYears());
                break;
        }
        staffInfo.setServiceTimes((int) techDetailHeadModuleBO
                .getStatisticVal(StatisticCountTypeEnum.TECHNICIAN_SERVICE_TIMES.getName(), 0));
        staffInfo.setLikeCount((int) techDetailHeadModuleBO
                .getStatisticVal(StatisticCountTypeEnum.TECHNICIAN_LIKE_TOTAL_COUNT.getName(), 0));
        staffInfo.setGoodReviewCount((int) techDetailHeadModuleBO
                .getStatisticVal(StatisticCountTypeEnum.TECHNICIAN_ALL_GOOD_REVIEW_COUNT.getName(), 0));
        return staffInfo;
    }

    private static void completeOperator(TechDetailHeadInfoQuery techDetailHeadInfoQuery) {
        Operator operator = new Operator();
        operator.setOperatorId(Tracer.getContext("userId"));
        operator.setOperatorName("");
        operator.setOperatorRole(OperatorRoleEnum.USER.getCode());
        techDetailHeadInfoQuery.setOperator(operator);
    }

    private static void completeEnvironment(TechDetailHeadInfoQuery techDetailHeadInfoQuery, Integer platform, Integer clientType) {
        Environment environment = new Environment();
        environment.setPlatform(PlatformEnum.MT.getCode() == platform ? com.dianping.technician.common.api.enums.PlatformEnum.MEI_TUAN : com.dianping.technician.common.api.enums.PlatformEnum.DIAN_PING);
        environment.setSourceEnum(convertClientType2SourceEnum(platform, clientType));
        techDetailHeadInfoQuery.setEnvironment(environment);
    }

    private static SourceEnum convertClientType2SourceEnum(Integer platform, Integer clientType) {
        if (clientType == null) {
            return null;
        }
        boolean isMt = platform == PlatformEnum.MT.getCode();
        if (ClientType.APP.getType() == clientType) {
            return isMt ? SourceEnum.MT_APP : SourceEnum.DP_APP;
        } else if (ClientType.M_SITE.getType() == clientType) {
            return isMt ? SourceEnum.MT_I : SourceEnum.DP_M;
        } else if (ClientType.MINI_PROGRAM.getType() == clientType) {
            return SourceEnum.MT_WX_APP;
        } else if (ClientType.WEIXIN.getType() == clientType) {
            return SourceEnum.MT_WX_BROWSER;
        } else {
            return isMt ? SourceEnum.MT_APP : SourceEnum.DP_PC;
        }
    }
}
