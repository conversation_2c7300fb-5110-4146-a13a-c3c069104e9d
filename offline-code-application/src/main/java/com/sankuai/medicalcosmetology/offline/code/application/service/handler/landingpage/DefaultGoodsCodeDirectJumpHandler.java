package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.google.gson.Gson;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.MagicMemberCouponScenarioEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.utils.LandingPageSwitchFilter;
import com.sankuai.medicalcosmetology.offline.code.application.utils.PassParamBuildUtil;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.OperationConfigDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.DealGroupAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PoiAclService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/10/8
 * @Description: 商品码直接跳转团详页处理类
 */
@Service
@Slf4j
public class DefaultGoodsCodeDirectJumpHandler implements PromoCodeLandingPageIface {

    @Autowired
    private LandingPageSwitchFilter landingPageSwitchFilter;

    @Autowired
    private DealGroupAclService dealGroupAclService;

    @Autowired
    private PoiAclService poiAclService;

    @Autowired
    private OperationConfigDomainService operationConfigDomainService;

    private final String mtMRNGoodsPage = "imeituan://www.meituan.com/gc/deal/detail?did=%s&poiid=%s";
    private final String dpMRNGoodsPage = "dianping://tuandeal?id=%s&shopid=%s";
    private final String mtXcxGoodsPage = "/gc/pages/deal/dealdetail/dealdetail?dealid=%s&shopid=%s";
    private final String mtH5GoodsPage = "https://%sg.meituan.com/app/gfe-app-page-tuan/detail-mt.html?dealId=%s&shopId=%s";
    private final String dpH5GoodsPage = "https://m.%sping.com/tuan/deal/%s";

    @Override
    public boolean support(PromoCodeLandingPageRequest request) {
        return isDefaultSource(request) && isGoodsCode(request) && canDirectJump(request);
    }

    private boolean isDefaultSource(PromoCodeLandingPageRequest request) {
        return request.getSource().equals(LandingPageSourceEnum.DEFAULT.getCode());
    }

    private boolean isGoodsCode(PromoCodeLandingPageRequest request) {
        return PromoCodeType.GOODS_CODE.code == request.getCodeType();
    }

    private boolean canDirectJump(PromoCodeLandingPageRequest request) {
        return landingPageSwitchFilter.hitLionSwitchConfig(LionConstant.PROMO_LANDING_GOODS_CODE_JUMP_SWITCH, request.getCodeType(), request.getPoiId());
    }

    @Override
    public String queryPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {

        /**
         * 美团app：imeituan://www.meituan.com/gc/deal/detail?did=xxx&poiid=xxx
         * 点评app：dianping://tuandeal?id=xxx&shopid=xxx
         * 小程序：/gc/pages/deal/dealdetail/dealdetail?dealid=xxx&shopid=xxx
         * 美团h5：线上：https://g.meituan.com/app/gfe-app-page-tuan/detail-mt.html?dealId=720071514&shopId=**********
         * 测试：https://test-g.meituan.com/app/gfe-app-page-tuan/detail-mt.html?dealId=100636420&shopId=xxx
         * 点评h5：线上：https://m.dianping.com/tuan/deal/33959209
         * 测试：https://m.51ping.com/tuan/deal/403012216
         **/
        boolean isMt = QRClientType.isMt(request.getQrClientType());
        boolean isXcx = QRClientType.isXcx(request.getQrClientType());
        boolean isH5 = QRClientType.isH5(request.getQrClientType());

        Long dpShopId = request.getPoiId() > 0L ? poiAclService.mt2dp(request.getPoiId()) : 0L;
        if (isXcx) {
            // 小程序
            return handlerExtParam(String.format(mtXcxGoodsPage, request.getGoodsId(), request.getPoiId()), request, dpShopId);
        } else if (isMt) {
            if (isH5) {
                // 美团h5
                return handlerExtParam(String.format(mtH5GoodsPage, Environment.isTestEnv() ? "test-" : "", request.getGoodsId(), request.getPoiId()), request, dpShopId);
            } else {
                // 美团app
                return handlerExtParam(String.format(mtMRNGoodsPage, request.getGoodsId(), request.getPoiId()), request, dpShopId);
            }
        } else {
            Long dpDealGroupId = dealGroupAclService.mt2dp(request.getGoodsId());
            if (isH5) {
                // 点评h5
                return handlerExtParam(String.format(dpH5GoodsPage, Environment.isTestEnv() ? "51" : "dian", dpDealGroupId), request, dpShopId);
            } else {
                // 点评app
                return handlerExtParam(String.format(dpMRNGoodsPage, dpDealGroupId, dpShopId), request, dpShopId);
            }
        }
    }

    @Override
    public PromoCodeLandingUrlDTO queryAllPlatformPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        return null;
    }

    private String handlerExtParam(String url, PromoCodeLandingPageRequest request, Long dpShopId) {
        try {
            // passparam参数
            //bizExtend为自定义参数，内部key和value由三方自己定义，推送订单消息时会以Json字符串形式通过推送接口的extand字段传递
            //Map<String, Object> bizExtend = new HashMap<>();
            //bizExtend.put("distributionCode", request.getCodeKey());
            //Map<String, Object> distributionBasicInfo = new HashMap<>();
            //distributionBasicInfo.put("distributionType", "promotion_code");//主渠道
            //distributionBasicInfo.put("sceneId", "ShopCode");//子渠道
            //distributionBasicInfo.put("bizExtend", new Gson().toJson(bizExtend));//用不到就不传
            //List<Map<String, Object>> distributionBasicInfoList = new ArrayList<>();
            //distributionBasicInfoList.add(distributionBasicInfo);
            //Map<String, Object> distribution = new HashMap<>();
            //distribution.put("distributionInfoList", distributionBasicInfoList);
            //Map<String, Object> passParam = new HashMap<>();
            //passParam.put("DISTRIBUTION_BASIC_INFO", new Gson().toJson(distribution));
            //String pass_param = URLEncoder.encode(new Gson().toJson(passParam), "utf-8");
            String pass_param = PassParamBuildUtil.buildLandingPagePassParam(request.getCodeKey(), PromoCodeType.GOODS_CODE.code);

            // source参数
            String source = "newyouhuima";

            // 神券参数
            String magicFlag = dpShopId != null && dpShopId > 0 ? operationConfigDomainService.getMagicMemberCouponSwitch(dpShopId) : MagicMemberCouponScenarioEnum.OFFLINE_SWITCH_OFF
                    .getMagicFlag();
            MagicMemberCouponScenarioEnum couponScenarioEnum = MagicMemberCouponScenarioEnum.fromMagicFlag(magicFlag);

            // 拼接参数
            url += url.contains("?") ? "&" : "?";
            url += "pass_param=%s&source=%s&mmcinflate=%s&mmcuse=%s&mmcbuy=%s&mmcfree=%s&offlinecode=%s";

            return String.format(url, pass_param, source, couponScenarioEnum.getCanInflate(), couponScenarioEnum.getCanUse(), couponScenarioEnum.getCanBuyMMCPackage(), couponScenarioEnum.getCanGetFreeMMC(), request.getCodeKey());
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + " handlerPassParam error, url is {}, req is {}, exception is", url, JSON.toJSONString(request), e);
            return url;
        }
    }
}
