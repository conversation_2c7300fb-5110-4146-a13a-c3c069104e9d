package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.queryapplyshop;

import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityRecordStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.model.DpAccountBaseInfo;
import com.sankuai.medicalcosmetology.offline.code.application.service.OperationConfigForBApplicationService;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DpAccountBaseInfoCacheUtils;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityConfigDomainService;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityRecordDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.AccountAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PoiAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 查询报名门店-准备数据
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_APPLY_SHOP_FOR_B, subScene = ProcessorSubSceneConstants.PREPARE_DATA)
public class QueryApplyShopPrepareDataProcessor implements BaseProcessor<QueryApplyShopProcessorContext> {

    @Resource
    private RebateActivityConfigDomainService rebateActivityConfigDomainService;

    @Resource
    private AccountAclService accountAclService;

    @Resource
    private RebateActivityRecordDomainService rebateActivityRecordDomainService;

    @Resource
    private PoiAclService poiAclService;

    @Resource
    private OperationConfigForBApplicationService operationConfigForBApplicationService;

    @Override
    public void execute(QueryApplyShopProcessorContext context) {

        ApplyShopInfoResponse applyShopInfoResponse = new ApplyShopInfoResponse();

        Long activityId = context.getActivityId();
        Long dpAccountId = context.getDpAccountId();

        //从缓存中获取账号基础信息
        DpAccountBaseInfo dpAccountBaseInfo = DpAccountBaseInfoCacheUtils.getDpAccountBaseInfo(dpAccountId);

        RebateActivityConfigDO rebateActivityConfigDO = rebateActivityConfigDomainService.getOperationConfigById(activityId);
        AssertUtil.notNull(rebateActivityConfigDO, "活动不存在");

        //查询账号下关联的门店
        List<Long> dpShopList;
        if (dpAccountBaseInfo != null && CollectionUtils.isNotEmpty(dpAccountBaseInfo.getDpShopIdList())) {
            dpShopList = new ArrayList<>(dpAccountBaseInfo.getDpShopIdList());
        } else {
            dpShopList = accountAclService.getDpShopListByDpAccountId(dpAccountId);
            operationConfigForBApplicationService.setDpAccountBaseInfoToCache(dpAccountId, dpShopList);
        }


        //查询已报名记录
        RebateActivityRecordQuery rebateActivityRecordQuery = new RebateActivityRecordQuery();
        rebateActivityRecordQuery.setRebateActivityType(OperationConfigTypeEnum.GOD_COUPON_REBATE_ACTIVITY_CONFIG.getCode());
        rebateActivityRecordQuery.setActivityConfigIdList(Lists.newArrayList(activityId));
        rebateActivityRecordQuery.setStatus(RebateActivityRecordStatusEnum.VALID.code);
        rebateActivityRecordQuery.setDpShopIdList(dpShopList);
        List<RebateActivityRecordDO> rebateActivityRecordDOList = rebateActivityRecordDomainService.queryRebateActivityRecordList(rebateActivityRecordQuery);
        if (CollectionUtils.isEmpty(rebateActivityRecordDOList)) {
            applyShopInfoResponse.setAppliedShopMap(new HashMap<>());
        }

        //查询门店信息
        Map<Long, DpPoiDTO> poiDTOMap;
        if (dpAccountBaseInfo != null && MapUtils.isNotEmpty(dpAccountBaseInfo.getPoiMap())) {
            poiDTOMap = new HashMap<>(dpAccountBaseInfo.getPoiMap());
        } else {
            poiDTOMap = poiAclService.batchGetDpPoiDTO(dpShopList, null);
        }

        context.setDpShopListByAccount(dpShopList);
        context.setPoiDTOMap(poiDTOMap);
        context.setApplyShopInfoResponse(applyShopInfoResponse);
        context.setRebateActivityRecordDOList(rebateActivityRecordDOList);
        context.setRebateActivityConfigDO(rebateActivityConfigDO);
    }
}
