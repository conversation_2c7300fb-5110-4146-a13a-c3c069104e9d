package com.sankuai.medicalcosmetology.offline.code.application.model.sso;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @Description:sso用户信息
 */
@Data
public class User implements Serializable {

    private long id;
    private String login;
    private String name;
    private String code;
    private String email;
    private String tenantId;
    private List<String> roles;

}
