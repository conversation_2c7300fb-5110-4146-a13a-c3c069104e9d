package com.sankuai.medicalcosmetology.offline.code.application.model.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2022/09/29
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderDiscountBO {

    /**
     * 立减类型
     * @see com.dianping.pay.order.common.enums.AmountType
     */
    private int type;

    /**
     * 用户优惠凭证id
     */
    private String discountId;

    /**
     * 立减金额
     */
    private BigDecimal discountAmount;
}
