package com.sankuai.medicalcosmetology.offline.code.application.enums.audit;

/**
 * <AUTHOR>
 * @Date 2024/6/14 16:21
 */
public enum ContentTypeEnum {
    UNKNOWN(0, "未知"),
    PIC(1, "图片"),
    TEXT(2, "文本");

    private final int code;
    private final String desc;

    private ContentTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ContentTypeEnum fromCode(int code) {
        ContentTypeEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ContentTypeEnum enumValue = var1[var3];
            if (enumValue.getCode() == code) {
                return enumValue;
            }
        }
        return UNKNOWN;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
