package com.sankuai.medicalcosmetology.offline.code.application.converter.operation;

import com.dianping.gmkt.event.api.promoqrcode.dto.activity.*;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.CategoryConfigLimitDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.CityConfigLimitDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.ShopIdConfigLimitDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardConfigInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23
 * @Description:
 */
public class ResourceConverter {

    private static AwardConfigInfoDTO convert2AwardConfigDTO(AwardConfigDTO awardConfigInfoDTO) {
        if (awardConfigInfoDTO == null) {
            return null;
        }
        AwardConfigInfoDTO awardConfigInfo = new AwardConfigInfoDTO();
        awardConfigInfo.setMainAwardConfig(convert2AwardDTOList(awardConfigInfoDTO.getMainAwardConfig()));
        awardConfigInfo.setMultiAwardConfig(convert2MultiAwardDTOList(awardConfigInfoDTO.getMultiAwardConfig()));
        return awardConfigInfo;
    }

    private static List<List<AwardActivityDTO>> convert2MultiAwardDTOList(List<List<AwardDTO>> multiAwardConfig) {
        if (CollectionUtils.isEmpty(multiAwardConfig)) {
            return Lists.newArrayList();
        }
        List<List<AwardActivityDTO>> multiAwardActivityDTOList = Lists.newArrayList();
        for (List<AwardDTO> awardDTOList : multiAwardConfig) {
            multiAwardActivityDTOList.add(convert2AwardDTOList(awardDTOList));
        }
        return multiAwardActivityDTOList;
    }

    private static List<AwardActivityDTO> convert2AwardDTOList(List<AwardDTO> awardConfigDTOList) {
        if (CollectionUtils.isEmpty(awardConfigDTOList)) {
            return Lists.newArrayList();
        }
        List<AwardActivityDTO> awardActivityDTOList = Lists.newArrayList();
        for (AwardDTO awardConfigDTO : awardConfigDTOList) {
            awardActivityDTOList.add(convert2AwardDTO(awardConfigDTO));
        }
        return awardActivityDTOList;
    }

    private static AwardActivityDTO convert2AwardDTO(AwardDTO awardConfigDTO) {
        if (awardConfigDTO == null) {
            return null;
        }
        AwardActivityDTO awardActivityDTO = new AwardActivityDTO();
        awardActivityDTO.setCode(awardConfigDTO.getECode());
        awardActivityDTO.setPlatform(awardConfigDTO.getPlatform());
        awardActivityDTO.setValid(awardConfigDTO.getValid());
        awardActivityDTO.setName(awardConfigDTO.getName());
        awardActivityDTO.setStartTime(awardConfigDTO.getStartTime());
        awardActivityDTO.setEndTime(awardConfigDTO.getEndTime());
        return awardActivityDTO;
    }

    private static CityConfigLimitDTO convert2CityConfigDTO(CityConfigDTO cityConfigDTO) {
        if (cityConfigDTO == null) {
            return null;
        }
        CityConfigLimitDTO cityConfigLimitDTO = new CityConfigLimitDTO();
        cityConfigLimitDTO.setLimitCity(cityConfigDTO.getLimitCity());
        cityConfigLimitDTO.setCityIds(cityConfigDTO.getCityIds());
        return cityConfigLimitDTO;
    }

    private static CategoryConfigLimitDTO convert2CategoryConfigDTO(CategoryConfigDTO categoryConfigDTO) {
        if (categoryConfigDTO == null) {
            return null;
        }
        CategoryConfigLimitDTO categoryConfig = new CategoryConfigLimitDTO();
        categoryConfig.setLimitCategory(categoryConfigDTO.getLimitCategory());
        categoryConfig.setCategoryIds(categoryConfigDTO.getCategoryIds());
        return categoryConfig;
    }

    private static ShopIdConfigLimitDTO convert2ShopIdConfigDTO(ShopIdConfigDTO shopIdConfigDTO) {
        if (shopIdConfigDTO == null) {
            return null;
        }
        ShopIdConfigLimitDTO shopIdConfigLimitDTO = new ShopIdConfigLimitDTO();
        shopIdConfigLimitDTO.setShopIdsLong(shopIdConfigDTO.getShopIdsLong());
        shopIdConfigLimitDTO.setType(shopIdConfigDTO.getType());
        shopIdConfigLimitDTO.setDpShopItemId(shopIdConfigDTO.getDpShopItemId());
        shopIdConfigLimitDTO.setLimitShopId(shopIdConfigDTO.getLimitShopId());
        return shopIdConfigLimitDTO;
    }
}
