package com.sankuai.medicalcosmetology.offline.code.application.model;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class StaffOrderCollectModel {
    private String dpShopId;
    private String shopName;
    private String techId;
    private String techName;
    private int orderSum;
    private int verifyOrderSum;
    private double payAmount;
    private double verifyPayAmount;
    private long scanUserCnt;
    private long reviewCnt;
}
