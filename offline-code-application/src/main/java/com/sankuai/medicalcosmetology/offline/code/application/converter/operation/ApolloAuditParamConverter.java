package com.sankuai.medicalcosmetology.offline.code.application.converter.operation;

import com.alibaba.fastjson.JSON;
import com.dianping.gb.audit.page.api.dto.AuditDataItemDTO;
import com.dianping.gb.audit.page.api.dto.item.CommonField;
import com.dianping.gb.audit.platform.api.dto.NodeDataDTO;
import com.dianping.gb.audit.platform.api.dto.User;
import com.dianping.gb.audit.platform.api.dto.WorkOrderDataDTO;
import com.dianping.gb.audit.platform.api.enums.UserTypeEnum;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.FreeCommissionRuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateOrderPriceStepConfigDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleConditionDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleCustomerStepRuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleRuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleRuleStepConfigDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateJoinConditionTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleConditionKeyEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleRuleTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ApolloAuditConstant;
import com.sankuai.medicalcosmetology.offline.code.application.utils.AssertUtil;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DateUtil;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.constant.CommonConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.sankuai.medicalcosmetology.offline.code.application.constants.ApolloAuditConstant.ACTIVITY_FINANCE_BP_AUDIT_NODE_NAME;
import static com.sankuai.medicalcosmetology.offline.code.application.constants.ApolloAuditConstant.ACTIVITY_X1_AUDIT_NODE_NAME;

/**
 * 阿波罗审核参数转换类
 */
public class ApolloAuditParamConverter {

    public static List<NodeDataDTO> buildOrderActivityAuditNodeData(OperationInfoDTO operationInfoDTO) {
        /* 创建分单信息，workOrderSubmitter如何填充请联系平台侧对接同学 */
        WorkOrderDataDTO workOrderDataDTO = new WorkOrderDataDTO();
        workOrderDataDTO.setWorkOrderSubmitter(new User(UserTypeEnum.SSO_USER, Math.toIntExact(operationInfoDTO.getLoginUserId())));

        //透传参数
        Map<String, String> extendValues = new HashMap<>();
        extendValues.put(ApolloAuditConstant.SUBMIT_SOURCE, CommonConstant.APP_KEY);

        List<AuditDataItemDTO> auditDataItemDTOs = buildAuditDataItemList(operationInfoDTO);

        NodeDataDTO x1AuditNode = new NodeDataDTO();
        x1AuditNode.setNodeName(ACTIVITY_X1_AUDIT_NODE_NAME);
        x1AuditNode.setWorkOrderDataDTO(workOrderDataDTO);
        x1AuditNode.setData(auditDataItemDTOs);
        x1AuditNode.setPassThroughValues(extendValues);

        NodeDataDTO financeBPAuditNode = new NodeDataDTO();
        financeBPAuditNode.setNodeName(ACTIVITY_FINANCE_BP_AUDIT_NODE_NAME);
        financeBPAuditNode.setWorkOrderDataDTO(workOrderDataDTO);
        financeBPAuditNode.setData(auditDataItemDTOs);
        financeBPAuditNode.setPassThroughValues(extendValues);
        return Lists.newArrayList(x1AuditNode, financeBPAuditNode);
    }

    public static List<AuditDataItemDTO> buildAuditDataItemList(OperationInfoDTO operationInfoDTO) {
        RebateActivityDTO rebateActivityDTO = operationInfoDTO.getRebateActivityInfo();
        List<AuditDataItemDTO> auditDataItemDTOs = Lists.newArrayList();
        /* 活动类型 */
        RebateActivityTypeEnum rebateActivityTypeEnum = RebateActivityTypeEnum.getByCode(rebateActivityDTO.getRebateActivityType());
        CommonField activity_type = CommonField.build(rebateActivityTypeEnum == null ? "" : rebateActivityTypeEnum.desc);
        /* 业务类型 */
        Integer bizType = operationInfoDTO.getBizType();
        BizTypeEnum bizTypeEnum = BizTypeEnum.fromCode(bizType);
        CommonField business_type = CommonField.build(bizTypeEnum.getDescription());
        /* 专项活动 */
        String specialTag = "";
        if (CollectionUtils.isNotEmpty(operationInfoDTO.getTags())) {
            specialTag = StringUtils.join(operationInfoDTO.getTags(), "/");
        }
        CommonField special_event = CommonField.build(specialTag);
        /* 活动名称 */
        CommonField activity_name = CommonField.build(operationInfoDTO.getActivityName());
        /* 活动时间 */
        String activityTime = DateUtil.format(new Date(operationInfoDTO.getStartTime()))
                + " 至 " + DateUtil.format(new Date(operationInfoDTO.getEndTime()));
        CommonField activity_time = CommonField.build(activityTime);
        /* 活动规则 */
        CommonField activity_rule = CommonField.build(operationInfoDTO.getRules());
        /* 规则填写校验数据 */
        RuleDTO rule = rebateActivityDTO.getRule();
        AssertUtil.notNull(rebateActivityTypeEnum, "返利活动类型不能为空");
        String ruleDetail = buildRuleDetail(rebateActivityTypeEnum, rule);
        CommonField rule_check_data = CommonField.build(ruleDetail);
        /* 活动介绍 */
        CommonField activity_intro = CommonField.build(operationInfoDTO.getDescription());
        /* 点评门店白名单 */
//        CommonField dp_shop_white_list = CommonField.build(StringUtils.EMPTY);
//        if (RebateJoinConditionTypeEnum.DP_SHOPID_WHITELIST.code.equals(rebateActivityDTO.getCondition().getType())) {
//            dp_shop_white_list = CommonField.build(JSON.toJSONString(rebateActivityDTO.getCondition().getDpShopIds()));
//        } else if (RebateJoinConditionTypeEnum.DP_SHOP_ITEM_ID.code.equals(rebateActivityDTO.getCondition().getType())) {
//            dp_shop_white_list = CommonField.build("点评门店楼层id：" + rebateActivityDTO.getCondition().getDpShopItemId());
//        }
        auditDataItemDTOs.add(AuditDataItemDTO.build("activity_type", activity_type));
        auditDataItemDTOs.add(AuditDataItemDTO.build("business_type", business_type));
        auditDataItemDTOs.add(AuditDataItemDTO.build("special_event", special_event));
        auditDataItemDTOs.add(AuditDataItemDTO.build("activity_name", activity_name));
        auditDataItemDTOs.add(AuditDataItemDTO.build("activity_time", activity_time));
        auditDataItemDTOs.add(AuditDataItemDTO.build("activity_rule", activity_rule));
        auditDataItemDTOs.add(AuditDataItemDTO.build("rule_check_data", rule_check_data));
        auditDataItemDTOs.add(AuditDataItemDTO.build("activity_intro", activity_intro));
//        auditDataItemDTOs.add(AuditDataItemDTO.build("dp_shop_white_list", dp_shop_white_list));
        return auditDataItemDTOs;
    }

    private static String buildRuleDetail(RebateActivityTypeEnum rebateActivityType, RuleDTO rule) {
        StringBuilder ruleDetail = new StringBuilder();
        FreeCommissionRuleDTO freeCommissionRule = rule.getFreeCommissionRule();
        RebateSettleRuleDTO rebateRule = rule.getRebateRule();
        switch (rebateActivityType) {
            case REDUCE_COMMISSION:
                ruleDetail.append("降后佣金率：")
                        .append(freeCommissionRule.getNewCommissionRate())
                        .append("%");
                break;
            case NORMAL_REBATE_ORDER_CNT:
                ruleDetail.append("1）单笔订单奖励金额 ")
                        .append(new BigDecimal(rebateRule.getNormalRule().getRebateAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                        .append(" 元；");
                for (RebateSettleConditionDTO conditionDTO : rebateRule.getCondition()) {
                    if (RebateSettleConditionKeyEnum.ORDER_PRICE.code.equals(conditionDTO.getKey())) {
                        ruleDetail.append("\n")
                                .append("2）订单金额不低于 ")
                                .append(new BigDecimal(conditionDTO.getValue()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                                .append(" 元。");
                    }
                }
                break;
            case NORMAL_REBATE_DEAL_ORDER_CNT:
                ruleDetail.append("1）单笔订单奖励金额 ")
                        .append(new BigDecimal(rebateRule.getNormalRule().getRebateAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                        .append(" 元；");
                for (RebateSettleConditionDTO conditionDTO : rebateRule.getCondition()) {
                    if (RebateSettleConditionKeyEnum.DEAL_ITEM_ID.code.equals(conditionDTO.getKey())) {
                        ruleDetail.append("\n")
                                .append("2）团单楼层ID ")
                                .append(conditionDTO.getValue())
                                .append(" 。");
                    }
                    if (RebateSettleConditionKeyEnum.ORDER_PRICE.code.equals(conditionDTO.getKey())) {
                        ruleDetail.append("\n")
                                .append("3）订单金额不低于 ")
                                .append(new BigDecimal(conditionDTO.getValue()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                                .append(" 元。");
                    }
                }
                break;
            case NORMAL_REBATE_USER_CNT:
                ruleDetail.append("1）单个用户奖励金额 ")
                        .append(new BigDecimal(rebateRule.getNormalRule().getRebateAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                        .append(" 元；");
                for (RebateSettleConditionDTO conditionDTO : rebateRule.getCondition()) {
                    if (RebateSettleConditionKeyEnum.ORDER_PRICE.code.equals(conditionDTO.getKey())) {
                        ruleDetail.append("\n")
                                .append("2）订单金额不低于 ")
                                .append(new BigDecimal(conditionDTO.getValue()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                                .append(" 元。");
                    }
                }
                break;
            case STEP_REBATE_USER_CNT:
                ruleDetail.append("1）奖励阶梯规则：活动期间顾客扫码下单且核销，");
                for (RebateSettleRuleStepConfigDTO ruleStepConfigDTO : rebateRule.getStepRule().getRuleStep()) {
                    ruleDetail.append("第 ")
                            .append(ruleStepConfigDTO.getStepStart()).append("-").append(ruleStepConfigDTO.getStepEnd())
                            .append(" 位，每人激励 ")
                            .append(new BigDecimal(ruleStepConfigDTO.getRebateAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" 元；");
                }
                for (RebateSettleConditionDTO conditionDTO : rebateRule.getCondition()) {
                    if (RebateSettleConditionKeyEnum.ORDER_PRICE.code.equals(conditionDTO.getKey())) {
                        ruleDetail.append("\n")
                                .append("2）订单金额不低于 ")
                                .append(new BigDecimal(conditionDTO.getValue()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                                .append(" 元。");
                    }
                }
                break;
            case USER_REVIEW:
                ruleDetail.append("1) 完成优惠码订单的评价且满足15字的用户奖励")
                        .append(rule.getReviewRule().getReviewAward().getAmount())
                        .append("元。");
                if (rule.getReviewRule().getOrderThresholdPrice() != null) {
                    ruleDetail.append("\n").append("2) 订单金额不低于 ")
                            .append(rule.getReviewRule().getOrderThresholdPrice())
                            .append(" 元。");
                }
                break;
            case STEP_REBATE_DAILY_FIRST_ORDER:
            case STEP_REBATE_NEW_OLD_CUSTOMER_CNT:
                int stepNo = 1;
                String newStepDetail = getStepDetail(stepNo, "新客", rebateRule.getNewCustomerStepRule());
                if (StringUtils.isNotBlank(newStepDetail)) {
                    ruleDetail.append(newStepDetail);
                    stepNo++;
                    String newConditionDetail = getConditionDetail(stepNo, "新客", rebateRule.getNewCustomerStepRule());
                    if (StringUtils.isNotBlank(newConditionDetail)) {
                        ruleDetail.append("\n").append(newConditionDetail);
                        stepNo++;
                    }
                }
                String oldStepDetail = getStepDetail(stepNo, "老客", rebateRule.getOldCustomerStepRule());
                if (StringUtils.isNotBlank(oldStepDetail)) {
                    if (ruleDetail.length() > 0) {
                        ruleDetail.append("\n");
                    }
                    ruleDetail.append(oldStepDetail);
                    stepNo++;
                    String oldConditionDetail = getConditionDetail(stepNo, "老客", rebateRule.getOldCustomerStepRule());
                    if (StringUtils.isNotBlank(oldConditionDetail)) {
                        ruleDetail.append("\n").append(oldConditionDetail);
                        stepNo++;
                    }
                }
                if (RebateSettleRuleTypeEnum.STEP_DAILY_FIRST_ORDER.code.equals(rebateRule.getType())) {
                    ruleDetail.append("\n")
                            .append(String.format("%d）每日首单最低返 ", stepNo))
                            .append(new BigDecimal(rebateRule.getDailyFirstOrderRebateAmount())
                                    .divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN)
                                    .toPlainString())
                            .append(" 元");
                }
                break;
            case ORDER_REBATE_PRICE_PROPORTION:
                ruleDetail.append("1）奖励规则: 活动期间顾客扫码下单且核销后, 按照订单金额的 ")
                        .append(new BigDecimal(rebateRule.getPriceProportionRule().getRebateProportion())
                                .divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                        .append("% 返现, 单笔返现上限 ")
                        .append(new BigDecimal(rebateRule.getPriceProportionRule().getLimitRebateAmount())
                                .divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                        .append(" 元;");
                break;
            case ORDER_REBATE_PRICE_FIXED:
                ruleDetail.append("1）奖励规则: 活动期间顾客扫码下单且核销后, ");
                for (RebateOrderPriceStepConfigDTO configDTO : rebateRule.getOrderPriceStepRule().getRuleStep()) {
                    ruleDetail.append("订单金额满 ")
                            .append(new BigDecimal(configDTO.getLimitOrderPrice())
                                    .divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" 元, 返现 ")
                            .append(new BigDecimal(configDTO.getRebateAmount())
                                    .divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" 元;");
                }
                break;
            case STEP_REBATE_ORDER_CNT:
                ruleDetail.append("1）奖励阶梯规则：活动期间顾客扫码下单且核销，");
                for (RebateSettleRuleStepConfigDTO ruleStepConfigDTO : rebateRule.getStepRule().getRuleStep()) {
                    ruleDetail.append("交易订单数范围 ")
                            .append(ruleStepConfigDTO.getStepStart()).append("-").append(ruleStepConfigDTO.getStepEnd())
                            .append(" 笔订单，按每笔订单金额的 ")
                            .append(new BigDecimal(ruleStepConfigDTO.getRebateProportion()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" %返利；");
                }
                if (rebateRule.getPriceProportionRule() != null && rebateRule.getPriceProportionRule().getLimitRebateAmount() != null) {
                    ruleDetail.append("\n")
                            .append("2）单笔订单返利最高金额 ")
                            .append(new BigDecimal(rebateRule.getPriceProportionRule().getLimitRebateAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" 元。");
                }
                break;
            case SET_REBATE_VERIFY_GTV:
                ruleDetail.append("1）奖励阶梯规则：活动期间顾客扫码下单且核销，");
                for (RebateSettleRuleStepConfigDTO ruleStepConfigDTO : rebateRule.getStepRule().getRuleStep()) {
                    ruleDetail.append("累计核销GTV范围 ")
                            .append(new BigDecimal(ruleStepConfigDTO.getStepStart()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append("-")
                            .append(new BigDecimal(ruleStepConfigDTO.getStepEnd()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" 元，每笔订单按核销GTV的 ")
                            .append(new BigDecimal(ruleStepConfigDTO.getRebateProportion()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" %返利；");
                }
                if (rebateRule.getPriceProportionRule() != null && rebateRule.getPriceProportionRule().getLimitRebateAmount() != null) {
                    ruleDetail.append("\n")
                            .append("2）单笔订单返利最高金额 ")
                            .append(new BigDecimal(rebateRule.getPriceProportionRule().getLimitRebateAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                            .append(" 元。");
                }
                break;
            case SELLER_SUBSIDY_PRICE_PROPORTION:
                ruleDetail.append("1）奖励规则: 活动期间顾客扫码下单且核销后, 按照商补金额的 ")
                        .append(new BigDecimal(rebateRule.getSellerSubsidyProportionRule().getRebateProportion())
                                .divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                        .append("% 返现 ");
                break;
            default:
                break;
        }
        return ruleDetail.toString();
    }

    private static String getStepDetail(int stepNo, String tip, RebateSettleCustomerStepRuleDTO stepRuleDTO) {
        List<RebateSettleRuleStepConfigDTO> customerStepConfig = Optional.ofNullable(stepRuleDTO)
                .map(RebateSettleCustomerStepRuleDTO::getRuleStep)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(customerStepConfig)) {
            return null;
        }
        return buildStepDetail(stepNo, tip, customerStepConfig);
    }

    private static String getConditionDetail(int stepNo, String tip, RebateSettleCustomerStepRuleDTO stepRuleDTO) {
        if (stepRuleDTO == null || stepRuleDTO.getConditionDTO() == null) {
            return null;
        }
        return buildConditionDetail(stepNo, tip, stepRuleDTO.getConditionDTO());
    }

    private static String buildStepDetail(int stepNo, String tip, List<RebateSettleRuleStepConfigDTO> customerStepConfig) {
        StringBuilder stepDetail = new StringBuilder();
        stepDetail.append(String.format("%d）%s奖励阶梯规则：活动期间%s扫码下单且核销，", stepNo, tip, tip));
        for (RebateSettleRuleStepConfigDTO ruleStepConfigDTO : customerStepConfig) {
            stepDetail.append("第 ")
                    .append(ruleStepConfigDTO.getStepStart()).append("-").append(ruleStepConfigDTO.getStepEnd())
                    .append(" 位，每人激励 ")
                    .append(new BigDecimal(ruleStepConfigDTO.getRebateAmount()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                    .append(" 元；");
        }
        return stepDetail.toString();
    }

    private static String buildConditionDetail(int stepNo, String tip, RebateSettleConditionDTO conditionDTO) {
        if (conditionDTO == null || !RebateSettleConditionKeyEnum.ORDER_PRICE.code.equals(conditionDTO.getKey())) {
            return null;
        }
        StringBuilder conditionDetail = new StringBuilder();
        conditionDetail.append(String.format("%d）%s订单金额不低于 ", stepNo, tip))
                .append(new BigDecimal(conditionDTO.getValue()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_DOWN).toString())
                .append(" 元。");
        return conditionDetail.toString();
    }


}
