package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.queryapplyshop;

import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.rotate.territory.dto.BuInfoDTO;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.model.DpAccountBaseInfo;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DpAccountBaseInfoCacheUtils;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.BusinessDepartmentConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.CityAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.TerritoryShopAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * B端查询可报名门店-过滤
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_APPLY_SHOP_FOR_B, subScene = ProcessorSubSceneConstants.QUERY_APPLY_SHOP)
public class QueryApplyShopFilterProcessor implements BaseProcessor<QueryApplyShopProcessorContext> {


    @Resource
    private CityAclService cityAclService;

    @Resource
    private TerritoryShopAclService territoryShopAclService;

    @Override
    public void execute(QueryApplyShopProcessorContext context) {
        RebateActivityConfigDO rebateActivityConfigDO = context.getRebateActivityConfigDO();
        Map<Long, DpPoiDTO> poiDTOMap = context.getPoiDTOMap();
        List<Long> dpShopListByAccount = context.getDpShopListByAccount();
        ApplyShopInfoResponse applyShopInfoResponse = context.getApplyShopInfoResponse();
        List<RebateActivityRecordDO> rebateActivityRecordDOList = context.getRebateActivityRecordDOList();
        Long dpAccountId = context.getDpAccountId();

        //从缓存中获取账号基础信息
        DpAccountBaseInfo dpAccountBaseInfo = DpAccountBaseInfoCacheUtils.getDpAccountBaseInfo(dpAccountId);

        //可报名门店列表
        List<Long> appliableShopIdList = dpShopListByAccount;

        //BU筛选
        boolean needCheckBuLine = rebateActivityConfigDO.needCheckBuLine();

        if (needCheckBuLine) {
            List<BusinessDepartmentConfigDO> limitBuLine = rebateActivityConfigDO.getLimitBuLine();
            List<Integer> limitBuIdList = limitBuLine.stream().map(BusinessDepartmentConfigDO::getBuId).collect(Collectors.toList());
            // 查询门店对应的BU信息
            Map<Long, BuInfoDTO> shopBuInfoMap;
            if (dpAccountBaseInfo != null && MapUtils.isNotEmpty(dpAccountBaseInfo.getShopBuMap())) {
                shopBuInfoMap = new HashMap<>(dpAccountBaseInfo.getShopBuMap());
            } else {
                shopBuInfoMap = territoryShopAclService.queryBuByShopIdsV2(dpShopListByAccount);
            }
            if (MapUtils.isNotEmpty(shopBuInfoMap)) {
                List<Long> filteredBuShopIdList = new ArrayList<>();
                shopBuInfoMap.forEach((shopId, buInfoDTO) -> {
                    //必须要有BU信息
                    AssertUtil.notNull(buInfoDTO, "门店对应的BU信息为空,门店id:" + shopId);
                    // 如果店铺的BU信息在限制BU列表中，则将门店ID添加到过滤后的BU门店ID列表中。
                    if (limitBuIdList.contains(buInfoDTO.getBuId())) {
                        filteredBuShopIdList.add(shopId);
                    }
                });
                appliableShopIdList = filteredBuShopIdList;
            }
        }

        //一级类目限制

        boolean needCheckFirstCategory = rebateActivityConfigDO.needCheckFirstCategory();
        if (needCheckFirstCategory) {
            List<Integer> limitFirstCategoryList = rebateActivityConfigDO.getLimitFirstCategorys();
            //一级类目筛选
            appliableShopIdList = appliableShopIdList.stream().filter(shopId -> {
                DpPoiDTO dpPoiDTO = poiDTOMap.get(shopId);
                AssertUtil.notNull(dpPoiDTO, "获取不到门店信息,门店id:" + shopId);
                if (CollectionUtils.isEmpty(dpPoiDTO.getBackMainCategoryPath())) {
                    return false;
                }
                List<DpPoiBackCategoryDTO> backMainCategoryPath = dpPoiDTO.getBackMainCategoryPath();
                DpPoiBackCategoryDTO firstCategory = backMainCategoryPath.stream().filter(dpPoiBackCategoryDTO -> dpPoiBackCategoryDTO.getCategoryLevel() == 1).findFirst().orElse(null);
                if (firstCategory == null) {
                    return false;
                }
                return limitFirstCategoryList.contains(firstCategory.getCategoryId());
            }).collect(Collectors.toList());
        }

        //二级类目限制
        boolean needCheckSecondCategory = rebateActivityConfigDO.needCheckSecondCategory();

        if (needCheckSecondCategory) {
            List<Integer> limitSecondCategoryList = rebateActivityConfigDO.getLimitSecondCategorys();
            //二级类目筛选
            appliableShopIdList = appliableShopIdList.stream().filter(shopId -> {
                DpPoiDTO dpPoiDTO = poiDTOMap.get(shopId);
                AssertUtil.notNull(dpPoiDTO, "获取不到门店信息,门店id:" + shopId);
                if (CollectionUtils.isEmpty(dpPoiDTO.getBackMainCategoryPath())) {
                    return false;
                }
                List<DpPoiBackCategoryDTO> backMainCategoryPath = dpPoiDTO.getBackMainCategoryPath();
                DpPoiBackCategoryDTO secondCategory = backMainCategoryPath.stream().filter(dpPoiBackCategoryDTO -> dpPoiBackCategoryDTO.getCategoryLevel() == 2).findFirst().orElse(null);
                if (secondCategory == null) {
                    return false;
                }
                return limitSecondCategoryList.contains(secondCategory.getCategoryId());
            }).collect(Collectors.toList());
        }

        List<Integer> shopCityIdList = poiDTOMap.values().stream().map(DpPoiDTO::getCityId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        //城市筛选
        boolean need = rebateActivityConfigDO.needCheckCity();
        if (need) {
            List<Integer> limitCitys = rebateActivityConfigDO.getLimitCitys();
            appliableShopIdList = appliableShopIdList.stream().filter(shopId -> {
                DpPoiDTO dpPoiDTO = poiDTOMap.get(shopId);
                AssertUtil.notNull(dpPoiDTO, "获取不到门店信息,门店id:" + shopId);
                if (dpPoiDTO.getCityId() == null) {
                    return false;
                }
                if (limitCitys.contains(dpPoiDTO.getCityId())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }
        Map<Integer, String> cityIdNameMap = new HashMap<>();
        // 组装城市->可报名门店信息
        if (CollectionUtils.isEmpty(appliableShopIdList)) {
            applyShopInfoResponse.setAppliableShopMap(new HashMap<>());
        } else {
            List<CityInfoDTO> cityInfoDTOList;
            if (dpAccountBaseInfo != null && CollectionUtils.isNotEmpty(dpAccountBaseInfo.getCityInfoDTOList())) {
                cityInfoDTOList = new ArrayList<>(dpAccountBaseInfo.getCityInfoDTOList());
            } else {
                cityInfoDTOList = cityAclService.batchLoadDpCities(shopCityIdList);
            }

            if (CollectionUtils.isNotEmpty(cityInfoDTOList)) {
                cityIdNameMap = cityInfoDTOList.stream().collect(Collectors.toMap(CityInfoDTO::getCityId, CityInfoDTO::getCityName));
            }

            //剔除已报名的门店信息
            if (CollectionUtils.isNotEmpty(rebateActivityRecordDOList)) {
                List<Long> appliedShopIdList = rebateActivityRecordDOList.stream().map(RebateActivityRecordDO::getDpShopId).collect(Collectors.toList());
                appliableShopIdList = appliableShopIdList.stream().filter(shopId -> !appliedShopIdList.contains(shopId)).collect(Collectors.toList());
            }
            Map<String, List<ShopInfoResponse>> appliableShopMap = new HashMap<>();
            for (Long shopId : appliableShopIdList) {
                DpPoiDTO dpPoiDTO = poiDTOMap.get(shopId);
                AssertUtil.notNull(dpPoiDTO, "获取不到门店信息,门店id:" + shopId);
                Integer cityId = dpPoiDTO.getCityId();
                String cityName = cityIdNameMap.get(cityId);
                if (appliableShopMap.containsKey(cityName)) {
                    appliableShopMap.get(cityName).add(new ShopInfoResponse(shopId, dpPoiDTO.getShopName()));
                } else {
                    appliableShopMap.put(cityName, Lists.newArrayList(new ShopInfoResponse(shopId, dpPoiDTO.getShopName())));
                }
            }
            applyShopInfoResponse.setAppliableShopMap(appliableShopMap);
        }

        // 组装城市->已报名门店信息
        Map<String, List<ShopInfoResponse>> appliedShopMap = new HashMap<>();
        for (RebateActivityRecordDO rebateActivityRecordDO : rebateActivityRecordDOList) {
            Long dpShopId = rebateActivityRecordDO.getDpShopId();
            DpPoiDTO dpPoiDTO = poiDTOMap.get(dpShopId);
            AssertUtil.notNull(dpPoiDTO, "获取不到门店信息,门店id:" + dpShopId);
            Integer cityId = dpPoiDTO.getCityId();
            String cityName = cityIdNameMap.get(cityId);
            if (appliedShopMap.containsKey(cityName)) {
                appliedShopMap.get(cityName).add(new ShopInfoResponse(dpShopId, dpPoiDTO.getShopName(), rebateActivityRecordDO.getDpAccountId()));
            } else {
                appliedShopMap.put(cityName, Lists.newArrayList(new ShopInfoResponse(dpShopId, dpPoiDTO.getShopName(), rebateActivityRecordDO.getDpAccountId())));
            }
        }
        applyShopInfoResponse.setAppliedShopMap(appliedShopMap);
    }
}
