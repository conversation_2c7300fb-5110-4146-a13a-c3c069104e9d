package com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations;

import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 处理器注解
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface Processor {

    /**
     * 业务类型
     */
    String bizType();

    /**
     * 主场景
     */
    String mainScene();

    /**
     * 子场景
     */
    String subScene();

}
