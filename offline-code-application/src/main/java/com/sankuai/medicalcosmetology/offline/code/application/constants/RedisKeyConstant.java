package com.sankuai.medicalcosmetology.offline.code.application.constants;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/23
 * @Description:
 */
public class RedisKeyConstant {

    /**
     * 品牌码附近门店缓存
     */
    public static final String BRAND_NEARBY_POI = "brand_nearby_poi_cache";

    /**
     * 所有城市信息缓存
     */
    public static final String CITY_INFO = "all_city_info_cache";

    public static final String STAFF_BARGAIN_LOCK = "staff_bargain_code_lock";

    /**
     * 活动配置操作锁
     */
    public static final String OPERATION_ACTION_LOCK = "operation_update_lock";

    public static final String BACKGROUND_PIC_INFO = "background_pic_info";

    /**
     * 购后券订单缓存
     */
    public static final String POSTPURCHASE_COUPON_ORDER = "postpurchase_coupon_order";

    /**
     * 最近一次购后券缓存
     */
    public static final String LAST_POSTPURCHASE_COUPON_RECORD = "lastpostpurchase_coupon_record";

    /**
     * 领购后券后是否未到访过团购频道页
     */
    public static final String RECEIVE_POSTPURCHASE_COUPON_NO_VISIT_DEAL_CHANNEL = "postpurchasecoupon_novisitdeal";

    /**
     * 用户最近线下码订单缓存
     */
    public static final String OFFLINE_USER_LAST_ORDER = "offline_user_last_order";

    /**
     * 线下码落地页顶部模块缓存
     */
    public static final String OFFLINE_TOP_MODULE_CACNE = "offlinecode_top_module_cache";
}
