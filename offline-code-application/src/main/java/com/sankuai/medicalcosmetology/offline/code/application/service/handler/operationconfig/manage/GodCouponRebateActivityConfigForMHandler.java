package com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.manage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.gb.audit.platform.api.dto.NodeDataDTO;
import com.dianping.gb.audit.platform.api.dto.Result;
import com.dianping.gb.audit.platform.api.dto.User;
import com.dianping.gb.audit.platform.api.enums.UserTypeEnum;
import com.dianping.rhino.cluster.common.util.StringUtil;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationPageQueryDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationPageResultDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleConditionDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.settlerule.RebateSettleSellerSubsidyProportionRuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.OperationStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleConditionKeyEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ApolloAuditConstant;
import com.sankuai.medicalcosmetology.offline.code.application.converter.operation.ApolloAuditParamConverter;
import com.sankuai.medicalcosmetology.offline.code.application.converter.operation.OperationConverter;
import com.sankuai.medicalcosmetology.offline.code.application.converter.operation.RebateActivityConverter;
import com.sankuai.medicalcosmetology.offline.code.application.utils.RebateActivityUtil;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationExtKeyEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.OperationConfigQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityConfigDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.exception.BusinessException;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ApolloAuditService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class GodCouponRebateActivityConfigForMHandler extends AbsOperationConfigForMHandler {

    @Resource
    private RebateActivityConfigDomainService rebateActivityConfigDomainService;

    @Resource
    private ApolloAuditService apolloAuditService;

    @Override
    public String getActivityType() {
        return OperationConfigTypeEnum.GOD_COUPON_REBATE_ACTIVITY_CONFIG.name();
    }

    @Override
    public OperationPageResultDTO activityPageQuery(OperationPageQueryDTO queryDTO) {
        OperationPageResultDTO resultDTO = new OperationPageResultDTO();
        OperationConfigQuery operationConfigQuery = OperationConverter.convert2OperationConfigQuery(queryDTO);
        long count = rebateActivityConfigDomainService.countRebateActivityConfig(operationConfigQuery);
        if (count == 0) {
            resultDTO.setTotalCount(0L);
            resultDTO.setList(new ArrayList<>());
            return resultDTO;
        }
        List<RebateActivityConfigDO> list = rebateActivityConfigDomainService.pageQueryRebateActivityConfig(operationConfigQuery);
        List<Object> operationInfoDTOList = new ArrayList<>();
        for (RebateActivityConfigDO rebateActivityConfigDO : list) {
            OperationInfoDTO operationInfoDTO = RebateActivityConverter.convertRebateConfig2OperationInfoDTO(rebateActivityConfigDO);
            RebateActivityDTO rebateActivityInfo = operationInfoDTO.getRebateActivityInfo();
            if (rebateActivityInfo != null) {
                RebateActivityTypeEnum rebateActivityTypeEnum = RebateActivityTypeEnum.getByCode(rebateActivityInfo.getRebateActivityType());
                rebateActivityInfo.setRebateActivityTypeDesc(rebateActivityTypeEnum == null ? null : rebateActivityTypeEnum.desc);
            }
            if (operationInfoDTO.getStatus() != null) {
                RebateActivityUtil.setRebateActivityStatus(operationInfoDTO);
            }
            operationInfoDTOList.add(operationInfoDTO);
        }
        resultDTO.setList(operationInfoDTOList);
        resultDTO.setTotalCount(count);
        return resultDTO;
    }


    @Override
    public OperationInfoDTO activityDetailQuery(Long activityId, Integer activityType) {
        RebateActivityConfigDO operationConfigById = rebateActivityConfigDomainService.getOperationConfigById(activityId);
        if (operationConfigById == null) {
            return null;
        }
        return RebateActivityConverter.convertRebateConfig2OperationInfoDTO(operationConfigById);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean activityCreate(OperationInfoDTO activityInfo) {
        //参数校验
        AssertUtil.notNull(activityInfo.getActivityType(), "活动类型不能为空");
        AssertUtil.notEmpty(activityInfo.getActivityName(), "活动名称不能为空");
        AssertUtil.notEmpty(activityInfo.getRules(), "活动规则不能为空");
        AssertUtil.notNull(activityInfo.getStartTime(), "活动开始时间不能为空");
        AssertUtil.notNull(activityInfo.getEndTime(), "活动结束时间不能为空");
        AssertUtil.notEmpty(activityInfo.getDescription(), "活动介绍不能为空");
        AssertUtil.notNull(activityInfo.getDeliveryRestriction(), "投放限制不能为空");
        AssertUtil.assertNotEmpty(activityInfo.getDeliveryRestriction().getBuLines(), "投放限制-业务线不能为空");
        AssertUtil.assertNotEmpty(activityInfo.getDeliveryRestriction().getFirstCategories(), "投放限制-一级类目不能为空");
        AssertUtil.assertNotEmpty(activityInfo.getDeliveryRestriction().getCityDict(), "投放限制-城市不能为空");
        AssertUtil.notNull(activityInfo.getRebateActivityInfo(), "返利活动信息不能为空");
        AssertUtil.notNull(activityInfo.getRebateActivityInfo().getRebateActivityType(), "返利活动类型不能为空");
        RuleDTO rule = activityInfo.getRebateActivityInfo().getRule();
        AssertUtil.notNull(rule.getRebateRule(), "返利规则不能为空");
        AssertUtil.notNull(rule.getRebateRule().getType(), "返利类型不能为空");

        RebateSettleSellerSubsidyProportionRuleDTO sellerSubsidyProportionRule = rule.getRebateRule().getSellerSubsidyProportionRule();
        AssertUtil.notNull(sellerSubsidyProportionRule, "返利规则-商家补贴比例不能为空");
        AssertUtil.notNull(sellerSubsidyProportionRule.getRebateProportion(), "返利规则-商家补贴比例-比例百分比不能为空");
        AssertUtil.isTrue(activityInfo.getEndTime() > activityInfo.getStartTime(), "活动结束时间必须大于活动开始时间");
        AssertUtil.isTrue(activityInfo.getStartTime() > new Date().getTime(), "活动开始时间必须大于当前时间");
        AssertUtil.notNull(activityInfo.getPriority(), "活动优先级不能为空");
        AssertUtil.isTrue(activityInfo.getPriority() >= 0, "活动优先级必须大于等于0");
        AssertUtil.isTrue(activityInfo.getPriority() <= 1000, "活动优先级必须小于等于1000");

        List<RebateSettleConditionDTO> conditionList = rule.getRebateRule().getCondition();
        AssertUtil.assertNotEmpty(conditionList, "返利规则-参与条件不能为空");
        List<RebateSettleConditionDTO> expansionCondition = conditionList.stream().filter(condition -> RebateSettleConditionKeyEnum.EXPANSION_TYPE.code.equals(condition.getKey())).collect(Collectors.toList());
        AssertUtil.assertNotEmpty(expansionCondition, "返利规则-参与条件-膨胀类型不能为空");

        RebateActivityConfigDO rebateActivityConfigDO = RebateActivityConverter.convert2RebateActivityConfigDO(activityInfo);

        Long activityId = rebateActivityConfigDomainService.createRebateActivityConfig(rebateActivityConfigDO);
        //发起审批
        List<NodeDataDTO> nodeDataDTOList = ApolloAuditParamConverter.buildOrderActivityAuditNodeData(activityInfo);
        User user = new User(UserTypeEnum.SSO_USER, Math.toIntExact(activityInfo.getLoginUserId()));
        Result<Long> submitAuditResult = apolloAuditService.submitAudit(ApolloAuditConstant.ACTIVITY_AUDIT_PROCESS_TYPE, activityId, user, nodeDataDTOList);

        AssertUtil.isTrue(submitAuditResult.isSuccess(), "提交审核失败,原因：" + submitAuditResult.getMsg());

        rebateActivityConfigDO = rebateActivityConfigDomainService.getOperationConfigById(activityId);
        //更新审批链接
        Map<String, Object> extInfoMap = new HashMap<>();
        extInfoMap.put(OperationExtKeyEnum.AUDIT_ID.getKey(), submitAuditResult.getObj());

        rebateActivityConfigDO.setExtInfo(JSON.toJSONString(extInfoMap));

        rebateActivityConfigDomainService.updateRebateActivityConfig(rebateActivityConfigDO);
        return true;
    }

    @Override
    public void activityUpdateOnlineStatus(Long activityId, Integer activityType, Integer onlineStatus) {
        AssertUtil.notNull(activityType, "活动类型不能为空");
        AssertUtil.notNull(onlineStatus, "状态不能为空");
        AssertUtil.notNull(activityId, "活动id不能为空");
        OperationStatusEnum operationStatusEnum = OperationStatusEnum.getByCode(onlineStatus);
        AssertUtil.notNull(operationStatusEnum, "状态不合法");
        //幂等
        RebateActivityConfigDO originRebateActivityConfig = rebateActivityConfigDomainService.getOperationConfigById(activityId);
        if (onlineStatus.equals(originRebateActivityConfig.getStatus())) {
            return;
        }

        RebateActivityConfigDO rebateActivityConfigDO = new RebateActivityConfigDO();
        rebateActivityConfigDO.setId(activityId);
        rebateActivityConfigDO.setOperationId(activityId);
        rebateActivityConfigDO.setStatus(onlineStatus);
        rebateActivityConfigDomainService.updateRebateActivityConfig(rebateActivityConfigDO);

        //如果是审批中，并且是撤回操作，需要关闭审批流
        if (OperationStatusEnum.ON_APPROVE.code == originRebateActivityConfig.getStatus() && OperationStatusEnum.OFFLINE.code == onlineStatus) {
            apolloAuditService.closeAudit(ApolloAuditConstant.ACTIVITY_AUDIT_PROCESS_TYPE, activityId);
        }
    }

    @Override
    public void activityUpdate(OperationInfoDTO activityInfo) {
        RebateActivityConfigDO rebateActivityConfigDO = rebateActivityConfigDomainService.getOperationConfigById(activityInfo.getActivityId());
        Long id = rebateActivityConfigDO.getId();
        AssertUtil.notNull(rebateActivityConfigDO, "返利活动不存在");
        Integer status = rebateActivityConfigDO.getStatus();
        if (OperationStatusEnum.ONGOING.code == status) {
            throw new BusinessException("活动已上线,不允许修改");
        }
        if (OperationStatusEnum.OFFLINE.code == status) {
            throw new BusinessException("活动已下线,不允许修改");
        }

        rebateActivityConfigDO = RebateActivityConverter.convert2RebateActivityConfigDO(activityInfo);
        //审批中 or 审批未通过 重新发起审批
        if (OperationStatusEnum.ON_APPROVE.code == status || OperationStatusEnum.UNAPPROVED.code == status) {
            List<NodeDataDTO> nodeDataDTOList = ApolloAuditParamConverter.buildOrderActivityAuditNodeData(activityInfo);
            User user = new User(UserTypeEnum.SSO_USER, Math.toIntExact(activityInfo.getLoginUserId()));
            Result<Long> submitAuditResult = apolloAuditService.submitAudit(ApolloAuditConstant.ACTIVITY_AUDIT_PROCESS_TYPE, id, user, nodeDataDTOList);
            AssertUtil.isTrue(submitAuditResult.isSuccess(), "提交审核失败,原因：" + submitAuditResult.getMsg());
            String extInfo = rebateActivityConfigDO.getExtInfo();
            Map<String, Object> extInfoMap;
            if (StringUtil.isNotBlank(extInfo)) {
                extInfoMap = JSON.parseObject(extInfo, new TypeReference<Map<String, Object>>() {
                });
            } else {
                extInfoMap = new HashMap<>();
            }
            extInfoMap.put(OperationExtKeyEnum.AUDIT_ID.getKey(), submitAuditResult.getObj());

            rebateActivityConfigDO.setExtInfo(JSON.toJSONString(extInfoMap));
        }
        rebateActivityConfigDO.setStatus(OperationStatusEnum.ON_APPROVE.code);
        rebateActivityConfigDomainService.updateRebateActivityConfig(rebateActivityConfigDO);
    }
}
