package com.sankuai.medicalcosmetology.offline.code.application.model.order;

import com.meituan.nibtp.trade.client.buy.enums.DistributionTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/4/9
 * @Description:
 */
@Data
public class ScanOrderFilterConfigBO implements Serializable {

    private String sceneCode;

    private FilterRule filterRule;

    private String filterReason;

    @Data
    public class FilterRule implements Serializable {

        private String distributionType;

    }

    public boolean checkDistributionType(OrderInfoBO orderInfoBO) {
        if (filterRule == null || StringUtils.isBlank(filterRule.getDistributionType())) {
            return true;
        }
        if (MapUtils.isNotEmpty(orderInfoBO.getExtDistributionInfo()) && orderInfoBO.getExtDistributionInfo().containsKey(filterRule.getDistributionType())) {
            return true;
        }
        return false;
    }
}
