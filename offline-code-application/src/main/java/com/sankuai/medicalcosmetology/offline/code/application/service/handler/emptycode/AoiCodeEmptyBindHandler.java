package com.sankuai.medicalcosmetology.offline.code.application.service.handler.emptycode;

import com.alibaba.fastjson.JSONObject;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeDTO;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.*;
import com.sankuai.carnation.distribution.empty.code.prebind.request.EmptyCodeBindRequest;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.request.EmptyCodeBindInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.enums.PromoCodeBindTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.application.utils.AssertUtil;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.EmptyCodeAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PromoCodeActivityAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: xiongjunwei
 * @Date: 2025/2/20
 * @Description:
 */
@Slf4j
@Service
public class AoiCodeEmptyBindHandler implements EmptyCodeBindHandlerIface {

    @Resource
    private PromoCodeActivityAclService promoQRCodeAclService;

    @Resource
    private EmptyCodeAclService emptyCodeAclService;

    @Resource
    private ShopMapperService shopMapperService;

    @Override
    public Boolean support(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        boolean isValidBindType = emptyCodeBindInfoRequestDTO.getBindType() != null &&
                PromoCodeBindTypeEnum.fromCode(emptyCodeBindInfoRequestDTO.getBindType()) != null;
        return isValidBindType && emptyCodeBindInfoRequestDTO.getBindType().equals(PromoCodeBindTypeEnum.AOI_CODE.getCode());
    }

    @Override
    public Boolean bind(List<QRCodeConfigDTO> qrCodeConfigDTOList, EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO,
            Long accountId) {
        AssertUtil.isTrue(
                emptyCodeBindInfoRequestDTO.getDpShopId() != null && emptyCodeBindInfoRequestDTO.getDpShopId() > 0L,
                "门店id不合法");
        Long mtShopId = shopMapperService.dp2mt(emptyCodeBindInfoRequestDTO.getDpShopId());
        if (mtShopId == null) {
            log.error("门店映射失败 dpShopId={}", emptyCodeBindInfoRequestDTO.getDpShopId());
            return false;
        }
        emptyCodeBindInfoRequestDTO.setMtShopId(mtShopId);
        for (QRCodeConfigDTO qrCodeConfigDTO : qrCodeConfigDTOList) {
            if (!emptyCodeAclService
                    .bindEmptyCode(createEmptyCodeBindRequest(qrCodeConfigDTO, emptyCodeBindInfoRequestDTO))) {
                log.error("绑定AOI空码失败 qrCodeConfigDTO={}", JSONObject.toJSONString(qrCodeConfigDTO));
                return false;
            }
        }
        log.info("绑定AOI空码成功 emptyCodeBindInfoRequestDTO={}", JSONObject.toJSONString(emptyCodeBindInfoRequestDTO));
        return true;
    }

    private EmptyCodeBindRequest createEmptyCodeBindRequest(QRCodeConfigDTO emptyCodeInfo,
            EmptyCodeBindInfoRequest requestDTO) {
        EmptyCodeBindRequest res = new EmptyCodeBindRequest();
        res.setBizId(0L);
        res.setBizType(EmptyCodeBizTypeEnum.AOI_CODE.getCode());
        res.setBindKeyType(EmptyCodeBindKeyTypeEnum.MT_POI.getCode());
        res.setBindKey(String.valueOf(requestDTO.getMtShopId()));
        res.setCodeSource(EmptyCodeSourceEnum.AOI_CODE.getCode());
        res.setCodeKey(emptyCodeInfo.getSecretKey());
        res.setCodeLink(emptyCodeInfo.getCodeUrl());
        res.setCodeImage(emptyCodeInfo.getImageUrl());
        res.setOperatorType(EmptyCodeOperatorTypeEnum.B_SHOP_ACCOUNT_ID.getCode());
        res.setOperatorId(String.valueOf(requestDTO.getOperatorId()));
        res.setStatus(EmptyCodeBindStatusEnum.BOUND.getCode());
        res.setBindExtString(JSONObject.toJSONString(requestDTO));
        return res;
    }
}
