package com.sankuai.medicalcosmetology.offline.code.application.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/21
 * @Description: 券类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AwardTypeEnum {

    RAINBOW_DRAW(1, "彩虹抽奖"),

    MAGIC_COUPON(2, "神会员券");

    private Integer code;

    private String desc;

    public static AwardTypeEnum getByCode(Integer code) {
        for (AwardTypeEnum value : AwardTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


}
