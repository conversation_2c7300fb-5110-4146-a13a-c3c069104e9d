package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchcancel;

import com.alibaba.fastjson.JSON;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.CancelApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.AccountAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * B端-批量取消报名-参数校验
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.BATCH_CANCEL_APPLY_FOR_B, subScene = ProcessorSubSceneConstants.PARAM_CHECK)
public class BatchCancelParamCheckProcessor implements BaseProcessor<BatchCancelApplyProcessorContext> {

    @Resource
    private AccountAclService accountAclService;

    @Override
    public void execute(BatchCancelApplyProcessorContext context) {
        AssertUtil.notNull(context, "上下文不能为空");
        CancelApplyActivityRequest request = context.getRequest();
        String activityViewId = request.getActivityViewId();
        AssertUtil.notEmpty(activityViewId, "活动id不能为空");
        AssertUtil.isTrue(ResourceEncoder.checkValid(activityViewId), "活动id不合法");
        Long accountId = context.getAccountId();
        AssertUtil.notNull(accountId, "账号id不能为空");
        List<Long> selectedDpShopIds = request.getDpShopIds();
        AssertUtil.assertNotEmpty(selectedDpShopIds, "门店id不能为空");

        List<Long> dpShopListFromAccount = accountAclService.getDpShopListByDpAccountId(accountId);
        AssertUtil.assertNotEmpty(dpShopListFromAccount, "账号下查询不到门店信息");
        List<Long> invalidShopList = selectedDpShopIds.stream().filter(shopId -> !dpShopListFromAccount.contains(shopId)).collect(Collectors.toList());
        AssertUtil.isTrue(CollectionUtils.isEmpty(invalidShopList), "所选门店中无操作权限,门店id:" + JSON.toJSONString(invalidShopList));
    }
}
