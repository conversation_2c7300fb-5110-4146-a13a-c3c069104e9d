package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/8
 * @Description:
 */
@Service
public class PromoCodeLandingPageHandler {

    @Autowired
    private List<PromoCodeLandingPageIface> ifaceList;

    @Autowired
    private DefaultLandingPageHandler defaultLandingPageHandler;

    public String queryPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        return findIface(request).queryPromoCodeLandingPageUrl(request);
    }

    public PromoCodeLandingUrlDTO queryAllPlatformPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        return findIface(request).queryAllPlatformPromoCodeLandingPageUrl(request);
    }

    private PromoCodeLandingPageIface findIface(PromoCodeLandingPageRequest request) {
        if (request.getSource() == null) {
            return defaultLandingPageHandler;
        }
        PromoCodeLandingPageIface selectedIface = ifaceList.stream()
                .filter(iface -> iface.support(request))
                .findFirst()
                .orElse(defaultLandingPageHandler);

        return selectedIface;
    }
}
