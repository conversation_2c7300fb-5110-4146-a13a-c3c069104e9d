package com.sankuai.medicalcosmetology.offline.code.application.model.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/9/15
 **/
@Data
public class OrderSkuBO {

    /**
     * 商品数量
     */
    private int quantity;

    /**
     * 单商品核销数量，未统计次卡核销数
     */
    private int skuVerifyCount;

    /**
     * 单商品核销数量，未统计次卡核销数
     */
    private int totalVerifyCount;

    private long skuId;

    private long productId;

    private String productName;

    /**
     * 单价
     */
    private BigDecimal unitValue;
}
