package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchcancel;

import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityRecordStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityRecordDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * B端-批量取消报名-准备数据
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.BATCH_CANCEL_APPLY_FOR_B, subScene = ProcessorSubSceneConstants.PREPARE_DATA)
public class BatchCancelPrepareDataProcessor implements BaseProcessor<BatchCancelApplyProcessorContext> {

    private static final int BATCH_QUERY_LIMIT = 200;

    @Resource
    private RebateActivityRecordDomainService rebateActivityRecordDomainService;

    @Override
    public void execute(BatchCancelApplyProcessorContext context) {
        List<Long> dpShopIds = context.getRequest().getDpShopIds();
        String activityViewId = context.getRequest().getActivityViewId();
        Long activityConfigId = ResourceEncoder.decodeResourceViewId(activityViewId);

        List<List<Long>> partitions = Lists.partition(dpShopIds, BATCH_QUERY_LIMIT);

        List<RebateActivityRecordDO> validRecordList = Lists.newArrayList();
        for (List<Long> partition : partitions) {
            RebateActivityRecordQuery query = new RebateActivityRecordQuery();
            query.setDpShopIdList(partition);
            query.setStatus(RebateActivityRecordStatusEnum.VALID.code);
            query.setActivityConfigIdList(Lists.newArrayList(activityConfigId));
            List<RebateActivityRecordDO> rebateActivityRecordDOList = rebateActivityRecordDomainService.queryRebateActivityRecordList(query);
            if (CollectionUtils.isEmpty(rebateActivityRecordDOList)) {
                continue;
            }
            for (RebateActivityRecordDO record : rebateActivityRecordDOList) {
                if (Objects.equals(RebateActivityRecordStatusEnum.VALID.code, record.getStatus())) {
                    validRecordList.add(record);
                }
            }

        }
        AssertUtil.assertNotEmpty(validRecordList, "未查询到有效的活动记录");
        context.setRebateActivityRecordDOList(validRecordList);
    }
}
