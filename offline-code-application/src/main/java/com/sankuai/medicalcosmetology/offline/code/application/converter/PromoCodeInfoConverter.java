package com.sankuai.medicalcosmetology.offline.code.application.converter;

import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeInfoDTO;
import org.springframework.stereotype.Service;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/19
 * @Description:
 */

public class PromoCodeInfoConverter {

    public static PromoCodeInfoDTO convert2PromoCodeInfoDTO(QRCodeConfigDTO qrCodeConfigDTO) {
        PromoCodeInfoDTO promoCodeInfoDTO = new PromoCodeInfoDTO();
        promoCodeInfoDTO.setSecretKey(qrCodeConfigDTO.getSecretKey());
        promoCodeInfoDTO.setImgUrl(qrCodeConfigDTO.getImageUrl());
        promoCodeInfoDTO.setSourceUrl(qrCodeConfigDTO.getCodeUrl());
        promoCodeInfoDTO.setBizType(qrCodeConfigDTO.getBizType());
        return promoCodeInfoDTO;
    }
}
