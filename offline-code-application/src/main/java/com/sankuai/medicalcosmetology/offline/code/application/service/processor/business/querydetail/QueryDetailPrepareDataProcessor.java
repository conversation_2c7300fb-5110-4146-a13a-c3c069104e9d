package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querydetail;

import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateActivityDetailResponse;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.converter.operation.RebateActivityConverter;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.RebateActivityUtil;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityConfigDomainService;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityRecordDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;

import javax.annotation.Resource;
import java.util.List;

/**
 * 查询详情页-准备数据
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, subScene = ProcessorSubSceneConstants.PREPARE_DATA)
public class QueryDetailPrepareDataProcessor implements BaseProcessor<QueryDetailProcessorContext> {

    @Resource
    private RebateActivityConfigDomainService rebateActivityConfigDomainService;

    @Resource
    private RebateActivityRecordDomainService rebateActivityRecordDomainService;

    @Override
    public void execute(QueryDetailProcessorContext context) {
        Long accountId = context.getAccountId();

        String activityViewId = context.getActivityViewId();
        //查询活动
        Long activityId = ResourceEncoder.decodeResourceViewId(activityViewId);
        RebateActivityConfigDO rebateActivityConfigDO = rebateActivityConfigDomainService.getOperationConfigById(activityId);
        AssertUtil.notNull(rebateActivityConfigDO, "活动不存在");
        OperationInfoDTO operationInfoDTO = RebateActivityConverter.convertRebateConfig2OperationInfoDTO(rebateActivityConfigDO);
        RebateActivityDTO rebateActivityInfo = operationInfoDTO.getRebateActivityInfo();
        RebateActivityUtil.setRebateActivityStatus(operationInfoDTO);
        RebateActivityDetailResponse rebateActivityDetailResponse = new RebateActivityDetailResponse();
        rebateActivityDetailResponse.setActivityConfigViewId(activityViewId);
        rebateActivityDetailResponse.setRuleType(rebateActivityInfo.getRule().getRebateRule().getType());
        rebateActivityDetailResponse.setFactorType(rebateActivityInfo.getRule().getRebateRule().getFactorType());
        rebateActivityDetailResponse.setActivityName(operationInfoDTO.getActivityName());
        rebateActivityDetailResponse.setStartTime(operationInfoDTO.getStartTime());
        rebateActivityDetailResponse.setEndTime(operationInfoDTO.getEndTime());
        rebateActivityDetailResponse.setRuleTxt(operationInfoDTO.getRules());
        rebateActivityDetailResponse.setActivityDesc(operationInfoDTO.getDescription());
        rebateActivityDetailResponse.setStatus(operationInfoDTO.getStatus());
        //查询当前账号的报名记录

        RebateActivityRecordQuery rebateActivityRecordQuery = new RebateActivityRecordQuery();
        rebateActivityRecordQuery.setRebateActivityType(OperationConfigTypeEnum.GOD_COUPON_REBATE_ACTIVITY_CONFIG.getCode());
        rebateActivityRecordQuery.setActivityConfigIdList(Lists.newArrayList(rebateActivityConfigDO.getId()));
        rebateActivityRecordQuery.setDpShopAccountId(accountId);
        List<RebateActivityRecordDO> rebateActivityRecords = rebateActivityRecordDomainService.queryRebateActivityRecordList(rebateActivityRecordQuery);


        context.setRebateActivityRecords(rebateActivityRecords);
        context.setRebateActivityDetailResponse(rebateActivityDetailResponse);
    }
}
