package com.sankuai.medicalcosmetology.offline.code.application.enums;
import lombok.Getter;
import java.util.Arrays;
@Getter
public enum StaffStatusEnum {
    ON_THE_JOB(1, "在职"),
    DIMISSION(0, "离职");
    public final int code;
    public final String desc;
    StaffStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getDescStr(Object code) {
        if (code instanceof Integer){
            int codeInt = (int) code;
            return Arrays.stream(values()).filter(t -> t.code == codeInt).findFirst().map(StaffStatusEnum::getDesc).orElse("");
        }
        return "";
    }
}