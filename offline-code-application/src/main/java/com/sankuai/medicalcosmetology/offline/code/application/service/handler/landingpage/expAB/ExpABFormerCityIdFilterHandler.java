package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.QueryExpABInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.MagicMemberCouponScenarioDomainEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/12/26
 * @Description:
 */
@Service
public class ExpABFormerCityIdFilterHandler implements ExpABFormerFilterIface {

    @Override
    public boolean support(String key) {
        return "cityId".equals(key);
    }

    @Override
    public boolean queryFilterResult(QueryExpABInfoRequest request, String filterRule) {
        if (StringUtils.isBlank(filterRule)) {
            return false;
        }
        switch (filterRule) {
            case "magicMemberCouponV2":
                Map<String, List> cityIdMap = Lion.getMap(Environment.getAppName(), LionConstant.MAGIC_FLAG_EXP_AB_CITY_FILTER_CONFIG, List.class);
                List<Integer> cityIdList = cityIdMap.get(String.valueOf(request.getPlatform()));
                if (CollectionUtils.isEmpty(cityIdList)) {
                    return true;
                }
                if (cityIdList.contains(-1) || cityIdList.contains(request.getCityId())) {
                    return false;
                }
                return true;
            default:
                return false;
        }
    }
}
