package com.sankuai.medicalcosmetology.offline.code.application.converter.operation;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import org.springframework.beans.BeanUtils;

public class RebateActivityRecordConverter {


    public static RebateActivityRecordDTO convert2RebateActivityRecordDTO(RebateActivityRecordDO rebateActivityRecordDO) {
        if (null == rebateActivityRecordDO) {
            return null;
        }
        RebateActivityRecordDTO rebateActivityRecord = new RebateActivityRecordDTO();
        BeanUtils.copyProperties(rebateActivityRecordDO, rebateActivityRecord);
        return rebateActivityRecord;
    }
}
