package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.application.service.PromoCodeLandingPageUrlService;
import com.sankuai.medicalcosmetology.offline.code.application.utils.LandingPageSwitchFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URLEncoder;
import java.util.Map;

/**
 * 落地页处理器抽象基类
 * 使用模板方法模式封装通用的URL生成逻辑
 */
@Slf4j
public abstract class AbstractLandingPageHandler implements PromoCodeLandingPageIface {

    @Autowired
    protected LandingPageUrlContext urlContext;

    @Autowired
    protected PromoCodeLandingPageUrlService promoCodeLandingPageUrlService;

    @Autowired
    protected LandingPageSwitchFilter landingPageSwitchFilter;

    /**
     * 模板方法：查询优惠码落地页URL
     */
    @Override
    public String queryPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        try {
            // 1. 预处理请求（子类可覆盖）
            preprocessRequest(request);

            // 2. 获取配置和状态信息
            Map<String, String> pageUrlMap = urlContext.getPageUrlMap();
            LandingPageUrlContext.SwitchInfo switches = urlContext.getSwitchInfo(request);
            LandingPageUrlContext.ClientTypeInfo clientType = urlContext.getClientTypeInfo(request);

            // 3. 选择页面URL（子类可覆盖）
            String pageUrl = selectPageUrl(pageUrlMap, clientType, switches, request);

            // 4. 填充URL参数
            pageUrl = fillUrlParameters(pageUrl, clientType, switches, request);

            // 5. 后处理URL（子类可覆盖）
            pageUrl = postprocessUrl(pageUrl, request);

            // 6. 编码处理
            return encodeUrl(pageUrl, pageUrlMap, clientType, switches);

        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".queryPromoCodeLandingPageUrl error, request is {}, exception is", 
                     request, e);
            return "";
        }
    }

    @Override
    public PromoCodeLandingUrlDTO queryAllPlatformPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        PromoCodeLandingUrlDTO promoCodeLandingUrlDTO = new PromoCodeLandingUrlDTO();

        Map<String, String> pageUrlMap = urlContext.getPageUrlMap();
        LandingPageUrlContext.SwitchInfo switches = urlContext.getSwitchInfo(request);

        try {

            LandingPageUrlContext.ClientTypeInfo wxClientType = new LandingPageUrlContext.ClientTypeInfo(true, true, false);

            String wxOriginPageUrl = selectPageUrl(pageUrlMap, wxClientType, switches, request);
            wxOriginPageUrl = fillUrlParameters(wxOriginPageUrl, wxClientType, switches, request);

            String wxLandingUrl = encodeUrl(wxOriginPageUrl, pageUrlMap, wxClientType, switches);
            promoCodeLandingUrlDTO.setWxLandingUrl(wxLandingUrl);
            promoCodeLandingUrlDTO.setWxOriginLandingUrl(wxOriginPageUrl);
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".queryAllPlatformPromoCodeLandingPageUrl error, request is {}, exception is",
                    request, e);
        }

        LandingPageUrlContext.ClientTypeInfo mtClientType = new LandingPageUrlContext.ClientTypeInfo(true, false, false);
        String mtLandingUrl = selectPageUrl(pageUrlMap, mtClientType, switches, request);
        mtLandingUrl = fillUrlParameters(mtLandingUrl, mtClientType, switches, request);

        LandingPageUrlContext.ClientTypeInfo dpClientType = new LandingPageUrlContext.ClientTypeInfo(false, false, false);
        String dpLandingUrl = selectPageUrl(pageUrlMap, dpClientType, switches, request);
        dpLandingUrl = fillUrlParameters(dpLandingUrl, dpClientType, switches, request);

        promoCodeLandingUrlDTO.setMtLandingUrl(mtLandingUrl);
        promoCodeLandingUrlDTO.setDpLandingUrl(dpLandingUrl);
        promoCodeLandingUrlDTO.setNodeLandingUrl(buildNodeLandingPageUrl(request));

        return promoCodeLandingUrlDTO;
    }

    /**
     * 预处理请求 - 钩子方法，子类可覆盖
     */
    protected void preprocessRequest(PromoCodeLandingPageRequest request) {
        // 默认不做处理，子类可覆盖
    }

    /**
     * 选择页面URL - 钩子方法，子类可覆盖
     */
    protected String selectPageUrl(Map<String, String> pageUrlMap,
                                 LandingPageUrlContext.ClientTypeInfo clientType,
                                 LandingPageUrlContext.SwitchInfo switches,
                                 PromoCodeLandingPageRequest request) {
        // 默认使用标准URL选择逻辑
        return urlContext.selectStandardUrl(pageUrlMap, clientType, switches);
    }

    /**
     * 填充URL参数
     */
    protected String fillUrlParameters(String pageUrl, 
                                 LandingPageUrlContext.ClientTypeInfo clientType,
                                 LandingPageUrlContext.SwitchInfo switches,
                                 PromoCodeLandingPageRequest request) {
        pageUrl = promoCodeLandingPageUrlService.fillLandingPageUrl(pageUrl, request, needEncrypt());
        
        Long targetId = request.getCodeType() != PromoCodeType.BRAND_CODE.code 
            ? request.getPoiId() : request.getCustomerId();
            
        return promoCodeLandingPageUrlService.fillLandingPageUrlExt(pageUrl, request.getCodeType(), targetId);
    }

    /**
     * 后处理URL - 钩子方法，子类可覆盖
     */
    protected String postprocessUrl(String pageUrl, PromoCodeLandingPageRequest request) {
        // 默认不做处理，子类可覆盖
        return pageUrl;
    }

    /**
     * URL编码处理
     */
    protected String encodeUrl(String pageUrl, Map<String, String> pageUrlMap,
                             LandingPageUrlContext.ClientTypeInfo clientType,
                             LandingPageUrlContext.SwitchInfo switches) throws Exception {
        if (clientType.isXcx()) {
            // 检查微信原生开关，如果开启则不需要编码
            if (switches.isWechatNativeSwitch() && switches.isWechatNativeDouHuSwitch()) {
                return pageUrl; // 直接返回，不加前缀不编码
            }
            
            String prefixUrl = urlContext.getWxPrefixUrl(pageUrlMap);
            return prefixUrl + URLEncoder.encode(pageUrl, "UTF-8");
        }
        return pageUrl;
    }

    /**
     * 是否需要加密 - 抽象方法，子类必须实现
     */
    protected abstract boolean needEncrypt();
} 