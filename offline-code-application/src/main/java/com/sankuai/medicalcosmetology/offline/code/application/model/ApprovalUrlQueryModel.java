package com.sankuai.medicalcosmetology.offline.code.application.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 审批URL查询模型
 */
@Data
public class ApprovalUrlQueryModel implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 审批类型：x1 或 finance
     */
    private String type;
    
    /**
     * 业务场景标识
     */
    private String sceneId;
    
    /**
     * 活动ID
     */
    private String activityId;
    
    /**
     * 审批跳转URL
     */
    private String url;
} 