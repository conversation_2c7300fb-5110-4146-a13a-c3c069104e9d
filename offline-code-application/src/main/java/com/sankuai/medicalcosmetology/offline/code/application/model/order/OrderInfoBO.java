package com.sankuai.medicalcosmetology.offline.code.application.model.order;

import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.common.enums.AmountType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 功能描述: 订单详情
 *
 * <AUTHOR>
 * @date 2022/04/28
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderInfoBO {

    /**
     * 订单类型
     * @see com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum
     */
    private int orderType;

    /**
     * 订单id
     * 类型到综订单时，值为统一订单id
     */
    private String orderId;

    /**
     * 对外长整型订单id
     */
    private long longOrderId;

    /**
     * @see com.sankuai.carnation.distribution.intention.enums.PlatformEnum
     */
    private int platform;

    /**
     * 用户id
     * 适配平台
     */
    private long userId;

    /**
     * 订单底层记录的点评用户id
     */
    private Long dpUserId;

    /**
     * 订单底层记录的美团用户id
     */
    private Long mtUserId;

    /**
     * 城市id
     * 和 platform 平台保持一致
     */
    private int cityId;

    /**
     * 门店id
     */
    private Long shopId;

    /**
     * 订单记录的下单点评门店id
     */
    private Long dpShopId;

    /**
     * 订单记录的下单美团门店id
     */
    private Long mtShopId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 分销码
     */
    private String distributionCode;

    /**
     * 商品类型
     * @see com.sankuai.carnation.distribution.common.enums.ProductTypeEnum
     */
    private int productType;

    /**
     * 订单商品类型
     * @see com.dianping.pay.order.domain.enums.ProductEnum
     */
    private int orderBizType;

    /**
     * 商品id
     * 适配平台
     */
    private long productId;

    /**
     * 点评商品id
     */
    @Deprecated
    private long dpProductId;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 支付渠道
     */
    private PayPlatform payPlatform;

    /***
     * 支付成功
      */
    private Date buySuccessTime;

    /**
     * 优惠
     */
    private List<OrderDiscountBO> discountList;

    /**
     * 支付详情
     */
    private List<OrderPaymentDetailBO> paymentDetailList;

    /**
     * 交易品
     */
    private List<OrderSkuBO> skuList;

    /**
     * 扩展信息
     */
    private Map<String, String> extDistributionInfo = Maps.newHashMap();


    /**
     * 交易的下单平台
     */
    private int tradePlatform;

    public BigDecimal getThirdPartyAmount() {
        if (CollectionUtils.isEmpty(this.getPaymentDetailList())) {
            return BigDecimal.ZERO;
        }
        BigDecimal thirdPartyAmount = BigDecimal.ZERO;
        for (OrderPaymentDetailBO paymentDetail : this.getPaymentDetailList()) {
            if (paymentDetail.getAmountType() == AmountType.MEITUANPAY.value
                    || paymentDetail.getAmountType() == AmountType.MERCHANT_MEITUANPAY.value
                    || paymentDetail.getAmountType() == AmountType.FINANCIAL_INSTALLMENT.value) {
                thirdPartyAmount = thirdPartyAmount.add(paymentDetail.getAmount());
            }
        }
        return thirdPartyAmount;
    }
}
