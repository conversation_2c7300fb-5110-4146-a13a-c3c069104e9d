package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchapply;

import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.ApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.ProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import lombok.Data;

import java.util.List;

/**
 * 批量报名处理器上下文
 */
@Data
public class BatchApplyProcessorContext implements ProcessorContext {

    /**
     * 批量报名请求
     */
    private ApplyActivityRequest request;

    /**
     * 报名成功的门店ID列表
     */
    private List<Long> applySuccessShopIdList;


    /**
     * 活动配置信息
     */
    private RebateActivityConfigDO rebateActivityConfigDO;
}
