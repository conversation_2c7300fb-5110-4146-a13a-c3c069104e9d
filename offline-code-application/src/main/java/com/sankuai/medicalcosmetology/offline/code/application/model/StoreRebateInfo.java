package com.sankuai.medicalcosmetology.offline.code.application.model;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@Data
public class StoreRebateInfo {
    private Long dpShopId;
    private String dpShopName;
    private String orderId;
    private String orderAmount;
    private String rebateAmount;
    private Date orderTime; //订单核销时间
    private String fetchAccount;

    private Integer settleType;
    private Date withDrawTime;
    private Boolean hasWithDraw;

    private String dealGroupId;
    private String dealGroupName;
}
