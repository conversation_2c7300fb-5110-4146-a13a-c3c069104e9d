package com.sankuai.medicalcosmetology.offline.code.application.service.handler.staffbargain;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.squirrel.client.StoreKey;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ProductDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.StaffBargainCodeDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.BargainStaffTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ProductTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.StaffBargainBizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.RedisKeyConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.StaffBargainConstant;
import com.sankuai.medicalcosmetology.offline.code.application.enums.audit.ContentTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.application.service.DealGroupApplicationService;
import com.sankuai.medicalcosmetology.offline.code.domain.audit.enums.ContentAuditBizType;
import com.sankuai.medicalcosmetology.offline.code.domain.audit.model.AuditContent;
import com.sankuai.medicalcosmetology.offline.code.domain.audit.model.AuditEnv;
import com.sankuai.medicalcosmetology.offline.code.domain.audit.model.ContentAuditRequest;
import com.sankuai.medicalcosmetology.offline.code.domain.audit.service.ContentAuditDomainService;
import com.sankuai.medicalcosmetology.offline.code.domain.content.repository.ContentAuditRecordRepository;
import com.sankuai.medicalcosmetology.offline.code.domain.product.repository.StaffBargainDetailRepository;
import com.sankuai.medicalcosmetology.offline.code.domain.product.service.StaffBargainDomainService;
import com.sankuai.medicalcosmetology.offline.code.domain.staffbargain.config.StaffBargainConfig;
import com.sankuai.medicalcosmetology.offline.code.domain.staffbargain.model.StaffBargainConfigRequest;
import com.sankuai.medicalcosmetology.offline.code.domain.staffbargain.service.StaffBargainConfigService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.cache.RedisCache;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.ContentAuditRecords;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.StaffBargainDetail;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.exception.BizSceneException;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.producer.enums.AuditDataSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.producer.enums.AuditSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.producer.enums.ContentAuditStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.DealGroupAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.QRCodeAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.transaction.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:55
 */
@Component
@Slf4j
public abstract class AbstractStaffBargainCodeHandler implements StaffBargainCodeHandlerIface {

    private final ThreadPoolExecutor EXECUTOR = Rhino.newThreadPool("AbstractStaffBargainCodeHandler",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(10)
                    .withMaxSize(20)
                    .withMaxQueueSize(10000)
                    .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())).getExecutor();

    private final Integer BATCH_SIZE = 20;

    private final ObjectMapper objectMapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    private static final String CAT_TYPE = AbstractStaffBargainCodeHandler.class.getSimpleName();

    @Resource
    private RedisCache redisCache;

    @Resource
    private DealGroupAclService dealGroupAclService;

    @Resource
    private DealGroupApplicationService dealGroupApplicationService;

    @Resource
    private StaffBargainDomainService staffBargainDomainService;

    @Resource
    private StaffBargainDetailRepository staffBargainDetailRepository;

    @Resource
    private ContentAuditRecordRepository contentAuditRecordRepository;

    @Resource
    private TransactionUtil transactionUtil;

    @Resource
    private QRCodeAclService qrCodeAclService;

    @Resource
    private ContentAuditDomainService contentAuditDomainService;

    @Resource
    private StaffBargainConfigService staffBargainConfigService;

    abstract Integer getCodeBizType();

    abstract StaffBargainDetail init(StaffBargainCodeContext context);

    @Override
    public StaffBargainCodeDTO handle(StaffBargainCodeContext context) {
        //接口去重
        duplicatesCheck(context);
        //查询商品信息
        queryProductInfo(context);
        //订单价格限制
        calcOrderPriceLimit(context);
        //保存议价
        saveStaffBargain(context);
        //异步送审
        asyncAudit(context);
        //生成二维码
        createStaffBargainCode(context);
        //封装返回
        fillResponse(context);
        return context.getStaffBargainCodeDTO();
    }

    private void duplicatesCheck(StaffBargainCodeContext context) {
        StoreKey storeKey = new StoreKey(RedisKeyConstant.STAFF_BARGAIN_LOCK, context.getStaffId(), context.getStaffType().getCode());
        if (redisCache.existData(storeKey)) {
            throw new BizSceneException("接口调用频繁，请稍后再试");
        }
        redisCache.setDataToRedis(storeKey, System.currentTimeMillis(), 1);
    }

    private void queryProductInfo(StaffBargainCodeContext context) {
        //这里团单都存dp团单吧，和之前保持一致
        Map<Integer, List<ProductDTO>> type2ProductsMap = context
                .getProductList().stream().collect(Collectors.groupingBy(ProductDTO::getProductType));
        List<ProductDTO> dpDealList = type2ProductsMap.getOrDefault(ProductTypeEnum.TUAN_DEAL.getCode(), Collections.emptyList());
        List<ProductDTO> mtDealList = type2ProductsMap.getOrDefault(ProductTypeEnum.MT_DEAL_GROUP.getCode(), Collections.emptyList());
        List<ProductDTO> preList = type2ProductsMap.getOrDefault(ProductTypeEnum.PREPAY.getCode(), Collections.emptyList());
        Map<Long, Long> mt2DpDealIdMap = queryMt2DpDealId(mtDealList);
        dpDealList.addAll(mt2DpDealIdMap.values().stream().map(dpDealId -> ProductDTO.init(dpDealId, ProductTypeEnum.TUAN_DEAL.getCode())).collect(Collectors.toList()));

        //查询团单信息
        List<Long> dpDealIdList = dpDealList.stream().map(ProductDTO::getProductId).collect(Collectors.toList());
        List<CompletableFuture<Map<Long, DealGroupDTO>>> dealInfoCfList = Lists.partition(dpDealIdList, BATCH_SIZE)
                .stream()
                .map(list -> CompletableFuture.supplyAsync(() -> dealGroupAclService.queryDealGroupDTOByDpIds(list), EXECUTOR))
                .collect(Collectors.toList());
        //查询预付信息
        List<Long> preIdList = preList.stream().map(ProductDTO::getProductId).collect(Collectors.toList());
        List<CompletableFuture<Map<Long, DealGroupDTO>>> preInfoCfList = Lists.partition(preIdList, BATCH_SIZE)
                .stream()
                .map(list -> CompletableFuture.supplyAsync(() -> dealGroupAclService.queryDealGroupByBizProductIdList(list), EXECUTOR))
                .collect(Collectors.toList());

        List<DealGroupDTO> dealInfoList = CompletableFuture.allOf(dealInfoCfList.toArray(new CompletableFuture[0]))
                .thenApply(v -> dealInfoCfList.stream().map(CompletableFuture::join).flatMap(value -> value.values().stream()).collect(Collectors.toList()))
                .join();
        List<DealGroupDTO> preInfoList = CompletableFuture.allOf(preInfoCfList.toArray(new CompletableFuture[0]))
                .thenApply(v -> preInfoCfList.stream().map(CompletableFuture::join).flatMap(value -> value.values().stream()).collect(Collectors.toList()))
                .join();

        context.setMt2DpDealIdMap(mt2DpDealIdMap);
        context.setDealInfoList(dealInfoList);
        context.setPreInfoList(preInfoList);
    }

    private void calcOrderPriceLimit(StaffBargainCodeContext context) {
        BigDecimal minBargainPrice = null;
        BigDecimal maxBargainPrice = null;
        StaffBargainBizTypeEnum bizTypeEnum = null;
        StaffBargainConfig staffbargainConfig = null;
        if (!ObjectUtils.isEmpty(context.getDealInfoList())) {
            DealGroupDTO dealGroupDTO = context.getDealInfoList().get(0);
            boolean isPre = dealGroupApplicationService.checkDealGroupPrePay(dealGroupDTO);
            if (isPre) {
                bizTypeEnum = StaffBargainBizTypeEnum.MULTIPLE_ORDERS_ONE_USER;
                staffbargainConfig = getStaffBargainConfig(bizTypeEnum, context.getStaffType());
                minBargainPrice = staffBargainDomainService.calMinBargainPrice(dealGroupDTO.getDeals().get(0).getPrice().getPrePayPrice(),
                        dealGroupDTO.getPrice().getSalePrice(), staffbargainConfig.getMinBargainPriceRatio());
                maxBargainPrice = staffBargainDomainService.calMaxBargainPrice(
                        dealGroupDTO.getDeals().get(0).getPrice().getPrePayPrice(), staffbargainConfig.getMaxBargainPriceRatio());
            } else {
                bizTypeEnum = StaffBargainBizTypeEnum.SINGLE_ORDER_ONE_USER;
                staffbargainConfig = getStaffBargainConfig(bizTypeEnum, context.getStaffType());
                minBargainPrice = staffBargainDomainService.calNormalDealMinBargainPrice(dealGroupDTO.getPrice().getSalePrice(), staffbargainConfig.getMinBargainPriceRatio());
                maxBargainPrice = staffBargainDomainService.calNormalDealMaxBargainPrice(dealGroupDTO.getPrice().getSalePrice(), staffbargainConfig.getMaxBargainPriceRatio());
            }
        } else if (!ObjectUtils.isEmpty(context.getPreInfoList())) {
            DealGroupDTO dealGroupDTO = context.getPreInfoList().get(0);
            bizTypeEnum = StaffBargainBizTypeEnum.PRE_ORDER_ONE_USER;
            staffbargainConfig = getStaffBargainConfig(bizTypeEnum, context.getStaffType());
            minBargainPrice = staffBargainDomainService.calNormalDealMinBargainPrice(dealGroupDTO.getDeals().get(0).getPrice().getSalePrice(), staffbargainConfig.getMinBargainPriceRatio());
            maxBargainPrice = staffBargainDomainService.calNormalDealMaxBargainPrice(dealGroupDTO.getDeals().get(0).getPrice().getSalePrice(), staffbargainConfig.getMaxBargainPriceRatio());
        }

        if (context.getBargainPrice().compareTo(minBargainPrice) <= 0
                || context.getBargainPrice().compareTo(maxBargainPrice) > 0) {
            Cat.logEvent(CAT_TYPE, "bargainPriceRangeError");
            log.error("bargain price range error context:{}", JSON.toJSONString(context));
            throw new BizSceneException("议价超出上下线限制");
        }

        context.setMinBargainPrice(minBargainPrice);
        context.setMaxBargainPrice(maxBargainPrice);
        context.setBizTypeEnum(bizTypeEnum);
        context.setStaffbargainConfig(staffbargainConfig);
    }

    public void saveStaffBargain(StaffBargainCodeContext context) {
        //保存议价
        StaffBargainDetail staffBargainDetail = init(context);
        transactionUtil.transaction(() -> {
            staffBargainDetailRepository.insert(staffBargainDetail);
            context.setStaffBargainDetail(staffBargainDetail);
            if (!ObjectUtils.isEmpty(context.getPicUrlList())) {
                List<ContentAuditRecords> picRecords = context.getPicUrlList()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(pic -> buildStaffBargainAuditRecord(staffBargainDetail.getId()
                                , StaffBargainConstant.STAFF_BARGAIN_PIC_AUDIT_TYPE, pic, ContentTypeEnum.PIC))
                        .collect(Collectors.toList());
                contentAuditRecordRepository.batchInsertContentAudit(picRecords);
                context.setPicRecords(picRecords);
            }
            if (!ObjectUtils.isEmpty(context.getText())) {
                ContentAuditRecords textRecord = buildStaffBargainAuditRecord(staffBargainDetail.getId()
                        , StaffBargainConstant.STAFF_BARGAIN_TEXT_AUDIT_TYPE, context.getText(), ContentTypeEnum.TEXT);
                contentAuditRecordRepository.insert(textRecord);
                context.setTextRecord(textRecord);
            }
        });
    }

    private void asyncAudit(StaffBargainCodeContext context) {
        //异步送审
        if (!ObjectUtils.isEmpty(context.getPicUrlList())) {
            CompletableFuture.runAsync(() -> contentAuditDomainService
                    .submitAudit(buildPicAuditRequest(context)), EXECUTOR);
        }
        if (!ObjectUtils.isEmpty(context.getTextRecord())) {
            CompletableFuture.runAsync(() -> contentAuditDomainService
                    .submitAudit(buildTextAuditRequest(context)), EXECUTOR);
        }
    }

    private ContentAuditRequest buildPicAuditRequest(StaffBargainCodeContext context) {
        ContentAuditRequest request = new ContentAuditRequest();
        request.setType(ContentAuditBizType.STAFF_BARGAIN_PIC);
        request.setContentList(context.getPicRecords().stream().filter(Objects::nonNull).map(record ->
                AuditContent.builder().bizId(String.valueOf(record.getId())).content(record.getContent()).build()
        ).collect(Collectors.toList()));
        request.setBizId(String.valueOf(context.getStaffBargainDetail().getId()));
        request.setEnv(fillEnv(context));
        return request;
    }

    private ContentAuditRequest buildTextAuditRequest(StaffBargainCodeContext context) {
        ContentAuditRequest request = new ContentAuditRequest();
        request.setType(ContentAuditBizType.STAFF_BARGAIN_TEXT);
        request.setContentList(Lists.newArrayList(context.getTextRecord()).stream().filter(Objects::nonNull).map(record ->
                AuditContent.builder().bizId(String.valueOf(record.getId())).content(record.getContent()).build()
        ).collect(Collectors.toList()));
        request.setBizId(String.valueOf(context.getStaffBargainDetail().getId()));
        request.setEnv(fillEnv(context));
        return request;
    }

    private AuditEnv fillEnv(StaffBargainCodeContext context) {
        AuditEnv env = new AuditEnv();
        if (Objects.equals(context.getStaffType(), BargainStaffTypeEnum.CONSULTANT_TASK)) {

            env.setDataSource(AuditDataSourceEnum.OTHER);
            env.setSource(AuditSourceEnum.WX_UNION_ID);
        }
        if (Objects.equals(context.getStaffType(), BargainStaffTypeEnum.STAFF)) {
            env.setDataSource(AuditDataSourceEnum.MT_DP_CONSUMER);
            env.setSource(Objects.equals(PlatformEnum.DP, context.getEnv().getPlatform())
                    ? AuditSourceEnum.DP_PLATFORM_CONSUMER : AuditSourceEnum.MT_PLATFORM_CONSUMER);
        }
        env.setUserId(context.getEnv().getUserId());
        env.setUserIp(context.getEnv().getUserIp());
        env.setDpid(context.getEnv().getDpId());
        env.setUuid(context.getEnv().getUuid());
        env.setMtgsig(context.getEnv().getMtgsig());
        return env;
    }

    private void createStaffBargainCode(StaffBargainCodeContext context) {
        QRCodeConfigDTO qrCodeConfigDTO = qrCodeAclService.generateQRConfigDTO(context.getStaffBargainDetail().getId(), getCodeBizType(), false, false);
        if (ObjectUtils.isEmpty(qrCodeConfigDTO)) {
            throw new BizSceneException("生成二维码失败");
        }
        context.setQrCodeConfigDTO(qrCodeConfigDTO);
    }

    private void fillResponse(StaffBargainCodeContext context) {
        StaffBargainCodeDTO bargainCode = new StaffBargainCodeDTO();
        bargainCode.setStaffBargainId(context.getStaffBargainDetail().getId());
        bargainCode.setStaffBargainCode(context.getQrCodeConfigDTO().getImageUrl());
        bargainCode.setExpiredTime(context.getStaffbargainConfig().getExpiredTime());
        context.setStaffBargainCodeDTO(bargainCode);
    }

    private ContentAuditRecords buildStaffBargainAuditRecord(Long sceneId, Integer auditType, String content, ContentTypeEnum contentType) {
        ContentAuditRecords record = new ContentAuditRecords();
        record.setSceneId(sceneId);
        record.setAuditType(auditType);
        record.setContentType(contentType.getCode());
        record.setContent(content);
        record.setVerifyStatus(ContentAuditStatusEnum.Auditing.getCode());
        return record;
    }

    public StaffBargainConfig getStaffBargainConfig(StaffBargainBizTypeEnum bizTypeEnum, BargainStaffTypeEnum staffTypeEnum) {
        StaffBargainConfigRequest request = StaffBargainConfigRequest.builder()
                .staffTypeEnum(staffTypeEnum)
                .bargainBizTypeEnum(bizTypeEnum)
                .build();
        return staffBargainConfigService.getStaffBargainConfig(request);
    }

    private Map<Long, Long> queryMt2DpDealId(List<ProductDTO> mtDealList) {
        List<Long> mtDealIdList = mtDealList.stream().map(ProductDTO::getProductId).collect(Collectors.toList());
        List<CompletableFuture<Map<Long, Long>>> cfList = Lists.partition(mtDealIdList, BATCH_SIZE)
                .stream()
                .map(list -> CompletableFuture.supplyAsync(() -> dealGroupAclService.batchMt2Dp(list), EXECUTOR))
                .collect(Collectors.toList());
        Map<Long, Long> mt2DpMap = CompletableFuture.allOf(cfList.toArray(new CompletableFuture[0]))
                .thenApply(v -> cfList.stream().map(CompletableFuture::join)
                        .flatMap(m -> m.entrySet().stream())
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (k1, k2) -> k1)))
                .join();
        if (mtDealList.size() != mt2DpMap.size()) {
            throw new BizSceneException("团单转换异常");
        }
        return mt2DpMap;
    }
}
