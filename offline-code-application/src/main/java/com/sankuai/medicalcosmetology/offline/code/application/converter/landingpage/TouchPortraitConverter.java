package com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage;

import com.alibaba.fastjson.JSON;
import com.dianping.gmkt.event.api.promoqrcode.dto.order.PromoQRCodeOrderDTO;
import com.google.common.collect.Lists;
import com.sankuai.beautycontent.promotereview.dto.TipContentDTO;
import com.sankuai.beautycontent.promotereview.dto.TipContentFe;
import com.sankuai.beautycontent.promotereview.dto.TouchPortraitDTO;
import com.sankuai.beautycontent.promotereview.dto.TouchPortraitRequest;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ButtonContent;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.PopContent;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.ReviewFloatingLayer;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.TextContent;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/12
 * @Description:
 */
public class TouchPortraitConverter {

    public static TouchPortraitRequest convert2TouchPortraitRequest(Long mtShopId, Long dpShopId, Long userId, Integer platform, Integer qrClientType, String orderId) {
        TouchPortraitRequest touchPortraitRequest = new TouchPortraitRequest();
        touchPortraitRequest.setPlatform(platform);
        touchPortraitRequest.setSourcePlat(qrClientType);
        touchPortraitRequest.setDpPoiId(dpShopId);
        if (platform == PlatformEnum.MT.getCode()) {
            // 美团用户id，美团体系必传
            touchPortraitRequest.setMtUserId(userId);
            touchPortraitRequest.setMtPoiId(mtShopId);
        } else {
            touchPortraitRequest.setDpUserId(userId);
        }
        if (StringUtils.isNotEmpty(orderId)) {
            touchPortraitRequest.setUnifiedOrderId(orderId);
        }
        // 14：商家货价落地页促评触点
        touchPortraitRequest.setScene(14);
        return touchPortraitRequest;
    }

    public static ReviewFloatingLayer convert2ReviewFloatingLayer(TouchPortraitDTO touchPortraitDTO, int platform, PromoQRCodeOrderDTO promoQRCodeOrderDTO) {
        if (touchPortraitDTO == null || touchPortraitDTO.getTips() == null) {
            return null;
        }
        String content = touchPortraitDTO.getTips().get("tip1");
        if (content == null) {
            return null;
        }

        List<TipContentFe> tipContentFeList = JSON.parseArray(content, TipContentFe.class);
        if (CollectionUtils.isEmpty(tipContentFeList)) {
            return null;
        }

        TipContentFe tipContentFe = tipContentFeList.stream().filter(tipContent -> tipContent.getScene() != null)
                .findFirst().orElse(null);
        if (tipContentFe == null) {
            return null;
        }

        ReviewFloatingLayer reviewFloatingLayer = new ReviewFloatingLayer();
        reviewFloatingLayer.setTextContentList(transferTextFromTipContentFe(tipContentFe.getContentList()));
        reviewFloatingLayer.setPopContentList(transferPopFromTipContentFe(tipContentFe.getPopDTOList()));

        List<ButtonContent> buttonContentList = Lists.newArrayList();
        ButtonContent buttonContent = new ButtonContent();
        // 只有已核销或者dp才展示去评价按钮
        if (promoQRCodeOrderDTO != null && promoQRCodeOrderDTO.getHasConsumed() != null
                && promoQRCodeOrderDTO.getHasConsumed() == 1) {
            buttonContent.setText("去评价");
            buttonContent.setTextContentList(Lists.newArrayList(TextContent.builder().text("去评价").build()));
        } else if (platform == PlatformEnum.DP.getCode()) {
            buttonContent.setText("去评价");
            buttonContent.setTextContentList(Lists.newArrayList(TextContent.builder().text("去评价").build()));
        }
        if (StringUtils.isNotEmpty(buttonContent.getText()) || CollectionUtils.isNotEmpty(buttonContent.getTextContentList())) {
            buttonContent.setJumpUrl(touchPortraitDTO.getTips().get("url"));
        }
        buttonContentList.add(buttonContent);
        reviewFloatingLayer.setButtonContentList(buttonContentList);
        return reviewFloatingLayer;
    }


    private static List<TextContent> transferTextFromTipContentFe(List<TipContentDTO.ContentDTO> contentList) {
        List<TextContent> textContentList = Lists.newArrayList();
        for (TipContentDTO.ContentDTO contentDTO : contentList) {
            textContentList
                    .add(TextContent.builder().text(contentDTO.getText()).highlight(contentDTO.isHighlight()).build());
        }
        return textContentList;
    }

    private static List<PopContent> transferPopFromTipContentFe(List<TipContentDTO.PopDTO> popDTOList) {
        List<PopContent> popContentList = Lists.newArrayList();
        for (TipContentDTO.PopDTO popDTO : popDTOList) {
            List<TextContent> textContentList = popDTO.getRuleList().stream()
                    .map(rule -> TextContent.builder().text(rule).build()).collect(Collectors.toList());
            popContentList.add(PopContent.builder().title(popDTO.getTitle()).textContentList(textContentList).build());
        }
        return popContentList;
    }
}
