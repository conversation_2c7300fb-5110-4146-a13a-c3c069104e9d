package com.sankuai.medicalcosmetology.offline.code.application.converter.operation;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.*;
import com.dianping.gmkt.event.api.promoqrcode.enums.QRActivityStatus;
import com.dianping.gmkt.event.api.promoqrcode.enums.QRActivityType;
import com.dianping.gmkt.event.api.rebate.dto.RebateAndWithdrawDTO;
import com.dianping.gmkt.event.api.rebate.dto.RebateOrderRecordDTO;
import com.dianping.poi.cateproperty.api.dto.query.POICategoryQuery;
import com.sankuai.medicalcosmetology.offline.code.api.dto.common.DictDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.PicConfigDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.*;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ActivityTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.OperationStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.application.model.RebateOrderRecordWithdrawInfo;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.QRGoodsShelfType;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.ShopConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.BusinessDepartmentConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.OperationConfigQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.PicConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.ResourceConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.BusinessDepartmentConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/29
 * @Description:
 */
public class OperationConverter {

    public static ResourceConfigDO convert2OperationConfigDO(OperationInfoDTO operationInfoDTO) {
        ResourceConfigDO operationConfigDTO = new ResourceConfigDO();
        operationConfigDTO.setId(operationInfoDTO.getActivityId());
        operationConfigDTO.setOperationName(operationInfoDTO.getActivityName());
        if (Objects.nonNull(operationInfoDTO.getActivityType())) {
            operationConfigDTO.setOperationType(OperationConfigTypeEnum.getByCode(Objects
                    .requireNonNull(ActivityTypeEnum.getByCode(operationInfoDTO.getActivityType())).getInnerCode()));
        }
        operationConfigDTO.setDescription(operationInfoDTO.getDescription());
        operationConfigDTO.setTags(operationInfoDTO.getTags());
        if (Objects.nonNull(operationInfoDTO.getStartTime())) {
            operationConfigDTO.setStartTime(new Date(operationInfoDTO.getStartTime()));
        }
        if (Objects.nonNull(operationInfoDTO.getEndTime())) {
            operationConfigDTO.setEndTime(new Date(operationInfoDTO.getEndTime()));
        }
        if (operationInfoDTO.getPlatform() != null) {
            operationConfigDTO.setPlatform(operationInfoDTO.getPlatform());
        }
        if (operationInfoDTO.getBizType() != null) {
            operationConfigDTO.setBizType(BizTypeEnum.fromCode(operationInfoDTO.getBizType()));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getBuLines())) {
            operationConfigDTO.setLimitBuLine(operationInfoDTO.getDeliveryRestriction().getBuLines().stream()
                    .map(buStr -> BusinessDepartmentConfigService.getByCode(Integer.valueOf(buStr.getValue())))
                    .collect(Collectors.toList()));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getFirstCategories())) {
            Set<Integer> firstCategories = operationInfoDTO.getDeliveryRestriction().getFirstCategories().stream()
                    .filter(Objects::nonNull)
                    .filter(fc -> StringUtils.isNotBlank(fc.getValue()) && NumberUtils.isCreatable(fc.getValue()))
                    .map(fc -> Integer.parseInt(fc.getValue())).collect(Collectors.toSet());
            operationConfigDTO.setLimitFirstCategorys(new ArrayList<>(firstCategories));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getSecondCategories())) {
            Set<Integer> secondCategories = operationInfoDTO.getDeliveryRestriction().getSecondCategories().stream()
                    .filter(Objects::nonNull)
                    .filter(sc -> StringUtils.isNotBlank(sc.getValue()) && NumberUtils.isCreatable(sc.getValue()))
                    .map(sc -> Integer.parseInt(sc.getValue())).collect(Collectors.toSet());
            operationConfigDTO.setLimitSecondCategorys(new ArrayList<>(secondCategories));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getCityDict())) {
            Set<Integer> cityIds = operationInfoDTO.getDeliveryRestriction().getCityDict().stream()
                    .filter(Objects::nonNull)
                    .filter(city -> StringUtils.isNotBlank(city.getValue()) && NumberUtils.isCreatable(city.getValue()))
                    .map(city -> Integer.parseInt(city.getValue())).collect(Collectors.toSet());
            operationConfigDTO.setLimitCitys(new ArrayList<>(cityIds));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && StringUtils.isNotBlank(operationInfoDTO.getDeliveryRestriction().getCities()) && operationInfoDTO.getDeliveryRestriction().isLimitCity()) {
            operationConfigDTO.setLimitCitys(Arrays
                    .stream(operationInfoDTO.getDeliveryRestriction().getCities().split(","))
                    .filter(NumberUtils::isCreatable).map(Integer::parseInt).distinct().collect(Collectors.toList()));
        }
        // 1：手动输入（shopIds），2：楼层导入（floorIds）
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && StringUtils.isNotBlank(operationInfoDTO.getDeliveryRestriction().getShopIds())
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() != null
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() == 1) {
            operationConfigDTO.setLimitShops(Arrays
                    .stream(operationInfoDTO.getDeliveryRestriction().getShopIds().split(","))
                    .filter(NumberUtils::isCreatable).map(Long::parseLong).distinct().collect(Collectors.toList()));
            operationConfigDTO.setLimitFloorId(-1L);
        } else if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && StringUtils.isNotBlank(operationInfoDTO.getDeliveryRestriction().getFloorIds())
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() != null
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() == 2) {
            operationConfigDTO
                    .setLimitFloorId(Arrays.stream(operationInfoDTO.getDeliveryRestriction().getFloorIds().split(","))
                            .filter(NumberUtils::isCreatable).map(Long::parseLong).distinct()
                            .collect(Collectors.toList()).get(0));
            operationConfigDTO.setLimitShops(new ArrayList<>());
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && StringUtils.isNotBlank(operationInfoDTO.getDeliveryRestriction().getDealIds())) {
            operationConfigDTO.setLimitDealFloorId(
                    Arrays.stream(operationInfoDTO.getDeliveryRestriction().getDealIds().split(","))
                            .filter(NumberUtils::isCreatable).map(Long::parseLong).distinct()
                            .collect(Collectors.toList()).get(0));
        }
        ResourceDTO resourceConfig = operationInfoDTO.getResourceConfig();
        if (resourceConfig != null) {
            if (resourceConfig.getAwardInfo() != null) {
                operationConfigDTO.setAwardConfig(JSONObject.toJSONString(resourceConfig.getAwardInfo()));
            }
            if (resourceConfig.getBackgroundPic() != null) {
                operationConfigDTO.setBackgroundPic(convert2PicConfigDO(resourceConfig.getBackgroundPic()));
            }
            if (resourceConfig.getBannerPic() != null) {
                operationConfigDTO.setBannerPic(convert2PicConfigDO(resourceConfig.getBannerPic()));
            }
        }
        operationConfigDTO.setStatus(operationInfoDTO.getStatus());
        operationConfigDTO.setCreateUser(operationInfoDTO.getCreator());
        if (MapUtils.isNotEmpty(operationInfoDTO.getExtInfo())) {
            operationConfigDTO.setExtInfo(JSONObject.toJSONString(operationInfoDTO.getExtInfo()));
        }
        operationConfigDTO.setUpdateUser(operationInfoDTO.getUpdator());
        operationConfigDTO.setOrder(operationInfoDTO.getPriority());
        return operationConfigDTO;
    }

    public static ActivityDTO convert2ActivityDTO(OperationInfoDTO operationInfoDTO) {
        if (operationInfoDTO == null) {
            return null;
        }
        ActivityDTO activityDTO = new ActivityDTO();
        activityDTO.setActivityId(operationInfoDTO.getActivityId());
        activityDTO.setActivityName(operationInfoDTO.getActivityName());
        activityDTO.setBizType(operationInfoDTO.getBizType());
        DeliveryRestrictionDTO deliveryRestriction = operationInfoDTO.getDeliveryRestriction();
        if (deliveryRestriction != null) {
            // 1：类目限制，2：门店白名单
            if (deliveryRestriction.getType() == 1) {
                activityDTO.setType(QRActivityType.BIZ_NORMAL.code);
                activityDTO.setCategoryConfig(convertToCategoryConfigDTO(deliveryRestriction));
                activityDTO.setCityConfig(convertToCityConfigDTO(deliveryRestriction));
            } else {
                activityDTO.setType(QRActivityType.SHOP_WHITELIST.code);
                activityDTO.setShopIdConfig(convertToShopIdConfigDTO(deliveryRestriction));
            }
        }
        ResourceDTO resourceConfig = operationInfoDTO.getResourceConfig();
        if (resourceConfig != null) {
            activityDTO.setPicConfig(convertToPicConfigDTO(resourceConfig.getBackgroundPic()));
            activityDTO.setBannerPicConfig(convertToPicConfigDTO(resourceConfig.getBannerPic()));
            activityDTO.setAwardConfig(convertToAwardConfigDTO(resourceConfig.getAwardInfo()));
        }
        activityDTO.setStartTime(new Date(operationInfoDTO.getStartTime()));
        activityDTO.setEndTime(new Date(operationInfoDTO.getEndTime()));
        activityDTO.setShelfConfig(convertToShelfConfigDTO());
        activityDTO.setCreator(operationInfoDTO.getCreator());
        activityDTO.setUpdater(operationInfoDTO.getUpdator());
        if (operationInfoDTO.getCreateTime() != null) {
            activityDTO.setCreateTime(new Date(operationInfoDTO.getCreateTime()));
        }
        if (operationInfoDTO.getUpdateTime() != null) {
            activityDTO.setUpdateTime(new Date(operationInfoDTO.getUpdateTime()));
        }
        return activityDTO;
    }

    private static CategoryConfigDTO convertToCategoryConfigDTO(DeliveryRestrictionDTO resourceConfigDO) {
        CategoryConfigDTO categoryConfigDTO = new CategoryConfigDTO();
        if (CollectionUtils.isNotEmpty(resourceConfigDO.getSecondCategories())) {
            categoryConfigDTO.setLimitCategory(true);
            categoryConfigDTO.setCategoryIds(
                    resourceConfigDO.getSecondCategories().stream().filter(Objects::nonNull).map(DictDTO::getValue)
                            .filter(NumberUtils::isCreatable).map(Integer::parseInt).collect(Collectors.toList()));
        } else {
            categoryConfigDTO.setLimitCategory(false);
            categoryConfigDTO.setCategoryIds(Collections.emptyList());
        }
        return categoryConfigDTO;
    }

    private static ShopIdConfigDTO convertToShopIdConfigDTO(DeliveryRestrictionDTO resourceConfigDO) {
        ShopIdConfigDTO shopIdConfigDTO = new ShopIdConfigDTO();
        if (resourceConfigDO.getShopWhiteType() == 1 && StringUtils.isNotBlank(resourceConfigDO.getShopIds())) {
            shopIdConfigDTO.setType(ShopConfigTypeEnum.SHOP_ID.getCode());
            shopIdConfigDTO.setShopIdsLong(Arrays.asList(resourceConfigDO.getShopIds().split(",")).stream()
                    .filter(NumberUtils::isCreatable).map(Long::parseLong).collect(Collectors.toList()));
            shopIdConfigDTO.setLimitShopId(true);
        } else if (resourceConfigDO.getShopWhiteType() == 2 && StringUtils.isNotBlank(resourceConfigDO.getFloorIds())) {
            shopIdConfigDTO.setType(ShopConfigTypeEnum.FLOOR_ID.getCode());
            shopIdConfigDTO.setDpShopItemId(Long.valueOf(resourceConfigDO.getFloorIds()));
            shopIdConfigDTO.setLimitShopId(true);
        } else {
            shopIdConfigDTO.setLimitShopId(false);
            shopIdConfigDTO.setShopIdsLong(Collections.emptyList());
        }
        return shopIdConfigDTO;
    }

    private static CityConfigDTO convertToCityConfigDTO(DeliveryRestrictionDTO resourceConfigDO) {
        CityConfigDTO cityConfigDTO = new CityConfigDTO();
        if (resourceConfigDO.isLimitCity() && StringUtils.isNotBlank(resourceConfigDO.getCities())) {
            cityConfigDTO.setLimitCity(true);
            cityConfigDTO.setCityIds(Arrays.stream(resourceConfigDO.getCities().split(","))
                    .filter(NumberUtils::isCreatable).map(Integer::parseInt).collect(Collectors.toList()));
        } else {
            cityConfigDTO.setLimitCity(false);
            cityConfigDTO.setCityIds(Collections.emptyList());
        }
        return cityConfigDTO;
    }

    private static com.dianping.gmkt.event.api.promoqrcode.dto.activity.PicConfigDTO
            convertToPicConfigDTO(PicConfigDTO resourceConfigDO) {
        com.dianping.gmkt.event.api.promoqrcode.dto.activity.PicConfigDTO picConfigDTO = new com.dianping.gmkt.event.api.promoqrcode.dto.activity.PicConfigDTO();
        if (resourceConfigDO == null) {
            picConfigDTO.setUrl("");
            picConfigDTO.setUseDefault(true);
        } else {
            picConfigDTO.setUrl(resourceConfigDO.getUrl());
            picConfigDTO.setUseDefault(resourceConfigDO.getUseDefault());
        }
        return picConfigDTO;
    }

    public static List<ShelfConfigDTO> convertToShelfConfigDTO() {
        // 默认所有货架
        List<ShelfConfigDTO> shelfConfigDTOList = new ArrayList<>();
        Arrays.stream(QRGoodsShelfType.values()).filter(t -> !QRGoodsShelfType.YUFU.equals(t)).forEach(sheetConfig -> {
            ShelfConfigDTO shelfConfigDTO = new ShelfConfigDTO();
            shelfConfigDTO.setShelfType(sheetConfig.code);
            shelfConfigDTO.setPriority(sheetConfig.ordinal());
            shelfConfigDTO.setSortType(-1);
            shelfConfigDTOList.add(shelfConfigDTO);
        });
        return shelfConfigDTOList;
    }

    private static AwardConfigDTO convertToAwardConfigDTO(AwardInfoDTO awardConfigDO) {
        if (awardConfigDO == null) {
            return null;
        }
        AwardConfigDTO awardConfigDTO = new AwardConfigDTO();
        // 自动发券
        awardConfigDTO.setAwardState(1);
        // 自动领券
        awardConfigDTO.setDrawType(1);
        List<AwardDTO> awardConfigDTOList = new ArrayList<>();
        List<List<AwardDTO>> multiAwardConfigList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(awardConfigDO.getMainAwardConfig())) {
            for (AwardActivityDTO awardItemDO : awardConfigDO.getMainAwardConfig()) {
                transAwardDTO(awardConfigDTOList, awardItemDO);
            }
        }
        awardConfigDTO.setMainAwardConfig(awardConfigDTOList);
        if (CollectionUtils.isNotEmpty(awardConfigDO.getMultiAwardConfig())) {
            for (List<AwardActivityDTO> multiAwardConfig : awardConfigDO.getMultiAwardConfig()) {
                List<AwardDTO> multiAwardConfigDTOList = new ArrayList<>();
                for (AwardActivityDTO awardItemDO : multiAwardConfig) {
                    transAwardDTO(multiAwardConfigDTOList, awardItemDO);
                }
                multiAwardConfigList.add(multiAwardConfigDTOList);
            }
        }
        awardConfigDTO.setMultiAwardConfig(multiAwardConfigList);
        return awardConfigDTO;
    }

    private static void transAwardDTO(List<AwardDTO> multiAwardConfigDTOList, AwardActivityDTO awardItemDO) {
        AwardDTO awardDTO = new AwardDTO();
        awardDTO.setECode(awardItemDO.getCode());
        awardDTO.setPlatform(awardItemDO.getPlatform());
        awardDTO.setValid(awardItemDO.getValid());
        awardDTO.setName(awardItemDO.getName());
        awardDTO.setStartTime(awardItemDO.getStartTime());
        awardDTO.setEndTime(awardItemDO.getEndTime());
        multiAwardConfigDTOList.add(awardDTO);
    }

    public static PicConfigDO convert2PicConfigDO(PicConfigDTO picConfigDTO) {
        if (picConfigDTO == null) {
            return null;
        }
        PicConfigDO picConfigDO = new PicConfigDO();
        picConfigDO.setUrl(picConfigDTO.getUrl());
        picConfigDO.setUseDefault(picConfigDTO.getUseDefault());
        return picConfigDO;
    }

    public static PicConfigDTO
            convert2PicConfigDTO(PicConfigDTO picConfigDTO) {
        if (picConfigDTO == null) {
            return null;
        }
        PicConfigDTO picConfigDO = new PicConfigDTO();
        picConfigDO.setUrl(picConfigDTO.getUrl());
        picConfigDO.setUseDefault(picConfigDTO.getUseDefault());
        return picConfigDO;
    }

    public static OperationInfoDTO convert2OperationInfoDTO(ResourceConfigDO resourceConfigDO,
                                                            Map<Integer, POICategoryQuery> categoryMap, Map<Integer, CityInfoDTO> cityMap) {
        OperationInfoDTO operationInfoDTO = new OperationInfoDTO();
        if (resourceConfigDO == null) {
            return null;
        }
        operationInfoDTO.setActivityId(resourceConfigDO.getId());
        operationInfoDTO.setActivityName(resourceConfigDO.getOperationName());
        operationInfoDTO.setActivityType(
                Objects.requireNonNull(ActivityTypeEnum.getByInnerCode(resourceConfigDO.getOperationType().getCode()))
                        .getCode());
        operationInfoDTO.setDescription(resourceConfigDO.getDescription());
        operationInfoDTO.setTags(resourceConfigDO.getTags());
        if (Objects.nonNull(resourceConfigDO.getStartTime())) {
            operationInfoDTO.setStartTime(resourceConfigDO.getStartTime().getTime());
        }
        if (Objects.nonNull(resourceConfigDO.getEndTime())) {
            operationInfoDTO.setEndTime(resourceConfigDO.getEndTime().getTime());
        }
        if (Objects.nonNull(resourceConfigDO.getPlatform())) {
            operationInfoDTO.setPlatform(resourceConfigDO.getPlatform());
        }
        if (Objects.nonNull(resourceConfigDO.getBizType())) {
            operationInfoDTO.setBizType(resourceConfigDO.getBizType().getCode());
        }
        operationInfoDTO.setDeliveryRestriction(new DeliveryRestrictionDTO());
        if (CollectionUtils.isNotEmpty(resourceConfigDO.getLimitBuLine())) {
            List<DictDTO> dictDTOS = new ArrayList<>();
            for (BusinessDepartmentConfigDO businessDepartmentEnum : resourceConfigDO.getLimitBuLine()) {
                DictDTO dicDTO = new DictDTO();
                dicDTO.setValue(String.valueOf(businessDepartmentEnum.getCode()));
                dicDTO.setName(businessDepartmentEnum.getName());
                dictDTOS.add(dicDTO);
            }
            operationInfoDTO.getDeliveryRestriction().setBuLines(dictDTOS);
        }
        if (CollectionUtils.isNotEmpty(resourceConfigDO.getLimitFirstCategorys())) {
            operationInfoDTO.getDeliveryRestriction()
                    .setFirstCategories(resourceConfigDO.getLimitFirstCategorys().stream().map(categoryId -> {
                        DictDTO dicDTO = new DictDTO();
                        dicDTO.setValue(String.valueOf(categoryId));
                        POICategoryQuery categoryQuery = categoryMap.get(categoryId);
                        if (categoryQuery != null) {
                            dicDTO.setName(categoryQuery.getCategoryName());
                        }
                        return dicDTO;
                    }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(resourceConfigDO.getLimitSecondCategorys())) {
            operationInfoDTO.getDeliveryRestriction()
                    .setSecondCategories(resourceConfigDO.getLimitSecondCategorys().stream().map(categoryId -> {
                        DictDTO dicDTO = new DictDTO();
                        dicDTO.setValue(String.valueOf(categoryId));
                        POICategoryQuery categoryQuery = categoryMap.get(categoryId);
                        if (categoryQuery != null) {
                            dicDTO.setName(categoryQuery.getCategoryName());
                        }
                        return dicDTO;
                    }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(resourceConfigDO.getLimitCitys())) {
            operationInfoDTO.getDeliveryRestriction()
                    .setCityDict(resourceConfigDO.getLimitCitys().stream().map(cityId -> {
                        DictDTO dicDTO = new DictDTO();
                        dicDTO.setValue(String.valueOf(cityId));
                        CityInfoDTO cityInfoDTO = cityMap.get(cityId);
                        if (cityInfoDTO != null) {
                            dicDTO.setName(cityInfoDTO.getCityName());
                        }
                        if (cityId == 0) {
                            dicDTO.setName("全部");
                        }
                        return dicDTO;
                    }).collect(Collectors.toList()));
            operationInfoDTO.getDeliveryRestriction().setCities(resourceConfigDO.getLimitCitys().stream().map(String::valueOf).collect(Collectors.joining(",")));
            operationInfoDTO.getDeliveryRestriction().setLimitCity(true);
        }
        if (CollectionUtils.isNotEmpty(resourceConfigDO.getLimitShops())) {
            operationInfoDTO.getDeliveryRestriction().setShopIds(
                    resourceConfigDO.getLimitShops().stream().map(String::valueOf).collect(Collectors.joining(",")));
            operationInfoDTO.getDeliveryRestriction().setShopWhiteType(1);
            operationInfoDTO.getDeliveryRestriction().setFloorIds(null);
        } else if (Objects.nonNull(resourceConfigDO.getLimitFloorId()) && resourceConfigDO.getLimitFloorId() > 0) {
            operationInfoDTO.getDeliveryRestriction().setFloorIds(String.valueOf(resourceConfigDO.getLimitFloorId()));
            operationInfoDTO.getDeliveryRestriction().setShopWhiteType(2);
            operationInfoDTO.getDeliveryRestriction().setShopIds(null);
        }
        if (Objects.nonNull(resourceConfigDO.getLimitDealFloorId())) {
            operationInfoDTO.getDeliveryRestriction()
                    .setDealIds(String.valueOf(resourceConfigDO.getLimitDealFloorId()));
        }
        ResourceDTO resourceConfig = new ResourceDTO();
        if (StringUtils.isNotBlank(resourceConfigDO.getAwardConfig())) {
            AwardInfoDTO awardConfig = JSONObject.parseObject(resourceConfigDO.getAwardConfig(), AwardInfoDTO.class);
            resourceConfig.setAwardInfo(awardConfig);
        }
        if (resourceConfigDO.getBackgroundPic() != null) {
            resourceConfig.setBackgroundPic(convert2PicConfigDO(resourceConfigDO.getBackgroundPic()));
        }
        if (resourceConfigDO.getBannerPic() != null) {
            resourceConfig.setBannerPic(convert2PicConfigDO(resourceConfigDO.getBannerPic()));
        }
        resourceConfig.setResourceType(resourceConfigDO.getResourceType());
        boolean isCouponRelatedType = Objects.equals(OperationConfigTypeEnum.COUPON_RESOURCE_CONFIG, resourceConfigDO.getOperationType())
                || Objects.equals(OperationConfigTypeEnum.COUPON_LANDING_PAGE_PICTURE_CONFIG, resourceConfigDO.getOperationType())
                || Objects.equals(OperationConfigTypeEnum.POST_PURCHASE_COUPON_CONFIG, resourceConfigDO.getOperationType());

        boolean hasShopOrFloorLimit = CollectionUtils.isNotEmpty(resourceConfigDO.getLimitShops())
                || (resourceConfigDO.getLimitFloorId() != null && resourceConfigDO.getLimitFloorId() > 0);

        int restrictionType = (isCouponRelatedType && hasShopOrFloorLimit) ? 2 : 1;
        operationInfoDTO.getDeliveryRestriction().setType(restrictionType);
        operationInfoDTO.setResourceConfig(resourceConfig);
        operationInfoDTO.setStatus(resourceConfigDO.getStatus());
        if (StringUtils.isNotBlank(resourceConfigDO.getExtInfo()) && resourceConfigDO.getExtInfo().startsWith("{") && resourceConfigDO.getExtInfo().endsWith("}")) {
            operationInfoDTO.setExtInfo(JSONObject.parseObject(resourceConfigDO.getExtInfo(), new TypeReference<Map<String, Object>>() {}));
        }
        operationInfoDTO.setCreator(resourceConfigDO.getCreateUser());
        operationInfoDTO.setUpdator(resourceConfigDO.getUpdateUser());
        operationInfoDTO.setPriority(resourceConfigDO.getOrder());
        if (Objects.nonNull(resourceConfigDO.getAddTime())) {
            operationInfoDTO.setCreateTime(resourceConfigDO.getAddTime().getTime());
        }
        if (Objects.nonNull(resourceConfigDO.getUpdateTime())) {
            operationInfoDTO.setUpdateTime(resourceConfigDO.getUpdateTime().getTime());
        }
        return operationInfoDTO;
    }

    public static PicConfigDTO convert2PicConfigDO(PicConfigDO picConfig) {
        if (picConfig == null) {
            return null;
        }
        PicConfigDTO picConfigDTO = new PicConfigDTO();
        picConfigDTO.setUrl(picConfig.getUrl());
        picConfigDTO.setUseDefault(picConfig.getUseDefault());
        return picConfigDTO;
    }

    public static OperationConfigQuery convert2OperationConfigQuery(OperationPageQueryDTO pageQuery) {
        OperationConfigQuery operationConfigQuery = new OperationConfigQuery();
        if (StringUtils.isNotBlank(pageQuery.getActivityId()) && NumberUtils.isCreatable(pageQuery.getActivityId())) {
            operationConfigQuery.setId(Long.valueOf(pageQuery.getActivityId()));
        }
        if (StringUtils.isNotBlank(pageQuery.getActivityName())) {
            operationConfigQuery.setName(pageQuery.getActivityName());
        }
        if (StringUtils.isNotBlank(pageQuery.getBizType()) && NumberUtils.isCreatable(pageQuery.getBizType())) {
            operationConfigQuery.setBizType(Integer.valueOf(pageQuery.getBizType()));
        }
        if (pageQuery.getActivityType() != null) {
            operationConfigQuery
                    .setOperationConfigType(ActivityTypeEnum.getByCode(pageQuery.getActivityType()).getCode());
        }
        if (StringUtils.isNotBlank(pageQuery.getBuLines()) && NumberUtils.isCreatable(pageQuery.getBuLines())) {
            operationConfigQuery.setBuLine(BusinessDepartmentConfigService.getByCode(Integer.parseInt(pageQuery.getBuLines())));
        }
        if (StringUtils.isNotBlank(pageQuery.getFirstCategory())
                && NumberUtils.isCreatable(pageQuery.getFirstCategory())) {
            operationConfigQuery.setFirstCategory(Integer.parseInt(pageQuery.getFirstCategory()));
        }
        if (StringUtils.isNotBlank(pageQuery.getSecondCategory())
                && NumberUtils.isCreatable(pageQuery.getSecondCategory())) {
            operationConfigQuery.setSecondCategory(Integer.parseInt(pageQuery.getSecondCategory()));
        }
        if (StringUtils.isNotBlank(pageQuery.getCity()) && NumberUtils.isCreatable(pageQuery.getCity())) {
            operationConfigQuery.setCityId(Integer.parseInt(pageQuery.getCity()));
        }
        if (StringUtils.isNotBlank(pageQuery.getCreator())) {
            operationConfigQuery.setCreator(pageQuery.getCreator());
        }
        if (pageQuery.getPageNum() != null) {
            operationConfigQuery.setPageNo(pageQuery.getPageNum());
        } else {
            operationConfigQuery.setPageNo(1);
        }
        if (pageQuery.getPageSize() != null) {
            operationConfigQuery.setPageSize(pageQuery.getPageSize());
        } else {
            operationConfigQuery.setPageSize(10);
        }
        if (pageQuery.getStatus() != null) {
            operationConfigQuery.setStatus(pageQuery.getStatus());
            OperationStatusEnum operationStatusEnum = OperationStatusEnum.getByCode(pageQuery.getStatus());
            if (operationStatusEnum == OperationStatusEnum.NOT_START) {
                operationConfigQuery.setTimeStatus(1);
            }
            if (operationStatusEnum == OperationStatusEnum.END) {
                operationConfigQuery.setTimeStatus(2);
            }
            if (operationStatusEnum == OperationStatusEnum.ONGOING) {
                operationConfigQuery.setTimeStatus(3);
            }
        }
        return operationConfigQuery;
    }

    public static OperationInfoDTO convert2ResourceConfigDO(ActivityLiteDTO activityLiteDTO) {
        if (activityLiteDTO == null) {
            return null;
        }
        OperationInfoDTO resourceConfigDO = new OperationInfoDTO();
        resourceConfigDO.setActivityId(activityLiteDTO.getActivityId());
        resourceConfigDO.setActivityName(activityLiteDTO.getActivityName());
        resourceConfigDO.setActivityType(OperationConfigTypeEnum.COUPON_RESOURCE_CONFIG.getCode());
        resourceConfigDO.setStartTime(activityLiteDTO.getStartTime().getTime());
        resourceConfigDO.setEndTime(activityLiteDTO.getEndTime().getTime());
        resourceConfigDO.setBizType(activityLiteDTO.getBizType());
        resourceConfigDO.setCreator(activityLiteDTO.getCreator());
        resourceConfigDO.setStatus(getStatus(activityLiteDTO));
        resourceConfigDO.setUpdator(activityLiteDTO.getUpdater());
        resourceConfigDO.setCreator(activityLiteDTO.getCreator());
        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setAwardInfo(convert2AwardConfigDTO(activityLiteDTO));
        resourceConfigDO.setResourceConfig(resourceDTO);
        return resourceConfigDO;
    }

    public static OperationInfoDTO convert2ResourceConfigDTO(ActivityDTO activityDTO,
            Map<Integer, POICategoryQuery> categoryMap, Map<Integer, CityInfoDTO> cityMap) {
        if (activityDTO == null) {
            return null;
        }
        OperationInfoDTO resourceConfigDO = new OperationInfoDTO();
        resourceConfigDO.setActivityId(activityDTO.getActivityId());
        resourceConfigDO.setActivityName(activityDTO.getActivityName());
        resourceConfigDO.setActivityType(OperationConfigTypeEnum.COUPON_RESOURCE_CONFIG.getCode());
        if (activityDTO.getStartTime() != null) {
            resourceConfigDO.setStartTime(activityDTO.getStartTime().getTime());
        }
        if (activityDTO.getEndTime() != null) {
            resourceConfigDO.setEndTime(activityDTO.getEndTime().getTime());
        }
        resourceConfigDO.setBizType(activityDTO.getBizType());
        resourceConfigDO.setCreator(activityDTO.getCreator());
        resourceConfigDO.setUpdator(activityDTO.getUpdater());
        resourceConfigDO.setCreator(activityDTO.getCreator());
        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setAwardInfo(convert2AwardConfigDTO(activityDTO));
        resourceDTO.setBannerPic(convertToPicConfigDTO(activityDTO.getBannerPicConfig()));
        resourceDTO.setBackgroundPic(convertToPicConfigDTO(activityDTO.getPicConfig()));
        resourceConfigDO.setResourceConfig(resourceDTO);
        DeliveryRestrictionDTO deliveryRestrictionDTO = new DeliveryRestrictionDTO();
        if (activityDTO.getType() == null) {
            return resourceConfigDO;
        }
        if (QRActivityType.SHOP_WHITELIST.code == activityDTO.getType()) {
            // 1：类目限制，2：门店白名单
            deliveryRestrictionDTO.setType(2);
            if (activityDTO.getShopIdConfig() != null) {
                if (ShopConfigTypeEnum.SHOP_ID.getCode() == activityDTO.getShopIdConfig().getType()
                        && CollectionUtils.isNotEmpty(activityDTO.getShopIdConfig().getShopIdsLong())) {
                    // 1：手动输入（shopIds），2：楼层导入（floorIds）
                    deliveryRestrictionDTO.setShopWhiteType(1);
                    deliveryRestrictionDTO.setShopIds(activityDTO.getShopIdConfig().getShopIdsLong().stream()
                            .map(String::valueOf).collect(Collectors.joining(",")));
                }
                if (ShopConfigTypeEnum.FLOOR_ID.getCode() == activityDTO.getShopIdConfig().getType()
                        && activityDTO.getShopIdConfig().getDpShopItemId() != null) {
                    deliveryRestrictionDTO.setFloorIds(String.valueOf(activityDTO.getShopIdConfig().getDpShopItemId()));
                    deliveryRestrictionDTO.setShopWhiteType(2);
                }
            }
        }
        if (QRActivityType.BIZ_NORMAL.code == activityDTO.getType()) {
            // 1：类目限制，2：门店白名单
            deliveryRestrictionDTO.setType(1);
            if (activityDTO.getCategoryConfig() != null
                    && activityDTO.getCategoryConfig().getLimitCategory()
                    && CollectionUtils.isNotEmpty(activityDTO.getCategoryConfig().getCategoryIds())) {
                deliveryRestrictionDTO
                        .setSecondCategories(activityDTO.getCategoryConfig().getCategoryIds().stream().map(c -> {
                            DictDTO dicDTO = new DictDTO();
                            POICategoryQuery categoryQuery = categoryMap.get(c);
                            if (categoryQuery != null) {
                                dicDTO.setName(categoryQuery.getCategoryName());
                            }
                            dicDTO.setValue(String.valueOf(c));
                            return dicDTO;
                        }).collect(Collectors.toList()));
            }
            if (activityDTO.getCityConfig() != null
                    && activityDTO.getCityConfig().getLimitCity()
                    && CollectionUtils.isNotEmpty(activityDTO.getCityConfig().getCityIds())) {
                deliveryRestrictionDTO.setCities(activityDTO.getCityConfig().getCityIds().stream().map(String::valueOf)
                        .collect(Collectors.joining(",")));
                deliveryRestrictionDTO.setLimitCity(activityDTO.getCityConfig().getLimitCity());
            }
        }
        resourceConfigDO.setDeliveryRestriction(deliveryRestrictionDTO);
        return resourceConfigDO;
    }

    private static PicConfigDTO
            convertToPicConfigDTO(com.dianping.gmkt.event.api.promoqrcode.dto.activity.PicConfigDTO resourceConfigDO) {
        if (resourceConfigDO == null) {
            return null;
        }
        PicConfigDTO picConfigDTO = new PicConfigDTO();
        picConfigDTO.setUrl(resourceConfigDO.getUrl());
        picConfigDTO.setUseDefault(resourceConfigDO.getUseDefault());
        return picConfigDTO;
    }

    private static Integer getStatus(ActivityLiteDTO configDO) {
        if (configDO.getStatus() == QRActivityStatus.OFFLINE.code) {
            return OperationStatusEnum.OFFLINE.code;
        } else if (QRActivityStatus.NOT_START.code == configDO.getStatus()) {
            return OperationStatusEnum.NOT_START.code;
        } else if (QRActivityStatus.ONGOING.code == configDO.getStatus()) {
            return OperationStatusEnum.ONGOING.code;
        } else if (QRActivityStatus.END.code == configDO.getStatus()) {
            return OperationStatusEnum.END.code;
        }
        return null;
    }

    private static AwardInfoDTO convert2AwardConfigDTO(ActivityLiteDTO activityDTO) {
        AwardInfoDTO awardConfigDO = new AwardInfoDTO();
        if (CollectionUtils.isNotEmpty(activityDTO.getMainAwardConfig())) {
            List<AwardActivityDTO> mainAwardInfoList = new ArrayList<>();
            for (AwardDTO awardConfig : activityDTO.getMainAwardConfig()) {
                transAwardDTO(awardConfig, mainAwardInfoList);
            }
            awardConfigDO.setMainAwardConfig(mainAwardInfoList);
        }
        return awardConfigDO;
    }

    private static AwardInfoDTO convert2AwardConfigDTO(ActivityDTO activityDTO) {
        AwardConfigDTO awardConfigDTO = activityDTO.getAwardConfig();
        AwardInfoDTO awardConfigDO = new AwardInfoDTO();
        if (awardConfigDTO == null) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(awardConfigDTO.getMainAwardConfig())) {
            List<AwardActivityDTO> mainAwardInfoList = new ArrayList<>();
            for (AwardDTO awardConfig : awardConfigDTO.getMainAwardConfig()) {
                transAwardDTO(awardConfig, mainAwardInfoList);
            }
            awardConfigDO.setMainAwardConfig(mainAwardInfoList);
        }
        if (CollectionUtils.isNotEmpty(awardConfigDTO.getMultiAwardConfig())) {
            List<List<AwardActivityDTO>> multiAwardInfoList = new ArrayList<>();
            for (List<AwardDTO> awardConfigList : awardConfigDTO.getMultiAwardConfig()) {
                List<AwardActivityDTO> awardInfoList = new ArrayList<>();
                if (CollectionUtils.isEmpty(awardConfigList)) {
                    continue;
                }
                for (AwardDTO awardConfig : awardConfigList) {
                    transAwardDTO(awardConfig, awardInfoList);
                }
                multiAwardInfoList.add(awardInfoList);
            }
            awardConfigDO.setMultiAwardConfig(multiAwardInfoList);
        }
        return awardConfigDO;
    }

    private static void transAwardDTO(AwardDTO awardConfig, List<AwardActivityDTO> mainAwardInfoList) {
        AwardActivityDTO awardInfoDO = new AwardActivityDTO();
        awardInfoDO.setCode(awardConfig.getECode());
        awardInfoDO.setName(awardConfig.getName());
        awardInfoDO.setValid(awardConfig.getValid());
        awardInfoDO.setPlatform(awardConfig.getPlatform());
        awardInfoDO.setStartTime(awardConfig.getStartTime());
        awardInfoDO.setEndTime(awardConfig.getEndTime());
        mainAwardInfoList.add(awardInfoDO);
    }


    public static RebateOrderRecordWithdrawInfo convertToRebateOrderRecordWithdrawInfo(RebateOrderRecordDTO dto) {
        if (dto == null) {
            return null;
        }

        RebateOrderRecordWithdrawInfo info = new RebateOrderRecordWithdrawInfo();

        // Copy matching properties
        info.setId(dto.getId());
        info.setActivityConfigId(dto.getActivityConfigId());
        info.setActivityRecordId(dto.getActivityRecordId());
        info.setUnifiedOrderId(dto.getUnifiedOrderId());
        info.setLongOrderId(dto.getLongOrderId());
        info.setTotalAmount(dto.getTotalAmount());
        info.setActualPayAmount(dto.getActualPayAmount());
        info.setRebateAmount(dto.getRebateAmount());
        info.setDpShopId(dto.getDpShopId());
        info.setMtShopId(dto.getMtShopId());
        info.setUserId(dto.getUserId());
        info.setPlatform(dto.getPlatform());
        info.setScanTime(dto.getScanTime());
        info.setOrderTime(dto.getOrderTime());
        info.setVerifyTime(dto.getVerifyTime());
        info.setStatus(dto.getStatus());
        info.setCreateTime(dto.getCreateTime());
        info.setUpdateTime(dto.getUpdateTime());
        info.setGroupDifference(dto.getGroupDifference());
        info.setSettleType(null);
        info.setWithDrawTime(null);
        info.setHasWithDraw(null);

        return info;
    }

    public static RebateOrderRecordWithdrawInfo convertToRebateOrderRecordWithdrawInfo(RebateAndWithdrawDTO dto) {
        if (dto == null) {
            return null;
        }

        RebateOrderRecordWithdrawInfo info = new RebateOrderRecordWithdrawInfo();

        // Copy properties from DTO to Info
        info.setId(dto.getId());
        info.setActivityConfigId(dto.getActivityConfigId());
        info.setActivityRecordId(dto.getActivityRecordId());
        info.setUnifiedOrderId(dto.getUnifiedOrderId());
        info.setLongOrderId(dto.getLongOrderId());
        info.setTotalAmount(dto.getTotalAmount());
        info.setActualPayAmount(dto.getActualPayAmount());
        info.setRebateAmount(dto.getRebateAmount());
        info.setDpShopId(dto.getDpShopId());
        info.setMtShopId(dto.getMtShopId());
        info.setUserId(dto.getUserId());
        info.setPlatform(dto.getPlatform());
        info.setScanTime(dto.getScanTime());
        info.setOrderTime(dto.getOrderTime());
        info.setVerifyTime(dto.getVerifyTime());
        info.setStatus(dto.getStatus());
        info.setCreateTime(dto.getCreateTime());
        info.setUpdateTime(dto.getUpdateTime());
        info.setGroupDifference(dto.getGroupDifference());
        info.setSettleType(dto.getSettleType());
        info.setWithDrawTime(dto.getWithDrawTime());
        info.setHasWithDraw(dto.getHasWithDraw());

        return info;
    }

}
