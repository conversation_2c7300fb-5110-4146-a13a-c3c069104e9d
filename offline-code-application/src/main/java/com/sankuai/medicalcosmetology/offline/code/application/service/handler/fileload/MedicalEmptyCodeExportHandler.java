package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.dto.EmptyCodeInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.FileExportContext;
import com.sankuai.medicalcosmetology.offline.code.api.enums.fileload.FileLoadSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeCommonService;
import com.sankuai.medicalcosmetology.offline.code.application.model.sso.User;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ExcelUtil;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ZipUtils;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ElephantPushAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.wrapper.S3Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/22
 * @Description:医疗空码导出
 */
@Service
@Slf4j
public class MedicalEmptyCodeExportHandler implements FileExportProcessHandlerIface<EmptyCodeInfoDTO, User> {

    private static final String MEDICAL_EMPTY_CODE_ZIP_PREFIX = "medical-emptycodezip-";
    private static final String MEDICAL_EMPTY_CODE_LINK_PREFIX = "medical-emptycode-links-";
    
    // 医疗空码的业务类型，使用医疗类型
    private static final Integer MEDICAL_EMPTY_CODE_BIZ_TYPE = BizTypeEnum.MEDICAL_PROMO_CODE.getType();

    private static final ThreadPoolExecutor executorService = Rhino.newThreadPool("medicalEmptyCodeExport", DefaultThreadPoolProperties.Setter()
            .withCoreSize(5).withMaxSize(10).withBlockingQueue(new LinkedBlockingQueue<>(100))).getExecutor();

    @Autowired
    private PromoCodeCommonService promoCodeCommonService;

    @Autowired
    private S3Wrapper s3Wrapper;

    @Autowired
    private ElephantPushAclService elephantPushAclService;

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    @Override
    public boolean support(Integer sceneType) {
        return FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_IMAGE_EXPORT.getType().equals(sceneType) 
                || FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_LINK_EXPORT.getType().equals(sceneType);
    }

    public String executeWithResult(FileExportContext context, User param) {
        if (context == null || context.getParam() == null || !NumberUtils.isCreatable(context.getParam().toString())) {
            throw new IllegalArgumentException("参数错误");
        }
        Integer number = (Integer)context.getParam();
        Validate.isTrue(number != null && number > 0, "下载数量不能为空且必须大于0");
        
        // 根据场景类型进行不同的数量限制校验
        if (FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_IMAGE_EXPORT.getType().equals(context.getSceneType())) {
            // 医疗空码图片下载限制500
            Validate.isTrue(number <= 500, "医疗空码图片下载数量需在1到500之间");
        } else if (FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_LINK_EXPORT.getType().equals(context.getSceneType())) {
            // 医疗空码链接下载限制5000
            Validate.isTrue(number <= 5000, "医疗空码链接下载数量需在1到5000之间");
        }
        
        executorService.submit(() -> {
            try {
                List<EmptyCodeInfoDTO> beanList = readAsBean(context, param);
                processData(beanList, context, param);
            } catch (Exception e) {
                log.error(getClass().getSimpleName() + ".executeWithResult error", e);
            }
        });
        return getResult(null, null);
    };

    /**
     * 获取处理结果
     *
     * @param beanList 处理后的数据对象列表
     * @param param 额外的参数
     * @return 处理结果的字符串表示，可能是生成的报告、统计信息或其他格式化输出
     */
    @Override
    public String getResult(List<EmptyCodeInfoDTO> beanList, User param) {
        return "医疗空码下载任务提交成功，稍后下载链接将以大象通知发出，请注意查收。";
    }

    /**
     * 获取要导出的数据
     *
     * @param param
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    @Override
    public List<EmptyCodeInfoDTO> readAsBean(FileExportContext context, User param) {
        Integer number = (Integer)context.getParam();
        List<EmptyCodeInfoDTO> result = new ArrayList<>();
        
        for (int i = 0; i < number; i++) {
            // 使用医疗业务类型生成空码
            RemoteResponse<EmptyCodeInfoDTO> response = promoCodeCommonService.generateEmptyCode(MEDICAL_EMPTY_CODE_BIZ_TYPE, true, true);
            if (response != null && response.isSuccess() && response.getData() != null) {
                result.add(response.getData());
            } else {
                log.warn("生成医疗空码失败，第{}个", i + 1);
            }
        }
        
        log.info("成功生成{}个医疗空码", result.size());
        return result;
    }

    /**
     * 处理解析后的数据对象列表
     *
     * @param emptyCodeList 解析后的数据对象列表，每个对象代表一个文件的内容
     * @param req
     * @param param 额外的参数
     */
    @Override
    public void processData(List<EmptyCodeInfoDTO> emptyCodeList, FileExportContext req, User param) {
        List<String> users = getUsers(param);
        String fileName = generateFileName(req.getSceneType());
        try {
            File file;
            if (FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_LINK_EXPORT.getType().equals(req.getSceneType())) {
                // 生成Excel链接文件
                file = generateExcelFile(emptyCodeList, fileName);
            } else {
                // 生成zip图片文件
                file = generateZipFile(emptyCodeList, fileName);
            }
            uploadAndNotify(file, users, req.getSceneType());
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + "processData error", e);
            logErrorAndNotify(emptyCodeList, users, e, req.getSceneType());
        }
    }

    private File generateZipFile(List<EmptyCodeInfoDTO> emptyCodeList, String fileName) {
        List<String> imageUrlList = emptyCodeList.stream()
                .map(EmptyCodeInfoDTO::getQrCodeUrl)
                .collect(Collectors.toList());
        return ZipUtils.generateZipByUrls(imageUrlList, fileName);
    }

    private List<String> getUsers(User param) {
        List<String> users = Lion.getList(Environment.getAppName(), "emptycode.receivers", String.class,
                new ArrayList<>());
        users.add(param.getLogin());
        return users;
    }

    private String generateFileName(Integer sceneType) {
        if (FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_LINK_EXPORT.getType().equals(sceneType)) {
            return MEDICAL_EMPTY_CODE_LINK_PREFIX + System.currentTimeMillis();
        } else {
            // zip 文件名
            return MEDICAL_EMPTY_CODE_ZIP_PREFIX + System.currentTimeMillis();
        }
    }

    private File generateExcelFile(List<EmptyCodeInfoDTO> emptyCodeList, String fileName) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("no", "编号");
        headers.put("url", "链接");
        headers.put("key", "空码key值");
        headers.put("originalUrl", "源码链接");
        AtomicInteger index = new AtomicInteger(1);
        List<Map<String, Object>> dataList = emptyCodeList.stream().map(d -> {
            Map<String, Object> map = new HashMap<>();
            map.put("no", index.getAndIncrement());
            map.put("url", d.getQrCodeUrl());
            map.put("key", d.getCodeKey());
            map.put("originalUrl", d.getOriginalUrl());
            return map;
        }).sorted(Comparator.comparingInt(m -> (Integer)m.get("no"))).collect(Collectors.toList());

        return ExcelUtil.generateExcelFile(headers, dataList, fileName, "xlsx");
    }

    private void uploadAndNotify(File file, List<String> users, Integer sceneType) {
        try {
            boolean uploadResult = s3Wrapper.uploadFile(file.getName(), file.getAbsolutePath());
            StringBuilder sb = new StringBuilder();
            sb.append("【").append(Environment.getEnvironment()).append("环境").append("】");
            
            String sceneDesc = FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_IMAGE_EXPORT.getType().equals(sceneType) 
                    ? "医疗空码图片" : "医疗空码链接";
            
            if (uploadResult) {
                sb.append("  ").append(sceneDesc).append("导出成功，下载链接：").append(s3Wrapper.getDownloadUrl(file.getName()));
            } else {
                sb.append("  ").append(sceneDesc).append("文件导出失败，请联系研发产品及研发同学! ");
            }
            elephantPushAclService.pushText(sb.toString(), users);
        } catch (Exception e) {
            log.error("{}.uploadAndNotify error, file absolute path: {}, users: {}", getClass().getSimpleName(), file.getAbsolutePath(), users, e);
            throw new RuntimeException(e);
        } finally {
            file.delete();
        }
    }

    private void logErrorAndNotify(List<EmptyCodeInfoDTO> emptyCodeList, List<String> users, Exception e, Integer sceneType) {
        log.error(getClass().getSimpleName() + ".processData Exception, emptyCodeList:{}",
                JSONObject.toJSONString(emptyCodeList), e);
        
        String sceneDesc = FileLoadSceneEnum.MATERIAL_MEDICAL_EMPTYCODE_IMAGE_EXPORT.getType().equals(sceneType) 
                ? "医疗空码图片" : "医疗空码链接";
        
        elephantPushAclService.pushText("系统异常！" + sceneDesc + "文件导出失败，请联系研发产品及研发同学! ", users);
    }
} 