package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/5/17
 * @Description:
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BrandCodeShopTypeAuthorityConfig implements Serializable {

    private List<Integer> categoryIdList;

    private List<String> shopTypeList;
}
