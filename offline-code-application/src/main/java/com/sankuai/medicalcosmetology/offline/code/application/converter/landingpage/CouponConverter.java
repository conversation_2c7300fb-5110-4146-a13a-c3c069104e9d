package com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.gmkt.event.api.enums.CouponSendSituationEnum;
import com.dianping.gmkt.event.api.enums.DrawSceneEnum;
import com.dianping.gmkt.event.api.enums.RiskClientTypeEnum;
import com.dianping.gmkt.event.api.enums.UserPlatform;
import com.dianping.gmkt.event.api.model.PrecisePrize;
import com.dianping.gmkt.event.api.request.DrawEventRequest;
import com.dianping.gmkt.event.api.v2.model.CouponInfo;
import com.dianping.gmkt.event.api.v2.model.GodMemberContext;
import com.dianping.gmkt.event.api.v2.model.RichDrawLog;
import com.dianping.gmkt.event.api.v2.model.RiskContext;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.ops.remote.RemoteIpGetter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.beauty.idmapper.service.UserMapperService;
import com.meituan.beauty.idmapper.service.VirtualUserMapperService;
import com.meituan.mtrace.Tracer;
import com.sankuai.dz.srcm.userWechatCoupon.dto.UserWechatCouponResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.*;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.CouponTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.MobileOSEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.request.EnvRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingCouponRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.CouponPositionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.model.LandingContext;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DateUtil;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ImgUtils;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.CityAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.UserAccountAclService;
import com.sankuai.nib.magic.member.base.enums.BizEnum;
import com.sankuai.nib.magic.member.base.enums.NibBizLineEnum;
import com.sankuai.nib.mkt.common.base.enums.PageSourceEnum;
import com.sankuai.nib.mkt.common.base.enums.RulePropertyTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.TrafficFlagEnum;
import com.sankuai.nib.mkt.common.base.enums.activity.AggregateStrategyEnum;
import com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum;
import com.sankuai.nib.mkt.common.base.model.Property;
import com.sankuai.nib.mkt.magicBizGray.MagicBizControlResult;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.UserInfoDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.request.QueryMagicalMemberTagRequest;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberCouponDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.QueryMagicalMemberTagResponse;
import com.sankuai.nibmkt.promotion.api.query.model.poi.PoiDTO;
import com.sankuai.nibpt.transparentvalidator.domain.TransparentValidatorParam;
import jodd.util.URLDecoder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/12
 * @Description:
 */
@Component
public class CouponConverter {

    /**
     * 美团微信小程序appid(固定)
     */
    public static String MT_WX_APP_ID = "wxde8ac0a21135c07d";

    @Autowired
    private CityAclService cityAclService;

    @Autowired
    private UserAccountAclService userAccountAclService;

    @Autowired
    private VirtualUserMapperService virtualUserMapperService;

    @Autowired
    private UserMapperService userMapperService;

    public static DrawCouponResultInfo convert2DrawCouponResult(List<CouponInfo> drawCouponList, List<CouponInfo> drawMagicCouponList,
                                                                OperationInfoDTO activityDTO, List<UserWechatCouponResponse> drawWechatCouponList) {
        if (CollectionUtils.isEmpty(drawCouponList) && CollectionUtils.isEmpty(drawMagicCouponList) && CollectionUtils.isEmpty(drawWechatCouponList)) {
            return null;
        }

        DrawCouponResultInfo couponResultInfo = new DrawCouponResultInfo();
        List<ScanCouponInfo> couponList = new ArrayList<>(convertToScanCouponInfoList(drawCouponList, CouponTypeEnum.OFFLINE_CODE));
        List<ScanCouponInfo> magicCouponList = convertToScanCouponInfoList(drawMagicCouponList, CouponTypeEnum.MAGICAL_MEMBER);

        sortCouponList(couponList);
        sortCouponList(magicCouponList);

        List<ScanCouponInfo> resultList = new ArrayList<>(magicCouponList);
        resultList.addAll(couponList);
        
        if (CollectionUtils.isNotEmpty(drawWechatCouponList)) {
            List<ScanCouponInfo> wechatCouponList = wechatConvert2ScanCouponList(drawWechatCouponList);
            if (CollectionUtils.isNotEmpty(wechatCouponList)) {
                sortCouponList(wechatCouponList);
                resultList.addAll(0, wechatCouponList);
            }
        }

        couponResultInfo.setCouponList(resultList);

        couponResultInfo.setDrawBannerPicUrl(ImgUtils.transferImg2Webp(getDrawBannerPicUrl(activityDTO)));
        couponResultInfo.setLatestExpireTime(getLatestExpireTime(couponList));
        return couponResultInfo;
    }

    private static List<ScanCouponInfo> convertToScanCouponInfoList(List<CouponInfo> couponInfoList, CouponTypeEnum couponType) {
        if (CollectionUtils.isEmpty(couponInfoList)) {
            return Collections.emptyList();
        }

        return couponInfoList.stream()
                .filter(Objects::nonNull)
                .map(couponInfo -> createScanCouponInfo(couponInfo, couponType))
                .collect(Collectors.toList());
    }

    private static ScanCouponInfo createScanCouponInfo(CouponInfo couponInfo, CouponTypeEnum couponType) {
        ScanCouponInfo scanCouponInfo = new ScanCouponInfo();
        scanCouponInfo.setAmount(couponInfo.getAmount());
        scanCouponInfo.setLimitAmount(couponInfo.getLimitAmount().stripTrailingZeros().toPlainString());
        scanCouponInfo.setTitle(couponInfo.getCouponTitle());
        scanCouponInfo.setDesc(couponInfo.getCouponDesc());
        scanCouponInfo.setCouponType(couponType.code);
        scanCouponInfo.setStartTime(DateUtil.parse(couponInfo.getBeginUseTime()));
        scanCouponInfo.setEndTime(DateUtil.parse(couponInfo.getEndUseTime()));
        return scanCouponInfo;
    }

    private static void sortCouponList(List<ScanCouponInfo> couponList) {
        couponList.sort(Comparator.comparing(ScanCouponInfo::getAmount).reversed()
                .thenComparing(Comparator.comparing(ScanCouponInfo::getStartTime).reversed()));
    }

    private static String getDrawBannerPicUrl(OperationInfoDTO activityDTO) {
        String defaultPic = Lion.getString(Environment.getAppName(), LionConstant.DEFAULT_DRAW_BANNER_URL,
                "https://img.meituan.net/beautyimg/993e98d3e922d7b44c160077ee95f87e179568.png");

        if (activityDTO == null || activityDTO.getResourceConfig() == null ||
            activityDTO.getResourceConfig().getBackgroundPic() == null ||
            activityDTO.getResourceConfig().getBackgroundPic().getUseDefault() == null ||
            activityDTO.getResourceConfig().getBackgroundPic().getUseDefault() ||
            StringUtils.isBlank(activityDTO.getResourceConfig().getBackgroundPic().getUrl())) {
            return defaultPic;
        }

        return activityDTO.getResourceConfig().getBackgroundPic().getUrl();
    }

    private static Date getLatestExpireTime(List<ScanCouponInfo> couponList) {
        return couponList.stream()
                .min(Comparator.comparing(ScanCouponInfo::getEndTime))
                .map(ScanCouponInfo::getEndTime)
                .orElse(null);
    }

    public static QueryCouponResultInfo convert2QueryCouponResult(List<RichDrawLog> drawLogList) {
        if (CollectionUtils.isEmpty(drawLogList)) {
            return null;
        }
        QueryCouponResultInfo queryCouponResultInfo = new QueryCouponResultInfo();

        // 根据已领取券进行组装
        List<ScanCouponInfo> couponList = drawLogList.stream().map(richDrawLog -> {
            ScanCouponInfo scanCouponInfo = new ScanCouponInfo();
            scanCouponInfo
                    .setAmount(new BigDecimal(richDrawLog.getCouponAmount()).stripTrailingZeros().toPlainString());
            scanCouponInfo.setLimitAmount(richDrawLog.getCouponLimitAmount().stripTrailingZeros().toPlainString());
            scanCouponInfo.setTitle(richDrawLog.getCouponTitle());
            scanCouponInfo.setDesc(richDrawLog.getCouponDesc());
            scanCouponInfo.setStartTime(richDrawLog.getBeginTime());
            scanCouponInfo.setEndTime(richDrawLog.getEndTime());
            return scanCouponInfo;
        }).collect(Collectors.toList());

        // 按照券金额逆序排序，相等情况下再按照抽奖时间逆序排序
        couponList.sort(Comparator.comparing(ScanCouponInfo::getAmount).reversed()
                .thenComparing(Comparator.comparing(ScanCouponInfo::getStartTime).reversed()));
        queryCouponResultInfo.setCouponList(couponList);
        Date minEndTime = couponList.stream().min(Comparator.comparing(ScanCouponInfo::getEndTime))
                .map(ScanCouponInfo::getEndTime).orElse(null);
        queryCouponResultInfo.setLatestExpireTime(minEndTime);
        return queryCouponResultInfo;
    }

    public static List<ScanCouponInfo> convert2ScanCoupon(List<RichDrawLog> drawLogList) {
        if (CollectionUtils.isEmpty(drawLogList)) {
            return Lists.newArrayList();
        }

        // 根据已领取券进行组装
        List<ScanCouponInfo> couponList = drawLogList.stream().map(richDrawLog -> {
            ScanCouponInfo scanCouponInfo = new ScanCouponInfo();
            scanCouponInfo
                    .setAmount(new BigDecimal(richDrawLog.getCouponAmount()).stripTrailingZeros().toPlainString());
            scanCouponInfo.setLimitAmount(richDrawLog.getCouponLimitAmount().stripTrailingZeros().toPlainString());
            scanCouponInfo.setTitle(richDrawLog.getCouponTitle());
            scanCouponInfo.setDesc(richDrawLog.getCouponDesc());
            scanCouponInfo.setStartTime(richDrawLog.getBeginTime());
            scanCouponInfo.setEndTime(richDrawLog.getEndTime());
            scanCouponInfo.setCouponType(CouponTypeEnum.OFFLINE_CODE.getCode());
            return scanCouponInfo;
        }).collect(Collectors.toList());
        return couponList;
    }

    public static List<ScanCouponInfo> magicalConvert2ScanCoupon(QueryMagicalMemberTagResponse magicalMemberCouponResponse, Long shopId) {
        if (magicalMemberCouponResponse == null) {
            return Lists.newArrayList();
        }
        List<MagicalMemberCouponDTO> magicalMemberCouponDTOS = Lists.newArrayList();
        if (MapUtils.isNotEmpty(magicalMemberCouponResponse.getPoiMagicalMemberCouponDTOMap())
                && magicalMemberCouponResponse.getPoiMagicalMemberCouponDTOMap().containsKey(shopId)) {
            magicalMemberCouponDTOS = magicalMemberCouponResponse.getPoiMagicalMemberCouponDTOMap().get(shopId);
        }

        // 根据已领取券进行组装
        List<ScanCouponInfo> couponList = magicalMemberCouponDTOS.stream().map(magicalMemberCouponDTO -> {
            ScanCouponInfo scanCouponInfo = new ScanCouponInfo();
            scanCouponInfo.setAmount(convertFenToYuan(magicalMemberCouponDTO.getCouponAmount()));
            scanCouponInfo.setLimitAmount(convertFenToYuan(magicalMemberCouponDTO.getThreshold()));
            scanCouponInfo.setTitle(magicalMemberCouponDTO.getCouponName());
            //scanCouponInfo.setDesc(richDrawLog.getCouponDesc());
            //scanCouponInfo.setStartTime(richDrawLog.getBeginTime());
            scanCouponInfo.setEndTime(new Date(magicalMemberCouponDTO.getCouponEndTime()));
            scanCouponInfo.setCouponType(CouponTypeEnum.MAGICAL_MEMBER.getCode());
            MagicMemberCouponDTO magicMemberCouponDTO = new MagicMemberCouponDTO();
            magicMemberCouponDTO.setInflatedStatus(magicalMemberCouponDTO.getInflateStatus());
            magicMemberCouponDTO.setMaxInflateAmount(convertFenToYuan(magicalMemberCouponDTO.getMaxInflateAmount()));
            magicMemberCouponDTO.setMmcCategory(magicalMemberCouponDTO.getMmcCategory());
            magicMemberCouponDTO.setCanInflate(magicalMemberCouponDTO.isCanInflate());
            magicMemberCouponDTO.setCouponAmount(convertFenToYuan(magicalMemberCouponDTO.getCouponAmount()));
            magicMemberCouponDTO.setThreshold(convertFenToYuan(magicalMemberCouponDTO.getThreshold()));
            magicMemberCouponDTO.setCouponName(magicalMemberCouponDTO.getCouponName());
            magicMemberCouponDTO.setCouponEndTime(magicalMemberCouponDTO.getCouponEndTime());
            magicMemberCouponDTO.setCouponId(magicalMemberCouponDTO.getCouponId());
            magicMemberCouponDTO.setCouponGroupId(magicalMemberCouponDTO.getCouponGroupId());
            magicMemberCouponDTO.setTspCouponId(magicalMemberCouponDTO.getTspCouponId());
            magicMemberCouponDTO.setTspCouponGroupId(magicalMemberCouponDTO.getTspCouponGroupId());
            magicMemberCouponDTO.setAssetType(magicalMemberCouponDTO.getAssetType());
            magicMemberCouponDTO.setBizToken(magicalMemberCouponDTO.getBizToken());
            magicMemberCouponDTO.setCouponNum(magicalMemberCouponDTO.getCouponAggregateNum());
            scanCouponInfo.setMagicMemberCouponDTO(magicMemberCouponDTO);
            return scanCouponInfo;
        }).collect(Collectors.toList());
        return couponList;
    }

    public static QueryCouponResultInfo mergeAndSortCouponLists(List<String> packageIds, MagicalMemberTagTextDTO magicalMemberTagTextDTO, Map<String, String> extendFieldMap, MagicBizControlResult magicBizControlResult, List<ScanCouponInfo>... lists) {
        Comparator<ScanCouponInfo> comparator = Comparator
                // 首先按couponType的priority排序
                .comparing(ScanCouponInfo::getCouponType, (t1, t2) -> {
                    CouponTypeEnum e1 = CouponTypeEnum.getByCode(t1);
                    CouponTypeEnum e2 = CouponTypeEnum.getByCode(t2);
                    if (e1 == null && e2 == null) return 0;
                    if (e1 == null) return 1;
                    if (e2 == null) return -1;
                    return Integer.compare(e2.getPriority(), e1.getPriority());
                })
                // 对于couponType为2的，先按是否可膨胀排序，可膨胀的排在前面
                .thenComparing((c1, c2) -> {
                    if (c1.getCouponType() == 2 && c2.getCouponType() == 2) {
                        int inflateCompare = Boolean.compare(
                                c2.getMagicMemberCouponDTO().getCanInflate(),
                                c1.getMagicMemberCouponDTO().getCanInflate()
                        );
                        if (inflateCompare != 0) {
                            return inflateCompare;
                        }
                        // 如果膨胀状态相同，则按mmcCategory排序，2（付费）排在前面，1（免费）排在后面
                        return Integer.compare(
                                c2.getMagicMemberCouponDTO().getMmcCategory(),
                                c1.getMagicMemberCouponDTO().getMmcCategory()
                        );
                    }
                    return 0;
                })
                // 最后按amount降序排序
                .thenComparing(c -> new BigDecimal(c.getAmount()), Comparator.reverseOrder());

        QueryCouponResultInfo queryCouponResultInfo = new QueryCouponResultInfo();
        List<ScanCouponInfo> couponList = Arrays.stream(lists)
                .flatMap(List::stream)
                .sorted(comparator)
                .collect(Collectors.toList());
        queryCouponResultInfo.setCouponList(couponList);
        Date minEndTime = couponList.stream().min(Comparator.comparing(ScanCouponInfo::getEndTime))
                .map(ScanCouponInfo::getEndTime).orElse(null);
        queryCouponResultInfo.setLatestExpireTime(minEndTime);
        queryCouponResultInfo.setMaigicCouponIdList(packageIds);
        if (magicalMemberTagTextDTO != null) {
            MagicMemberTagTextDTO magicMemberTagTextDTO = convertToMagicMemberTagTextDTO(magicalMemberTagTextDTO);
            queryCouponResultInfo.setMagicMemberTagText(magicMemberTagTextDTO);
        }
        if (magicBizControlResult != null) {
            queryCouponResultInfo.setMagicMemberPackageGray(magicBizControlResult.isPass());
        }
        if (extendFieldMap != null) {
            String experimentIdSetStr = extendFieldMap.get(PromotionPropertyEnum.COMMON_EXPERIMENT_ID_SET.getValue());
            if (StringUtils.isNotBlank(experimentIdSetStr)) {
                queryCouponResultInfo.setMagicMemberExperimentIdSet(Sets.newHashSet(JSON.parseArray(experimentIdSetStr, Integer.class)));
            }
        }
        return queryCouponResultInfo;
    }

    private static MagicMemberTagTextDTO convertToMagicMemberTagTextDTO(MagicalMemberTagTextDTO magicalMemberTagTextDTO) {
        MagicMemberTagTextDTO magicMemberTagTextDTO = new MagicMemberTagTextDTO();
        magicMemberTagTextDTO.setPriority(magicalMemberTagTextDTO.getPriority());
        magicMemberTagTextDTO.setStatus(magicalMemberTagTextDTO.getStatus());
        magicMemberTagTextDTO.setMagicalMemberCouponTag(magicalMemberTagTextDTO.getMagicalMemberCouponTag());
        magicMemberTagTextDTO.setReduceMoney(magicalMemberTagTextDTO.getReduceMoney());
        magicMemberTagTextDTO.setInflateShowText(magicalMemberTagTextDTO.getInflateShowText());
        MagicalMemberTagShowTypeEnum magicalMemberTagShowTypeEnum = MagicalMemberTagShowTypeEnum.findByValue(magicalMemberTagTextDTO.getShowType());
        magicMemberTagTextDTO.setShowType(magicalMemberTagShowTypeEnum == null ? null : magicalMemberTagShowTypeEnum.getCode());
        return magicMemberTagTextDTO;
    }

    private static String convertFenToYuan(String amountInFen) {
        if (StringUtils.isBlank(amountInFen)) {
            return BigDecimal.ZERO.toPlainString();
        }
        // 将分转换为BigDecimal，便于计算
        BigDecimal amount = new BigDecimal(amountInFen);
        // 由于1元=100分，因此除以100
        BigDecimal amountInYuan = amount.divide(new BigDecimal("100"));
        // 格式化为字符串，保留两位小数
        return amountInYuan.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }

    private static String convertDecimalToStr(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return BigDecimal.ZERO.toPlainString();
        }
        // 格式化为字符串，保留两位小数
        return bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }

    public static QueryMagicalMemberTagRequest convert2QueryMagicalMemberTagRequest(LandingCouponRequest landingCouponRequest, LandingContext context, Long mtUserId, Boolean isAgg) {
        boolean isMt = landingCouponRequest.getPlatform().equals(PlatformEnum.MT.getCode());
        QueryMagicalMemberTagRequest request = new QueryMagicalMemberTagRequest();
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserId(mtUserId);
        if (QRClientType.MT_WE_CHAT_APPLET.equals(QRClientType.getByCode(landingCouponRequest.getQrClientType()))) {
            userInfoDTO.setDeviceId(context.getWxOpenId());
        } else {
            userInfoDTO.setDeviceId(context.getUuId());
        }
        userInfoDTO.setDpId(context.getDpId());
        request.setUserInfoDTO(userInfoDTO);
        request.setNibBiz(BizEnum.GENERAL_GROUPBUY.name());
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setPoiId(landingCouponRequest.getShopId());
        request.setPoiIdDTOList(Lists.newArrayList(poiDTO));
        request.setPageSource(PageSourceEnum.poiDetailPage.getCode());
        List<Property> commonProperties = Lists.newArrayList();
        if (landingCouponRequest.getCityId() != null && landingCouponRequest.getCityId() > 0L) {
            commonProperties.add(new Property(isMt ? RulePropertyTypeEnum.browseMtCityId.getCode() : RulePropertyTypeEnum.browseDpCityId.getCode(), landingCouponRequest.getCityId().toString()));
        }
        if (landingCouponRequest.getLocationCityId() != null && landingCouponRequest.getLocationCityId() > 0L) {
            commonProperties.add(new Property(isMt ? RulePropertyTypeEnum.gpsMtCityId.getCode() : RulePropertyTypeEnum.gpsDpCityId.getCode(), landingCouponRequest.getLocationCityId().toString()));
        }
        commonProperties.add(new Property(RulePropertyTypeEnum.clientTp.getCode(), getMagicalMemberClientType(landingCouponRequest).getValue()));
        if (landingCouponRequest.getQrClientType().equals(QRClientType.MT_WE_CHAT_APPLET.getCode())) {
            commonProperties.add(new Property(RulePropertyTypeEnum.wxVersion.getCode(), landingCouponRequest.getVersion()));
            commonProperties.add(new Property(RulePropertyTypeEnum.wxOpenId.getCode(), context.getWxOpenId()));
            commonProperties.add(new Property(RulePropertyTypeEnum.wxAppId.getCode(), "wxde8ac0a21135c07d"));
        } else {
            commonProperties.add(new Property(RulePropertyTypeEnum.version.getCode(), landingCouponRequest.getVersion()));
        }
        if (QRClientType.MT_APP.equals(QRClientType.getByCode(landingCouponRequest.getQrClientType()))) {
            commonProperties.add(new Property(RulePropertyTypeEnum.position.getCode(),
                    CouponPositionConstant.COLUMN_MT_APP_POSITION));
        } else if (QRClientType.DP_APP.equals(QRClientType.getByCode(landingCouponRequest.getQrClientType()))) {
            commonProperties.add(new Property(RulePropertyTypeEnum.position.getCode(),
                    CouponPositionConstant.COLUMN_DP_APP_POSITION));
        } else if (QRClientType.MT_WE_CHAT_APPLET
                .equals(QRClientType.getByCode(landingCouponRequest.getQrClientType()))) {
            commonProperties.add(new Property(RulePropertyTypeEnum.position.getCode(),
                    CouponPositionConstant.COLUMN_MT_WX_MP_POSITION));
        }
        if (landingCouponRequest.getLng() != null && landingCouponRequest.getLat() != null) {
            commonProperties.add(new Property(RulePropertyTypeEnum.longitude.getCode(), String.valueOf(landingCouponRequest.getLng())));
            commonProperties.add(new Property(RulePropertyTypeEnum.latitude.getCode(), String.valueOf(landingCouponRequest.getLat())));
        }
        commonProperties.add(new Property(RulePropertyTypeEnum.trafficFlag.getCode(), TrafficFlagEnum.NEW_YOUHUIMA_MINI.getCode()));
        String flagMapStr = landingCouponRequest.getFlagMapStr();
        Map<String, String> flagMap = JSON.parseObject(flagMapStr, new TypeReference<Map<String, String>>() {});
        commonProperties.add(new Property(RulePropertyTypeEnum.queryInflateType.getCode(), flagMap.getOrDefault("mmcinflate", "2")));
        commonProperties.add(new Property(RulePropertyTypeEnum.queryMMCPackageType.getCode(), flagMap.getOrDefault("mmcbuy", "2")));
        commonProperties.add(new Property(RulePropertyTypeEnum.queryMMCType.getCode(), flagMap.getOrDefault("mmcuse", "2")));
        commonProperties.add(new Property(RulePropertyTypeEnum.couponAggregateStrategy.getCode(),
                String.valueOf(isAgg != null && isAgg ? AggregateStrategyEnum.MAGIC_MEMBER_COUPON_AGGREGATION.getCode()
                        : AggregateStrategyEnum.NO_AGGREGATION.getCode())));
        commonProperties.add(new Property(RulePropertyTypeEnum.magicMemberComponentVersion.getCode(), landingCouponRequest.getMagicMemberComponentVersion()));
        request.setCommonProperties(commonProperties);
        return request;
    }

    public DrawEventRequest convert2DrawEventRequest(LandingCouponRequest request, LandingContext context, String eId, OperationInfoDTO operationInfoDTO, DrawSceneEnum DrawScene, CouponPositionConstant.CouponPositionSceneEnum positionSceneEnum) {
        DrawEventRequest drawEventRequest = new DrawEventRequest();
        String ip = Optional.ofNullable(Tracer.getContext("ip")).orElse(RemoteIpGetter.FAIL_IP_RESULT);
        String env = Optional.ofNullable(Tracer.getContext("env")).orElse("mobile");
        Boolean isMt = request.getPlatform().equals(PlatformEnum.MT.getCode());
        drawEventRequest.setUserIDL(context.getUserId());
        drawEventRequest.setDpid(context.getDpId());
        drawEventRequest.setFullEid(eId);
        drawEventRequest.set_token(context.getToken());
        drawEventRequest.setIp(ip);
        drawEventRequest.setEnv(env);
        drawEventRequest.setLng(request.getLng());
        drawEventRequest.setLat(request.getLat());
        // 首页城市/浏览城市
        drawEventRequest.setCityId(request.getCityId());
        // 定位or浏览，下游也不知道
        drawEventRequest.setLocationCityId(request.getLocationCityId());
        // 定位城市
        drawEventRequest.setActualCityId(request.getLocationCityId());
        drawEventRequest.setUserAgent(Tracer.getContext("User-Agent"));
        drawEventRequest.setRequestUrl(Tracer.getContext("Referer"));
        drawEventRequest.setPlatform(UserPlatform.getByCode(request.getPlatform()));
        drawEventRequest.setCx(Tracer.getContext("cx"));
        drawEventRequest.setPoiIdL(request.getShopId());
        drawEventRequest.setCouponSendSituation(CouponSendSituationEnum.PUSH.getCode());
        // COUPON_SEND_TYPE
        drawEventRequest.setCouponSendType(1);
        drawEventRequest.setDrawScene(DrawScene.code);
        // 发券点位
        if (positionSceneEnum != null) {
            QRClientType qrClientType = QRClientType.getByCode(request.getQrClientType());
            String couponSendPositionId = "";
            switch (positionSceneEnum) {
                case PUT_BEFORE_PURCHASE:
                    if (qrClientType != null) {
                        if (QRClientType.MT_APP.equals(qrClientType)) {
                            couponSendPositionId = CouponPositionConstant.PUT_MT_APP_POSITION;
                        } else if (QRClientType.DP_APP.equals(qrClientType)) {
                            couponSendPositionId = CouponPositionConstant.PUT_DP_APP_POSITION;
                        } else if (QRClientType.MT_WE_CHAT_APPLET.equals(qrClientType)) {
                            couponSendPositionId = CouponPositionConstant.PUT_MT_WX_MP_POSITION;
                        }
                    }
                    break;
                case PUT_AFTER_PURCHASE:
                    if (qrClientType != null) {
                        if (QRClientType.MT_APP.equals(qrClientType)) {
                            couponSendPositionId = CouponPositionConstant.PUT_AFTER_PURCHASE_MT_APP_POSITION;
                        } else if (QRClientType.MT_WE_CHAT_APPLET.equals(qrClientType)) {
                            couponSendPositionId = CouponPositionConstant.PUT_AFTER_PURCHASE_MT_WX_MP_POSITION;
                        }
                    }
                    break;
            }
            if (StringUtils.isNotBlank(couponSendPositionId)) {
                drawEventRequest.setCouponSendPositionId(couponSendPositionId);
            }
        }

        RiskContext riskContext = new RiskContext();
        riskContext.setDpid(context.getDpId());
        riskContext.setUuid(context.getUuId());
        riskContext.setEnv(env);
        riskContext.setIp(ip);
        if (QRClientType.isXcx(request.getQrClientType())) {
            // 微信小程序
            riskContext.setPlatform(13);
            riskContext.setAppId(MT_WX_APP_ID);
            riskContext.setOpenId(context.getWxOpenId());
        } else {
            riskContext.setPlatform(request.getMobileOS() == MobileOSEnum.IOS.getCode() ? 5 : 4);
        }
        riskContext.setVersion(request.getVersion());
        riskContext.setUserAgent(Tracer.getContext("User-Agent"));
        riskContext.setRequestUrl(Tracer.getContext("Referer"));
        riskContext.setMtgsig(Tracer.getContext("mtgsig"));
        String fingerprint = Tracer.getContext("fingerprint");
        if (StringUtils.isNotBlank(fingerprint)) {
            fingerprint = URLDecoder.decode(Tracer.getContext("fingerprint"));
        }
        if (QRClientType.isH5(request.getQrClientType())) {
            riskContext.setH5Fingerprint(fingerprint);
        } else if (QRClientType.isApp(request.getQrClientType())) {
            riskContext.setFingerprint(fingerprint);
        } else if (QRClientType.isXcx(request.getQrClientType())) {
            riskContext.setWechatFingerprint(fingerprint);
        }

        VirtualBindUserInfoDTO userInfoDTO = userAccountAclService.loadUserByToken(context.getToken(), ip);
        if (userInfoDTO != null) {
            riskContext.setPhone(userInfoDTO.getMobile());
        }
        if (QRClientType.isApp(request.getQrClientType())) {
            if (request.getMobileOS() == MobileOSEnum.IOS.getCode()) {
                if (isMt) {
                    riskContext.setClientType(RiskClientTypeEnum.MT_IPHONE.getCode());
                } else {
                    riskContext.setClientType(RiskClientTypeEnum.DP_IPHONE.getCode());
                }
            }
            if (request.getMobileOS() == MobileOSEnum.ANDROID.getCode()) {
                if (isMt) {
                    riskContext.setClientType(RiskClientTypeEnum.MT_ANDROID.getCode());
                } else {
                    riskContext.setClientType(RiskClientTypeEnum.DP_ANDROID.getCode());
                }
            }
        } else if (QRClientType.isXcx(request.getQrClientType())) {
            // 美团微信小程序
            riskContext.setClientType(5);
        }
        riskContext.setMtgsig(Tracer.getContext("mtgsig"));
        riskContext.setActivityId(operationInfoDTO.getActivityId());
        riskContext.setActivityName(operationInfoDTO.getActivityName());
        // 1-旧版风控（默认值）；2-领塞券新版风控
        riskContext.setRiskVersion(2);
        drawEventRequest.setRiskContext(riskContext);
        if (!isMt &&
                (CouponPositionConstant.CouponPositionSceneEnum.PUT_BEFORE_PURCHASE.equals(positionSceneEnum) ||
                        CouponPositionConstant.CouponPositionSceneEnum.PUT_AFTER_PURCHASE.equals(positionSceneEnum))) {
            GodMemberContext godMemberContext = new GodMemberContext();
            godMemberContext.setMtCityId(cityAclService.dp2mt(request.getCityId()));
            godMemberContext.setMtPoiId(request.getShopId());
            godMemberContext.setMtLocationCityId(cityAclService.dp2mt(request.getLocationCityId()));
            Long mtUserId = virtualUserMapperService.dp2mt(context.getUserId());
            Long mtVirId = userMapperService.dp2mt(context.getUserId());
            godMemberContext.setMtRealUserId(mtUserId);
            godMemberContext.setMtVirtualUserId(mtVirId);
            drawEventRequest.setGodMemberContext(godMemberContext);
        }
        return drawEventRequest;
    }

    public static TransparentValidatorParam convert2TransparentValidatorParam(String serviceName, String methodName, LandingCouponRequest request, LandingContext context, CouponPositionConstant.CouponPositionSceneEnum scene) {
        String ip = Optional.ofNullable(Tracer.getContext("ip")).orElse(RemoteIpGetter.FAIL_IP_RESULT);
        TransparentValidatorParam param = TransparentValidatorParam.builder()
                .serviceName(serviceName)
                .methodName(methodName)
                .bizLine(NibBizLineEnum.GENERAL.getCode())
                .uuid(context.getUuId())
                .dpId(context.getDpId())
                .appVersion(request.getVersion())
                .ip(ip)
                .mtgsig(Tracer.getContext("mtgsig"))
                .mtPoiId(request.getShopId())
                .appId(MT_WX_APP_ID)
                .wxOpenId(context.getWxOpenId())
                .trafficSource(1).build();

        if (request.getLat() != null) {
            param.setActualLatitude((long)(request.getLat() * 1000000));
        }
        if (request.getLng() != null) {
            param.setActualLongitude((long)(request.getLng() * 1000000));
        }

        if (PlatformEnum.MT.getCode() == request.getPlatform()) {
            param.setMtRealUserId(context.getUserId());
            param.setMtHomeCityId(request.getCityId());
            param.setUserLocalCityId(request.getLocationCityId());
        } else {
            param.setDpRealUserId(context.getUserId());
            param.setDpHomeCityId(request.getCityId());
            param.setDpUserLocalCityId(request.getLocationCityId());
        }

        String fingerprint = Tracer.getContext("fingerprint");
        if (StringUtils.isNotBlank(fingerprint)) {
            fingerprint = URLDecoder.decode(Tracer.getContext("fingerprint"));
        }
        if (QRClientType.MT_APP.getCode() == request.getQrClientType()) {
            param.setPlatform(com.sankuai.nibpt.transparentvalidator.enums.PlatformEnum.MEITUAN.getCode());
            param.setMtFingerprint(fingerprint);
            if (request.getMobileOS() == MobileOSEnum.IOS.getCode()) {
                param.setCType("mtiphone");
            } else if (request.getMobileOS() == MobileOSEnum.ANDROID.getCode()) {
                param.setCType("mtandroid");
            }
            if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.QUERY)) {
                param.setCPosition(CouponPositionConstant.COLUMN_MT_APP_POSITION);
            } else if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.PUT_BEFORE_PURCHASE)) {
                param.setCPosition(CouponPositionConstant.PUT_MT_APP_POSITION);
            } else if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.PUT_AFTER_PURCHASE)) {
                param.setCPosition(CouponPositionConstant.PUT_AFTER_PURCHASE_MT_APP_POSITION);
            }
        } else if (QRClientType.DP_APP.getCode() == request.getQrClientType()) {
            param.setPlatform(com.sankuai.nibpt.transparentvalidator.enums.PlatformEnum.DIANPING.getCode());
            if (request.getMobileOS() == MobileOSEnum.IOS.getCode()) {
                param.setCType("dp_iphone");
            } else if (request.getMobileOS() == MobileOSEnum.ANDROID.getCode()) {
                param.setCType("dp_android");
            }
            if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.QUERY)) {
                param.setCPosition(CouponPositionConstant.COLUMN_DP_APP_POSITION);
            } else if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.PUT_BEFORE_PURCHASE)) {
                param.setCPosition(CouponPositionConstant.PUT_DP_APP_POSITION);
            }
        } else if (QRClientType.MT_WE_CHAT_APPLET.getCode() == request.getQrClientType()) {
            param.setPlatform(com.sankuai.nibpt.transparentvalidator.enums.PlatformEnum.MEITUAN_APPLET.getCode());
            param.setFingerApplets(fingerprint);
            param.setCType("mt_weapp");
            if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.QUERY)) {
                param.setCPosition(CouponPositionConstant.COLUMN_MT_WX_MP_POSITION);
            } else if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.PUT_BEFORE_PURCHASE)) {
                param.setCPosition(CouponPositionConstant.PUT_MT_WX_MP_POSITION);
            } else if (scene.equals(CouponPositionConstant.CouponPositionSceneEnum.PUT_AFTER_PURCHASE)) {
                param.setCPosition(CouponPositionConstant.PUT_AFTER_PURCHASE_MT_WX_MP_POSITION);
            }
        }
        return param;
    }

    private static ClientTypeEnum getMagicalMemberClientType(EnvRequest envRequest) {
        // 目前只有ios、dpIos、android、dpAndroid、mtWeChatApplet
        if (envRequest.getQrClientType().equals(QRClientType.MT_WE_CHAT_APPLET.getCode())) {
            return ClientTypeEnum.MT_WE_CHAT_APPLET;
        }
        if (envRequest.getPlatform().equals(PlatformEnum.MT.getCode())) {
            if (envRequest.getMobileOS().equals(MobileOSEnum.IOS.getCode())) {
                return ClientTypeEnum.IPHONE;
            } else {
                return ClientTypeEnum.ANDROID;
            }
        } else {
            if (envRequest.getMobileOS().equals(MobileOSEnum.IOS.getCode())) {
                return ClientTypeEnum.DP_IPHONE;
            } else {
                return ClientTypeEnum.DP_ANDROID;
            }
        }
    }

    public static List<ScanCouponInfo> preciseConvert2ScanCoupon(List<PrecisePrize> precisePrizeList) {
        if (CollectionUtils.isEmpty(precisePrizeList)) {
            return Collections.emptyList();
        }
        // 根据已领取券进行组装
        return precisePrizeList.stream().filter(Objects::nonNull).filter(PrecisePrize::isAvailable).map(precisePrize -> {
            ScanCouponInfo scanCouponInfo = new ScanCouponInfo();
            scanCouponInfo.setAmount(convertDecimalToStr(precisePrize.getAmount()));
            scanCouponInfo.setLimitAmount(convertDecimalToStr(precisePrize.getLimitAmount()));
            scanCouponInfo.setTitle(precisePrize.getCouponName());
            //scanCouponInfo.setDesc(richDrawLog.getCouponDesc());
            //scanCouponInfo.setStartTime(richDrawLog.getBeginTime());
            scanCouponInfo.setEndTime(precisePrize.getEndTime());
            scanCouponInfo.setCouponType(CouponTypeEnum.MAGICAL_MEMBER.getCode());
            MagicMemberCouponDTO magicMemberCouponDTO = new MagicMemberCouponDTO();
//            magicMemberCouponDTO.setInflatedStatus(magicalMemberCouponDTO.getInflateStatus());
//            magicMemberCouponDTO.setMaxInflateAmount(convertFenToYuan(magicalMemberCouponDTO.getMaxInflateAmount()));
            // 1-免费券，2-付费券
            magicMemberCouponDTO.setMmcCategory(1);
            magicMemberCouponDTO.setCanInflate(false);
            magicMemberCouponDTO.setCouponAmount(convertDecimalToStr(precisePrize.getAmount()));
            magicMemberCouponDTO.setThreshold(convertDecimalToStr(precisePrize.getLimitAmount()));
            magicMemberCouponDTO.setCouponName(precisePrize.getCouponName());
            magicMemberCouponDTO.setCouponEndTime(precisePrize.getEndTime().getTime());
            magicMemberCouponDTO.setCouponId(String.valueOf(precisePrize.getPrizeCodeL()));
            magicMemberCouponDTO.setCouponGroupId(precisePrize.getCouponGroupId());
//            magicMemberCouponDTO.setAssetType(magicalMemberCouponDTO.getAssetType());
//            magicMemberCouponDTO.setBizToken(magicalMemberCouponDTO.getBizToken());
            scanCouponInfo.setMagicMemberCouponDTO(magicMemberCouponDTO);
            return scanCouponInfo;
        }).collect(Collectors.toList());
    }

    public static List<ScanCouponInfo> wechatConvert2ScanCouponList(List<UserWechatCouponResponse> userWechatCouponResponseList) {
        if (CollectionUtils.isEmpty(userWechatCouponResponseList)) {
            return Collections.emptyList();
        }
        return userWechatCouponResponseList.stream().filter(Objects::nonNull).map(CouponConverter::wechatCreateScanCoupon).collect(Collectors.toList());
    }

    private static ScanCouponInfo wechatCreateScanCoupon(UserWechatCouponResponse userWechatCouponResponse) {
        ScanCouponInfo scanCouponInfo = new ScanCouponInfo();
        scanCouponInfo.setAmount(convertDecimalToStr(userWechatCouponResponse.getDiscountAmount()));
        scanCouponInfo.setLimitAmount(userWechatCouponResponse.getPriceLimit().stripTrailingZeros().toPlainString());
        scanCouponInfo.setTitle(userWechatCouponResponse.getCouponGroupName());
        scanCouponInfo.setStartTime(userWechatCouponResponse.getValidBeginTime());
        scanCouponInfo.setEndTime(userWechatCouponResponse.getValidEndTime());
        scanCouponInfo.setCouponType(CouponTypeEnum.WECOM.getCode());
        return scanCouponInfo;
    }
}
