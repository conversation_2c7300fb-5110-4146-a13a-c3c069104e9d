package com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage;

import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.UserScanRecordDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoQRCodeScanSourceType;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.model.LandingContext;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/14
 * @Description:
 */
public class UserScanRecordConverter {

    public static UserScanRecordDTO convert2UserScanRecordDTO(LandingInfoRequest request, Long dpShopId, LandingContext context) {
        UserScanRecordDTO userScanRecordDTO = new UserScanRecordDTO();
        userScanRecordDTO.setUserId(context.getUserId());
        userScanRecordDTO.setPlatform(request.getPlatform());
        userScanRecordDTO.setShopId(request.getPlatform().equals(PlatformEnum.DP.getCode()) ? dpShopId : request.getShopId());
        userScanRecordDTO.setSourceType(PromoQRCodeScanSourceType.codeType2SourceType(request.getCodeType()).code);
        if (request.getCodeType().equals(PromoCodeType.SHOP_CODE.code)) {
            userScanRecordDTO.setSourceId(request.getShopId());
        } else if (request.getCodeType().equals(PromoCodeType.STAFF_CODE.code)
                || request.getCodeType().equals(PromoCodeType.BRAND_CODE.code)) {
            // 职人码or品牌码，sourceId取sourceIdentifier
            userScanRecordDTO.setSourceId(Long.valueOf(request.getSourceIdentifier()));
        }
        userScanRecordDTO.setCodeKey(request.getCodeKey());
        return userScanRecordDTO;
    }
}
