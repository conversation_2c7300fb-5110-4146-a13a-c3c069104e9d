package com.sankuai.medicalcosmetology.offline.code.application.service.handler.emptycode;

import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.EmptyCodeBindInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.QRCodeAclService;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.exception.NotSupportSceneException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EmptyCodeBindDispatcher {

    @Autowired
    private List<EmptyCodeBindHandlerIface> ifaceList;

    @Autowired
    private QRCodeAclService qrCodeAclService;

    public Boolean dispatch(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO, Long accountId) {
        List<QRCodeConfigDTO> qrCodeConfigDTOList = emptyCodeBindInfoRequestDTO.getSecrets().stream().filter(StringUtils::isNotBlank)
                .distinct().map(s -> {
                    QRCodeConfigDTO qrCodeConfigDTO = queryQRCodeConfig(s);
                    validateQRCodeConfig(qrCodeConfigDTO);
                    return qrCodeConfigDTO;
                }).collect(Collectors.toList());
        return findIface(emptyCodeBindInfoRequestDTO).bind(qrCodeConfigDTOList, emptyCodeBindInfoRequestDTO, accountId);
    }

    private EmptyCodeBindHandlerIface findIface(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        List<EmptyCodeBindHandlerIface> hitIfaceList = ifaceList.stream()
                .filter(iface -> iface.support(emptyCodeBindInfoRequestDTO)).collect(Collectors.toList());
        validateIfaceList(hitIfaceList, emptyCodeBindInfoRequestDTO);
        return hitIfaceList.get(0);
    }

    private QRCodeConfigDTO queryQRCodeConfig(String secret) {
        List<Integer> bizTypeList = Lists.newArrayList(BizTypeEnum.OFFLINE_EMPTY_CODE.getType(),
                BizTypeEnum.H5_EMPTY_CODE.getType(), BizTypeEnum.H5_AOI_EMPTY_CODE.getType());
        return qrCodeAclService.queryQrCodeConfigBySecretKeyAndBizTypes(secret, bizTypeList);
    }

    private void validateQRCodeConfig(QRCodeConfigDTO qrCodeConfigDTO) {
        if (qrCodeConfigDTO == null) {
            throw new NotSupportSceneException("未查询到二维码配置");
        }
    }

    private void validateIfaceList(List<EmptyCodeBindHandlerIface> hitIfaceList,
            EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        if (CollectionUtils.isEmpty(hitIfaceList)) {
            throw new NotSupportSceneException("暂未支持该类型绑定空码");
        }
    }
}
