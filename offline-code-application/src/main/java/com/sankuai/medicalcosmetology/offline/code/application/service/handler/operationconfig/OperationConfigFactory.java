package com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig;

import com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.business.OperationConfigForBHandler;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.manage.OperationConfigForMHandler;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 活动配置工厂类
 */
@Component
public class OperationConfigFactory {
    /**
     * M端活动处理器
     */
    private static final Map<String, OperationConfigForMHandler> operationConfigForMHandlerMap = new ConcurrentHashMap<>();

    /**
     * B端活动处理器
     */
    private static final Map<String, OperationConfigForBHandler> operationConfigForBHandlerMap = new ConcurrentHashMap<>();

    public static void registerForM(OperationConfigForMHandler handler) {
        operationConfigForMHandlerMap.put(handler.getActivityType(), handler);
    }

    public static void registerForB(OperationConfigForBHandler handler) {
        operationConfigForBHandlerMap.put(handler.getActivityType(), handler);
    }

    public static OperationConfigForMHandler getHandlerForM(String activityType) {
        return operationConfigForMHandlerMap.get(activityType);
    }

    public static OperationConfigForBHandler getHandlerForB(String activityType) {
        return operationConfigForBHandlerMap.get(activityType);
    }
}
