package com.sankuai.medicalcosmetology.offline.code.application.enums;


import lombok.Getter;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/11
 * @Description:
 */
@Getter
public enum ExpABSceneEnum {

    LANDING_PAGE_COMMON(1, "落地页通用"),
    MAGIC_COUPON(2, "神券"),
    GROUND_PROMOTION_MAGIC_COUPON(3, "地推神券"),
    RE_PURCHASE_COUPON(4, "复购券"),
    LANDING_PAGE_WX_NATIVE(5, "落地页微信原生页面");

    private final int code;
    private final String desc;

    ExpABSceneEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ExpABSceneEnum fromCode(int code) {
        ExpABSceneEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            ExpABSceneEnum enumValue = var1[var3];
            if (enumValue.getCode() == code) {
                return enumValue;
            }
        }

        return null;
    }
}
