package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchapply;

import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.ApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityConfigDomainService;

import javax.annotation.Resource;

/**
 * B端-批量报名-参数校验
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.BATCH_APPLY_FOR_B, subScene = ProcessorSubSceneConstants.PREPARE_DATA)
public class BatchApplyPrepareDataProcessor implements BaseProcessor<BatchApplyProcessorContext> {

    @Resource
    private RebateActivityConfigDomainService rebateActivityConfigDomainService;

    @Override
    public void execute(BatchApplyProcessorContext context) {
        ApplyActivityRequest request = context.getRequest();
        String activityViewId = request.getActivityViewId();
        Long activityConfigId = ResourceEncoder.decodeResourceViewId(activityViewId);

        //查询活动信息
        RebateActivityConfigDO rebateActivityConfigDO = rebateActivityConfigDomainService.getOperationConfigById(activityConfigId);
        context.setRebateActivityConfigDO(rebateActivityConfigDO);
    }
}
