package com.sankuai.medicalcosmetology.offline.code.application.service.handler.emptycode;

import com.alibaba.fastjson.JSONObject;
import com.dianping.gmkt.event.api.promoqrcode.dto.SubCodeRelationDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.gmkt.event.api.promoqrcode.enums.PromoCodeType;
import com.dianping.gmkt.event.api.promoqrcode.enums.QrExternalSourceTypeEnum;
import com.dianping.technician.biz.dto.AuthInfoDTO;
import com.google.common.collect.Lists;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.meituan.beauty.idmapper.service.UserMapperService;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.*;
import com.sankuai.carnation.distribution.empty.code.prebind.request.EmptyCodeBindRequest;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.EmptyCodeBindInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.EmptyCodeAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PromoCodeActivityAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.TechnicianAclService;
import com.sankuai.medicalcosmetology.offline.code.application.utils.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: fuchangming
 * @Date: 2024/7/17
 * @Description:
 */
@Slf4j
@Service
public class StaffCodeEmptyBindHandler implements EmptyCodeBindHandlerIface {

    @Autowired
    private PromoCodeActivityAclService promoQRCodeAclService;

    @Autowired
    private TechnicianAclService technicianAclService;

    @Resource
    private EmptyCodeAclService emptyCodeAclService;

    @Resource
    private ShopMapperService shopMapperService;

    @Autowired
    private UserMapperService userMapperService;

    @Override
    public Boolean support(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        return emptyCodeBindInfoRequestDTO.getCodeType() != null
                && emptyCodeBindInfoRequestDTO.getCodeType().equals(PromoCodeType.STAFF_CODE.code);
    }

    @Override
    public Boolean bind(List<QRCodeConfigDTO> qrCodeConfigDTOList, EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO,
            Long accountId) {
        checkParam(emptyCodeBindInfoRequestDTO);

        resolveStaffCodeId(emptyCodeBindInfoRequestDTO);
        if (emptyCodeBindInfoRequestDTO.getStaffCodeId() == null
                || emptyCodeBindInfoRequestDTO.getStaffCodeId() <= 0L) {
            log.error("门店优惠码不存在 dpShopId={}", emptyCodeBindInfoRequestDTO.getDpShopId());
            return false;
        }
        resolveDpShopId(emptyCodeBindInfoRequestDTO);
        if (emptyCodeBindInfoRequestDTO.getDpShopId() == null || emptyCodeBindInfoRequestDTO.getDpShopId() <= 0L) {
            log.error("点评门店id不存在 dpShopId={}", emptyCodeBindInfoRequestDTO.getDpShopId());
            return false;
        }
        Long mtShopId = shopMapperService.dp2mt(emptyCodeBindInfoRequestDTO.getDpShopId());
        if (mtShopId == null) {
            log.error("门店映射失败 dpShopId={}", emptyCodeBindInfoRequestDTO.getDpShopId());
            return false;
        }
        emptyCodeBindInfoRequestDTO.setDpShopId(mtShopId);
        for (QRCodeConfigDTO qrCodeConfigDTO : qrCodeConfigDTOList) {
            if (!emptyCodeAclService
                    .bindEmptyCode(createEmptyCodeBindRequest(qrCodeConfigDTO, emptyCodeBindInfoRequestDTO))) {
                log.error("绑定空码失败 qrCodeConfigDTO={}", JSONObject.toJSONString(qrCodeConfigDTO));
                return false;
            }
        }
        log.info("绑定空码成功 qrCodeConfigDTOList={}, emptyCodeBindInfoRequestDTO={}",
                JSONObject.toJSONString(qrCodeConfigDTOList), JSONObject.toJSONString(emptyCodeBindInfoRequestDTO));
        return true;
    }

    private void resolveStaffCodeId(EmptyCodeBindInfoRequest requestDTO) {
        Long staffCodeId = requestDTO.getStaffCodeId();
        if (staffCodeId != null && staffCodeId > 0L) {
            return;
        }
        Long dpUserId = resolveDpUserId(requestDTO);
        if (dpUserId == null || dpUserId <= 0L) {
            log.info("StaffCodeEmptyBindHandler#bind, dpUserId is null, requestDTO:{}",
                    JSONObject.toJSONString(requestDTO));
            return;
        }

        staffCodeId = findTechnicianId(dpUserId, requestDTO);
        requestDTO.setStaffCodeId(staffCodeId);
    }

    private Long resolveDpUserId(EmptyCodeBindInfoRequest requestDTO) {
        if (requestDTO.getPlatform().equals(PlatformEnum.DP.getCode())) {
            return requestDTO.getUserId();
        } else {
            return userMapperService.mt2dp(requestDTO.getUserId());
        }
    }

    private Long findTechnicianId(Long dpUserId, EmptyCodeBindInfoRequest requestDTO) {
        List<AuthInfoDTO> technicians = technicianAclService.getRealTechnicianByDpUserIds(Lists.newArrayList(dpUserId));
        Long technicianId = Optional.ofNullable(technicians).orElse(new ArrayList<>()).stream().findFirst()
                .map(AuthInfoDTO::getProfessionID).map(Long::valueOf).orElse(null);

        if (technicianId == null || technicianId.equals(0L)) {
            log.info("StaffCodeEmptyBindHandler#bind, technicianId is null, requestDTO:{}, technicians:{}",
                    JSONObject.toJSONString(requestDTO), JSONObject.toJSONString(technicians));
            return 0L;
        }

        return findSubCodeId(technicianId, requestDTO);
    }

    private Long findSubCodeId(Long technicianId, EmptyCodeBindInfoRequest requestDTO) {
        Map<String, List<SubCodeRelationDTO>> subCodeRelationMap = promoQRCodeAclService.querySubCodeRelationByExternal(
                QrExternalSourceTypeEnum.TECHNICIAN.getCode(), Lists.newArrayList(String.valueOf(technicianId)));
        List<SubCodeRelationDTO> subCodeRelationDTOS = subCodeRelationMap.get(String.valueOf(technicianId));
        if (CollectionUtils.isEmpty(subCodeRelationDTOS)) {
            log.info("StaffCodeEmptyBindHandler#bind, subCodeRelationDTOS is null, requestDTO:{}, subCodeRelationDTOS:{}",
                    JSONObject.toJSONString(requestDTO), JSONObject.toJSONString(subCodeRelationDTOS));
            return 0L;
        }

        SubCodeRelationDTO chosen = subCodeRelationDTOS.stream()
                .max(Comparator.comparing(SubCodeRelationDTO::getUpdateTime)).orElse(null);
        if (chosen == null || chosen.getSubCodeId() < 0L) {
            log.info("StaffCodeEmptyBindHandler#bind, chosen is null, requestDTO:{}, subCodeRelationDTOS:{}",
                    JSONObject.toJSONString(requestDTO), JSONObject.toJSONString(chosen));
            return 0L;
        }

        return chosen.getSubCodeId();
    }

    private void resolveDpShopId(EmptyCodeBindInfoRequest requestDTO) {
        Long dpShopId = requestDTO.getDpShopId();
        if (dpShopId != null && dpShopId > 0L) {
            return;
        }
        StaffCodeDTO staffCodeDTO = promoQRCodeAclService.queryStaffCodeByCodeId(requestDTO.getStaffCodeId());
        if (staffCodeDTO == null || staffCodeDTO.getDpShopIdLong() == null) {
            log.info("StaffCodeEmptyBindHandler#bind, staffCodeDTO is null, staffCodeDTO:{}",
                    JSONObject.toJSONString(staffCodeDTO));
            return;
        }

        requestDTO.setDpShopId(staffCodeDTO.getDpShopIdLong());
    }

    private EmptyCodeBindRequest createEmptyCodeBindRequest(QRCodeConfigDTO emptyCodeInfo,
            EmptyCodeBindInfoRequest requestDTO) {
        EmptyCodeBindRequest res = new EmptyCodeBindRequest();
        res.setBizType(EmptyCodeBizTypeEnum.PROMO_CODE.getCode());
        res.setBizId(Long.valueOf(requestDTO.getCodeType()));
        res.setBindKeyType(EmptyCodeBindKeyTypeEnum.STAFF_CODE.getCode());
        res.setBindKey(requestDTO.getDpShopId() + "_" + requestDTO.getStaffCodeId());
        res.setCodeSource(EmptyCodeSourceEnum.OFFLINE_CODE.getCode());
        res.setCodeKey(emptyCodeInfo.getSecretKey());
        res.setCodeLink(emptyCodeInfo.getCodeUrl());
        res.setCodeImage(emptyCodeInfo.getImageUrl());
        res.setOperatorType(EmptyCodeOperatorTypeEnum.B_SHOP_ACCOUNT_ID.getCode());
        res.setOperatorId(String.valueOf(requestDTO.getOperatorId()));
        res.setStatus(EmptyCodeBindStatusEnum.BOUND.getCode());
        res.setBindExtString(JSONObject.toJSONString(requestDTO));
        return res;
    }

    private void checkParam(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        Long staffCodeId = emptyCodeBindInfoRequestDTO.getStaffCodeId();
        Long dpShopId = emptyCodeBindInfoRequestDTO.getDpShopId();
        // 检查职人id和门店id至少有一个缺失
        boolean staffInfoAnyMiss = ((staffCodeId == null || staffCodeId <= 0L) || (dpShopId == null || dpShopId <= 0L));

        Long userId = emptyCodeBindInfoRequestDTO.getUserId();
        Integer platform = emptyCodeBindInfoRequestDTO.getPlatform();
        // 检查用户id和平台信息至少有一个缺失
        boolean userInfoAnyMiss = ((userId == null || userId <= 0L) || (platform == null || platform <= 0L));

        // 如果职人信息缺失且用户信息缺失，则报错
        AssertUtil.isTrue(!staffInfoAnyMiss || !userInfoAnyMiss, "职人信息不合法");
    }
}
