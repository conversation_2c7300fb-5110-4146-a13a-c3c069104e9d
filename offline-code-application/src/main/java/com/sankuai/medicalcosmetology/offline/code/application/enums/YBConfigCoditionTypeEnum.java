package com.sankuai.medicalcosmetology.offline.code.application.enums;

import lombok.Getter;

@Getter
public enum YBConfigCoditionTypeEnum {
    UNKNOWN(0, "未知"),
    BLACK_LIST(1,"黑名单"),
    WHITE_LIST(2,"白名单");

    private int code;
    private String desc;

    YBConfigCoditionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static YBConfigCoditionTypeEnum fromCode(int code) {
        for (YBConfigCoditionTypeEnum showTypeEnum : YBConfigCoditionTypeEnum.values()) {
            if (showTypeEnum.getCode() == code) {
                return showTypeEnum;
            }
        }
        return UNKNOWN;
    }
}
