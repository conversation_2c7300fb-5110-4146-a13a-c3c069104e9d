package com.sankuai.medicalcosmetology.offline.code.application.constants;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/24
 * @Description:
 */
public class LionConstant {

    public static final String BRAND_AUTHORITY_CONFIG_KEY = "brandcode.authorityConfig";

    public static final String STAFF_BARGAIN_AUTHORITY_CONFIG = "staff.bargain.authority.config";

    public static final String PROMO_LANDING_URL = "promoCode.landingPage.url";

    public static final String AOI_LANDING_URL = "aoiCode.landingPage.url";

    public static final String PROMO_LANDING_MRN_SWITCH = "promoCode.landingPage.mrn.switch.config";
    public static final String PROMO_LANDING_MRN_SWITCH_MAP = "promoCode.landingPage.mrn.switch.config.map";

    public static final String PROMO_LANDING_H5_NEW_SWITCH = "promoCode.landingPage.h5.new.switch.config";

    public static final String PROMO_LANDING_WECHAT_NATIVE_SWITCH = "promoCode.landingPage.wechat.native.switch.config";

    public static final String PROMO_LANDING_INTERMEDIATE_SWITCH = "promoCode.landingPage.intermediate.switch.config";

    public static final String PROMO_LANDING_EXT_MAP = "promoCode.landingPage.ext.config.map";

    public static final String PROMO_LANDING_GOODS_CODE_JUMP_SWITCH = "promoCode.landingPage.goods.code.jump.switch.config";

    public static final String BRAND_SHOP_TYPE_AUTHORITY_CONFIG_KEY = "brandcode.shopType.authorityConfig";

    public static final String USER_SCAN_DOUBLE_WRITE = "promoCode.landingPage.user.scan.double.write";

    /**
     * 加载职人货架的配置
     */
    public static final String LOAD_STAFF_SHELF_CONFIG = "promoCode.landing.staff.shelf.load.config";

    /**
     * 扫码后返回的默认banner图片
     */
    public static final String DEFAULT_BANNER_URL = "promoCode.landing.default.banner.url";

    /**
     * 扫码领券时展示的默认banner图
     */
    public static final String DEFAULT_DRAW_BANNER_URL = "promoCode.landing.default.draw.banner.url";

    /**
     * 促评模块点评侧展示默认评价入口的门店类目白名单
     */
    public static final String DEFAULT_REVIEW_CATEGORY_WHITELIST = "default.review.category.whitelist";

    /**
     * 优惠码-权限校验海马配置开关，关闭后使用Lion进行权限校验
     */
    public static final String PROMOCODE_HAIMASWITCH = "gmkt-portal-web.promocode.haimaswitch";

    /**
     * 开店宝门店优惠码小黄条展示内容配置（新）
     */
    public static final String PROMOCODE_YELLOW_BAR_CONFIG = "promoCode.yellowBarConfig";
    /**
     * 优惠码-券配置校验bu开关
     */
    public static final String COUPON_CONFIG_CHECK_BU_SWITCH = "coupon.config.check.bu.switch";

    public static final String OPERATION_SWITCH_CONFIG = "operation.switch.config";

    /**
     * 发券活动C端开关  true:读老接口数据，false读新接口数据
     */
    public static final String OPERATION_COUPON_C_SWITCH = "operation.coupon.c.switch";

    /**
     * 斗斛ab实验前置过滤配置
     */
    public static final String EXP_AB_FORMER_FILTER_CONFIG = "exp.ab.former.filter.config";

    /**
     * 神券ab配置
     */
    public static final String MAGIC_FLAG_EXP_AB_CONFIG = "magic.flag.exp.ab.config";

    /**
     * 神券ab前置city过滤条件配置
     */
    public static final String MAGIC_FLAG_EXP_AB_CITY_FILTER_CONFIG = "magic.flag.exp.ab.city.filter.config";

    /**
     * 神券ab门店白名单
     */
    public static final String MAGIC_FLAG_EXP_AB_WHITE_CONFIG = "ab.white.list";

    /**
     * 是否查询免费神券
     */
    public static final String QUERY_FREE_MAGIC_COUPON_SWITCH = "query.free.magic.coupon.switch";

    /**
     * 是否折叠神券
     */
    public static final String FOLD_MAGICAL_COUPON_SWITCH = "fold.magical.coupon.switch";

    /**
     * 领券弹窗，是否过滤不可用免费神券
     */
    public static final String FILTER_MAGIC_COUPON = "filter.magic.coupon";

    /**
     * 是否打印领券日志
     */
    public static final String DRAW_COUPON_LOG_SWITCH = "draw.coupon.log.switch";

    /**
     * 门店白名单配置
     */
    public static final String SHOP_WHITE_CONFIG = "shop.white.config";

    public static final String REBATE_ORDER_HAS_WITHDRAW_SWITCH = "rebate.order.has.withdraw.switch";

    /**
     * 落地页图片转webp开关
     */
    public static final String LANDING_PAGE_IMG_TO_WEBP_SWITCH = "landingPage.img2webp.switch";

    /**
     * 商家返利团单信息展示开关
     */
    public static final String MERCHANT_DEAL_INFO_DOWNLOAD_SWITCH = "merchant.deal.info.download.switch";

    /**
     * 神券包券包名灰度开关
     */
    public static final String MAGIC_MEMBER_PACKAGE_NAME_GRAY_SWITCH = "magic.member.package.name.gray.switch";

    /**
     * 神券聚合灰度配置
     */
    public static final String MAGIC_MEMBER_AGGREGATE_SWITCH = "magic.member.aggregate.switch";

    /**
     * 用户维度白名单控制
     */
    public static final String USER_WHITE_CONFIG = "user.white.config";

    /**
     * 扫码订单过滤配置
     */
    public static final String SCAN_ORDER_FILTER_CONFIG = "scan.order.filter.config";

    /**
     * Apollo审批url配置
     */
    public static final String APOLLO_AUDIT_URL = "apollo.audit.url";

    /**
     * 自动报名活动定时任务配置
     */
    public static final String AUTO_APPLY_ACTIVITY_JOB_CONFIG = "auto.apply.activity.job.config";

    /**
     * 提现天数配置
     */
    public static final String WITHDRAW_DAYS_CONFIG = "withdraw.days.config";

    /**
     * 落地页活动查询后移开关配置
     */
    public static final String LANDING_PAGE_ACTIVITY_BACKWARD_CONFIG = "landingPage.activity.backward.config";

    /**
     * 活动并行处理配置
     */
    public static final String ACTIVITY_PARALLEL_PROCESS_CONFIG = "activity.parallel.process.config";

    /**
     * 分享赚模块入口配置，用于校验职人是否开启改价功能
     */
    public static final String STAFF_SHARE_REWARD_MODULE_CONFIG = "staff.module.config";

    // 性能监控相关配置

    /**
     * 性能监控慢查询阈值配置（毫秒）
     */
    public static final String PERFORMANCE_MONITOR_SLOW_THRESHOLD = "performance.monitor.slow.threshold";

    /**
     * 性能监控采样率（百分比）
     */
    public static final String PERFORMANCE_MONITOR_SAMPLING_RATE = "performance.monitor.enabled";



    /**
     * 向日葵发薪的bizLine->payer映射
     */
    public static final String REBATE_PAYER_CODE_MAP = "gmkt-event-manage-service.rebate.payer.code.map";

    /**
     * 彩虹营销平台-免佣返利活动配置-商家活动渠道
     * key为对应的渠道名称，value为对应的渠道id
     */
    public static final String REBATE_CHANNEL_CODE_MAP = "gmkt-portal-web.rebate.channel.code.map";


    /**
     * 权限拒绝提示信息配置
     */
    public static final String ACCESS_DENIED_MSG = "access-denied-msg";
}
