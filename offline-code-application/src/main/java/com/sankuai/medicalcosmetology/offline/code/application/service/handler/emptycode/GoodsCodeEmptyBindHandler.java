package com.sankuai.medicalcosmetology.offline.code.application.service.handler.emptycode;

import com.alibaba.fastjson.JSONObject;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.goodscode.ApplyGoodsCodeDTO;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.*;
import com.sankuai.carnation.distribution.empty.code.prebind.request.EmptyCodeBindRequest;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.request.EmptyCodeBindInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.EmptyCodeAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PromoCodeActivityAclService;
import com.sankuai.medicalcosmetology.offline.code.application.utils.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fuchangming
 * @Date: 2024/7/17
 * @Description:
 */
@Slf4j
@Service
public class GoodsCodeEmptyBindHandler implements EmptyCodeBindHandlerIface {

    @Autowired
    private PromoCodeActivityAclService promoQRCodeAclService;

    @Resource
    private EmptyCodeAclService emptyCodeAclService;

    @Resource
    private ShopMapperService shopMapperService;

    @Override
    public Boolean support(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        return emptyCodeBindInfoRequestDTO.getCodeType() != null
                && emptyCodeBindInfoRequestDTO.getCodeType().equals(PromoCodeType.GOODS_CODE.code);
    }

    @Override
    public Boolean bind(List<QRCodeConfigDTO> qrCodeConfigDTOList, EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO,
            Long accountId) {
        validateRequest(emptyCodeBindInfoRequestDTO);
        ApplyGoodsCodeDTO applyGoodsCodeDTO = buildApplyGoodsCodeDTO(emptyCodeBindInfoRequestDTO);
        promoQRCodeAclService.createAndGetGoodsCode(applyGoodsCodeDTO);
        if (!isPromoQRCodeExist(emptyCodeBindInfoRequestDTO.getDpShopId())) {
            log.error("门店不存在优惠码, dpShopId={}", emptyCodeBindInfoRequestDTO.getDpShopId());
            return false;
        }
        Long mtShopId = shopMapperService.dp2mt(emptyCodeBindInfoRequestDTO.getDpShopId());
        if (mtShopId == null) {
            log.error("门店映射失败, dpShopId={}", emptyCodeBindInfoRequestDTO.getDpShopId());
            return false;
        }
        emptyCodeBindInfoRequestDTO.setDpShopId(mtShopId);
        for (QRCodeConfigDTO qrCodeConfigDTO : qrCodeConfigDTOList) {
            if (!emptyCodeAclService
                    .bindEmptyCode(createEmptyCodeBindRequest(qrCodeConfigDTO, emptyCodeBindInfoRequestDTO))) {
                log.error("绑定空码失败, qrCodeConfigDTO={}", JSONObject.toJSONString(qrCodeConfigDTO));
                return false;
            }
        }
        log.info("绑定空码成功, emptyCodeBindInfoRequestDTO={}", JSONObject.toJSONString(emptyCodeBindInfoRequestDTO));
        return true;
    }

    private EmptyCodeBindRequest createEmptyCodeBindRequest(QRCodeConfigDTO emptyCodeInfo,
            EmptyCodeBindInfoRequest requestDTO) {
        EmptyCodeBindRequest res = new EmptyCodeBindRequest();
        res.setBizType(EmptyCodeBizTypeEnum.PROMO_CODE.getCode());
        res.setBizId(Long.valueOf(requestDTO.getCodeType()));
        res.setBindKeyType(EmptyCodeBindKeyTypeEnum.GOODS_CODE.getCode());
        res.setBindKey(requestDTO.getDpShopId() + "_" + requestDTO.getDpDealId());
        res.setCodeSource(EmptyCodeSourceEnum.OFFLINE_CODE.getCode());
        res.setCodeKey(emptyCodeInfo.getSecretKey());
        res.setCodeLink(emptyCodeInfo.getCodeUrl());
        res.setCodeImage(emptyCodeInfo.getImageUrl());
        res.setOperatorType(EmptyCodeOperatorTypeEnum.B_SHOP_ACCOUNT_ID.getCode());
        res.setOperatorId(String.valueOf(requestDTO.getOperatorId()));
        res.setStatus(EmptyCodeBindStatusEnum.BOUND.getCode());
        res.setBindExtString(JSONObject.toJSONString(requestDTO));
        return res;
    }

    private void validateRequest(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        AssertUtil.isTrue(
                emptyCodeBindInfoRequestDTO.getDpShopId() != null && emptyCodeBindInfoRequestDTO.getDpShopId() > 0L,
                "门店id不合法");
        AssertUtil.isTrue(
                emptyCodeBindInfoRequestDTO.getDpDealId() != null && emptyCodeBindInfoRequestDTO.getDpDealId() > 0L,
                "团单id不合法");
    }

    private ApplyGoodsCodeDTO buildApplyGoodsCodeDTO(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO) {
        ApplyGoodsCodeDTO applyGoodsCodeDTO = new ApplyGoodsCodeDTO();
        applyGoodsCodeDTO.setDpShopIdLong(emptyCodeBindInfoRequestDTO.getDpShopId());
        applyGoodsCodeDTO.setGoodsId(emptyCodeBindInfoRequestDTO.getDpDealId());
        applyGoodsCodeDTO.setGoodsType((short)1);
        return applyGoodsCodeDTO;
    }

    private boolean isPromoQRCodeExist(Long dpShopId) {
        List<PromoQRCodeDTO> promoQRCodeDTOList = promoQRCodeAclService
                .queryAndCreateShopPromoQRCodeByDpShopId(dpShopId);
        return CollectionUtils.isNotEmpty(promoQRCodeDTOList);
    }

}
