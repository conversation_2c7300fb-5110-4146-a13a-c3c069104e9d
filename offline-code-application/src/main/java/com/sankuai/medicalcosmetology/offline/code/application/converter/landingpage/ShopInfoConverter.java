package com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage;

import com.google.common.collect.Lists;
import com.sankuai.dztheme.shop.enums.ClientType;
import com.sankuai.dztheme.shop.vo.ShopCardDTO;
import com.sankuai.dztheme.shop.vo.ShopThemePlanRequest;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.LandingPageShopInfo;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingBaseRequest;
import com.sankuai.medicalcosmetology.offline.code.application.model.LandingContext;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/12
 * @Description:
 */
public class ShopInfoConverter {

    public static ShopThemePlanRequest convert2ShopThemePlanRequest(LandingBaseRequest request, Long shopId, Long userId) {
        ShopThemePlanRequest shopThemePlanRequest = new ShopThemePlanRequest();
        shopThemePlanRequest.setPlanId(request.getPlatform() == PlatformEnum.DP.getCode() ? "10500419" : "10500418");
        shopThemePlanRequest.setUserLat(request.getLat());
        shopThemePlanRequest.setUserLng(request.getLng());
        shopThemePlanRequest.setCityId(request.getCityId());
        shopThemePlanRequest.setPlatform(request.getPlatform());
        Integer clientType = request.getClientType();
        shopThemePlanRequest.setClientType(clientType);
        shopThemePlanRequest.setUserId(userId);
        shopThemePlanRequest.setNativeClient(clientType.equals(ClientType.APP.getType()));
        shopThemePlanRequest.setMiniProgram(clientType.equals(ClientType.MINI_PROGRAM.getType()));
        shopThemePlanRequest.setClientVersion(request.getVersion());
        List<Long> allShopIds = Lists.newArrayList();
        allShopIds.add(shopId);
        shopThemePlanRequest.setLongShopIds(allShopIds);
        return shopThemePlanRequest;
    }

    public static LandingPageShopInfo convert2LandingPageShopInfo(ShopCardDTO shopCardDTO) {
        LandingPageShopInfo shopInfo = new LandingPageShopInfo();
        shopInfo.setShopId(shopCardDTO.getLongShopid());
        shopInfo.setShopUuid(shopCardDTO.getShopuuid());
        shopInfo.setDefaultPic(shopCardDTO.getHeadPic());
        shopInfo.setShopName(shopCardDTO.getShopName());
        shopInfo.setRegionName(shopCardDTO.getBareaName());
        shopInfo.setCategoryName(shopCardDTO.getCategoty() != null ? shopCardDTO.getCategoty().getName() : null);
        shopInfo.setMainCategoryId(shopCardDTO.getCategoty() != null ? shopCardDTO.getCategoty().getId() : 0);
        shopInfo.setCollectShop(shopCardDTO.getCollectDTO() != null ? shopCardDTO.getCollectDTO().isCollect() : false);
        shopInfo.setShopUrl(shopCardDTO.getShopUrl());
        shopInfo.setShopCityId(shopCardDTO.getCity() != null ? shopCardDTO.getCity().getId() : 0);
        shopInfo.setDistance(shopCardDTO.getDistanceValue());
        shopInfo.setAddress(shopCardDTO.getAddress());
        shopInfo.setStarStr(shopCardDTO.getStarStr());
        if (shopCardDTO.getBusinessHourInfoc() != null) {
            shopInfo.setBusinessState(shopCardDTO.getBusinessHourInfoc().getBusinessState());
            shopInfo.setBusinessHours(shopCardDTO.getBusinessHourInfoc().getBusinessHours());
        }
        return shopInfo;
    }
}
