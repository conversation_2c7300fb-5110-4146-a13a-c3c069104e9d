package com.sankuai.medicalcosmetology.offline.code.application.enums;

import lombok.Getter;

/**
 * <AUTHOR> wb_chenyanxiang
 * @description : VoucherShopFloorSyncLog的status字段状态值枚举
 * @date : 2024/5/27
 */
@Getter
public enum VoucherShopFloorSyncLogStatusEnum {
    /**
     * 成功
     */
    SUCCESS(1, "成功"),
    /**
     * 失败
     */
    FAIL(2, "失败"),
    /**
     * 活动异常
     */
    ACTIVITY_ERROR(3, "活动异常");


    final int code;
    final String desc;

    private VoucherShopFloorSyncLogStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
