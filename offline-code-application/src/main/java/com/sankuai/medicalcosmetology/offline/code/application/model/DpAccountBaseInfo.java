package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.rotate.territory.dto.BuInfoDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 点评账号基础信息
 */
@Data
public class DpAccountBaseInfo {

    /**
     * 点评门店列表
     */
    private List<Long> dpShopIdList;

    /**
     * 点评门店信息
     */
    private Map<Long, DpPoiDTO> poiMap;

    /**
     * 门店BU信息,key:shopId, value:BU信息
     */
    private Map<Long, BuInfoDTO> shopBuMap;

    /**
     * 账号下门店的城市信息
     */
    private List<CityInfoDTO> cityInfoDTOList;

    /**
     * 门店全名
     */
    private Map<Long, String> shopFullNameMap;
}
