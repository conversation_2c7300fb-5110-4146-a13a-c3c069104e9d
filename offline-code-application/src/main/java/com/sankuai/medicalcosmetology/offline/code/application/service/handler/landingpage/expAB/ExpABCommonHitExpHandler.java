package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB;

import com.sankuai.medicalcosmetology.offline.code.application.enums.ExpABSceneEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/11
 * @Description:
 */
@Service
public class ExpABCommonHitExpHandler implements ExpABHitExpIface {

    @Override
    public boolean support(ExpABSceneEnum expABSceneEnum) {
        return expABSceneEnum.equals(ExpABSceneEnum.LANDING_PAGE_COMMON);
    }

    @Override
    public boolean hitExp(String strategyKey) {
        return StringUtils.isNotBlank(strategyKey) && (strategyKey.contains("c") || strategyKey.contains("C"));
    }
}
