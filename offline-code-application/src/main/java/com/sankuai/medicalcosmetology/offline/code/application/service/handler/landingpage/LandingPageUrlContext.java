package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.QueryExpABInfoResultDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.QueryExpABInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.enums.ExpABSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.application.service.PromoCodeForCApplicationService;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB.ExpABHitExpHandler;
import com.sankuai.medicalcosmetology.offline.code.application.utils.LandingPageSwitchFilter;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.DouHuAclService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 落地页URL上下文管理类
 * 封装配置获取、开关判断、客户端类型判断等通用逻辑
 */
@Component
public class LandingPageUrlContext {

    @Autowired
    private LandingPageSwitchFilter landingPageSwitchFilter;

    @Autowired
    private DouHuAclService douHuAclService;

    @Autowired
    private PromoCodeForCApplicationService promoCodeForCApplicationService;

    @Autowired
    private ExpABHitExpHandler expABHitExpHandler;

    /**
     * 获取Lion配置的页面URL映射
     */
    public Map<String, String> getPageUrlMap() {
        return Lion.getMap(Environment.getAppName(), LionConstant.PROMO_LANDING_URL, String.class);
    }

    /**
     * 获取开关状态信息
     */
    public SwitchInfo getSwitchInfo(PromoCodeLandingPageRequest request) {
        Long targetId = request.getCodeType() != PromoCodeType.BRAND_CODE.code 
            ? request.getPoiId() : request.getCustomerId();
            
        boolean mrnSwitch = landingPageSwitchFilter.hitLionSwitchConfig(
            LionConstant.PROMO_LANDING_MRN_SWITCH, request.getCodeType(), targetId);
            
        boolean h5NewSwitch = landingPageSwitchFilter.hitLionSwitchConfig(
            LionConstant.PROMO_LANDING_H5_NEW_SWITCH, request.getCodeType(), targetId);
            
        boolean wechatNativeSwitch = landingPageSwitchFilter.hitLionSwitchConfig(
            LionConstant.PROMO_LANDING_WECHAT_NATIVE_SWITCH, request.getCodeType(), targetId);

        // douhu实验默认true，后续实验下线无需修改代码。有lion开关可回滚
        boolean wechatNativeDouHuSwitch = true;
        String expId = douHuAclService.getExpIdForScene(ExpABSceneEnum.LANDING_PAGE_WX_NATIVE.name());
        if (StringUtils.isNotBlank(expId)) {
            QueryExpABInfoRequest expABInfoRequest = new QueryExpABInfoRequest();
            expABInfoRequest.setExpId(expId);
            expABInfoRequest.setPlatform(PlatformEnum.MT.getCode());
            QueryExpABInfoResultDTO abInfoResultDTO = promoCodeForCApplicationService.queryExpABInfo(expABInfoRequest);
            if (abInfoResultDTO == null || !expABHitExpHandler.hitExp(abInfoResultDTO.getStrategyKey(), ExpABSceneEnum.LANDING_PAGE_WX_NATIVE)) {
                wechatNativeDouHuSwitch = false;
            }
        }
        return new SwitchInfo(mrnSwitch, h5NewSwitch, wechatNativeSwitch, wechatNativeDouHuSwitch);
    }

    /**
     * 获取中间页开关状态
     */
    public boolean getIntermediateSwitch(PromoCodeLandingPageRequest request) {
        return landingPageSwitchFilter.hitLionSwitchConfig(
            LionConstant.PROMO_LANDING_INTERMEDIATE_SWITCH, request.getCodeType(), request.getPoiId());
    }

    /**
     * 获取客户端类型信息
     */
    public ClientTypeInfo getClientTypeInfo(PromoCodeLandingPageRequest request) {
        boolean isMt = QRClientType.isMt(request.getQrClientType());
        boolean isXcx = QRClientType.isXcx(request.getQrClientType());
        boolean isH5 = QRClientType.isH5(request.getQrClientType());
        
        return new ClientTypeInfo(isMt, isXcx, isH5);
    }

    /**
     * 根据客户端类型和开关状态选择标准URL
     */
    public String selectStandardUrl(Map<String, String> pageUrlMap, 
                                  ClientTypeInfo clientType, 
                                  SwitchInfo switches) {
        if (clientType.isXcx()) {
            // 小程序 - 优先检查微信原生页面
            return pageUrlMap.get(switches.isWechatNativeSwitch() && switches.isWechatNativeDouHuSwitch() ? "wx-native" :
                (switches.isH5NewSwitch() ? "mt-h5-new" : "mt"));
        } else if (clientType.isH5()) {
            // 站外H5场景
            return pageUrlMap.get(clientType.isMt() ? 
                (switches.isH5NewSwitch() ? "mt-h5-new" : "mt") : 
                (switches.isH5NewSwitch() ? "dp-h5-new" : "dp"));
        } else {
            // 站内
            return pageUrlMap.get(clientType.isMt() ? 
                (switches.isMrnSwitch() ? "mt-mrn" : "mt") : 
                (switches.isMrnSwitch() ? "dp-mrn" : "dp"));
        }
    }

    /**
     * 获取小程序前缀URL
     */
    public String getWxPrefixUrl(Map<String, String> pageUrlMap) {
        return pageUrlMap.get("wx");
    }

    /**
     * 开关状态信息
     */
    @Data
    public static class SwitchInfo {
        private final boolean mrnSwitch;
        private final boolean h5NewSwitch;
        private final boolean wechatNativeSwitch;
        private final boolean wechatNativeDouHuSwitch;
    }

    /**
     * 客户端类型信息
     */
    @Data
    public static class ClientTypeInfo {
        private final boolean isMt;
        private final boolean isXcx;
        private final boolean isH5;
    }
} 