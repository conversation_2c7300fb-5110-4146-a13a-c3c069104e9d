package com.sankuai.medicalcosmetology.offline.code.application.service.handler;

import com.alibaba.fastjson.JSONObject;
import com.dianping.gmkt.event.api.enums.ActivityStatus;
import com.dianping.gmkt.event.api.model.CouponInfo;
import com.dianping.gmkt.event.api.model.DrawActivity;
import com.dianping.gmkt.event.api.model.DrawEventConfigBean;
import com.dianping.gmkt.event.api.v2.model.PigeonResponse;
import com.dianping.gmkt.event.api.v2.model.portal.PortalEvent;
import com.dianping.gmkt.manage.common.model.EventCodeInfo;
import com.dianping.gmkt.manage.common.utils.EventCodeUtil;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.beautycontent.beautylaunchapi.model.enums.CouponStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.api.dto.common.DictDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.DeliveryRestrictionDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.ResourceDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ActivityTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.MagicMemberCouponScenarioEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationExtKeyEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.OperationConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.ResourceConfigDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.DrawEventAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PortalEventAclService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/29
 * @Description:活动信息校验
 */
@Service
public class OperationInfoCheckService {

    private static final int ACTIVITY_NAME_LENGTH_LIMIT = 12;

    private static final int ACTIVITY_MULTI_AWARD_LIMIT = 9;

    @Resource
    private PortalEventAclService portalEventAclService;

    @Resource
    private DrawEventAclService drawEventAclService;

    public static void validateBaseOperationInfo(OperationInfoDTO request) {
        Validate.isTrue(request != null, "参数异常");
        Validate.isTrue(StringUtils.isNotBlank(request.getActivityName())
                && request.getActivityName().length() <= ACTIVITY_NAME_LENGTH_LIMIT, "活动名称必填，且不允许超过12个字");
    }

    private static void validateOperationTime(OperationInfoDTO request, Date now) {
        Validate.isTrue(
                request.getStartTime() != null && request.getStartTime() > 0 && request.getEndTime() != null
                        && request.getEndTime() > now.getTime() && request.getStartTime() < request.getEndTime(),
                "活动时间非法");
    }

    public void validateOperationInfo(OperationInfoDTO request) {
        OperationConfigTypeEnum operationType = Objects
                .requireNonNull(OperationConfigTypeEnum.getByCode(request.getActivityType()));
        switch (operationType) {
            case GOD_MEMBER_RESOURCE_CONFIG:
                validateGodMemberResourceConfig(request);
                break;
            case COUPON_RESOURCE_CONFIG:
                validateCouponResourceConfig(request);
                break;
            default:
                validateDefaultOperationType(request);
                break;
        }
    }

    private void validateGodMemberResourceConfig(OperationInfoDTO request) {
        validateBaseOperationInfo(request);
        boolean checkBu = Lion.getBoolean(Environment.getAppName(), LionConstant.COUPON_CONFIG_CHECK_BU_SWITCH, true);
        if (checkBu) {
            Validate.isTrue(Arrays.stream(ActivityTypeEnum.values())
                    .anyMatch(t -> t.getCode().equals(request.getActivityType())), "活动类型不能为空");
        }
        if (request.getDeliveryRestriction() != null) {
            request.getDeliveryRestriction().setType(1);
            request.getDeliveryRestriction().setLimitCity(true);
            validateDeliveryRestriction(request.getDeliveryRestriction(), null);
        }
        Map<String, Object> extInfo = request.getExtInfo();
        if (MapUtils.isEmpty(extInfo) || !extInfo.containsKey(OperationExtKeyEnum.MAGIC_TYPE.getKey()) || extInfo.get(OperationExtKeyEnum.MAGIC_TYPE.getKey()) == null) {
            throw new IllegalArgumentException("神券接入方式不能为空");
        }
        MagicMemberCouponScenarioEnum.fromMagicFlag((String) extInfo.get(OperationExtKeyEnum.MAGIC_TYPE.getKey()));
    }

    private void validateCouponResourceConfig(OperationInfoDTO request) {
        validateBaseOperationInfo(request);
        Validate.isTrue(BizTypeEnum.getByCode(request.getBizType()) != null, "业务类型不能为空");
        Date now = new Date();
        validateOperationTime(request, now);
        boolean checkBu = Lion.getBoolean(Environment.getAppName(), LionConstant.COUPON_CONFIG_CHECK_BU_SWITCH, true);
        if (checkBu) {
            Validate.isTrue(Arrays.stream(ActivityTypeEnum.values())
                    .anyMatch(t -> t.getCode().equals(request.getActivityType())), "活动类型不能为空");
        }
        validateDeliveryRestriction(request.getDeliveryRestriction(), now);
        validateResourceConfig(request.getResourceConfig(), now);
    }

    private void validateDefaultOperationType(OperationInfoDTO request) {
        // 对于未特定校验逻辑的活动类型，当前没有特定的校验逻辑
        Validate.isTrue(OperationConfigTypeEnum.getByCode(request.getActivityType()) != null,
                "不支持的活动类型：" + request.getActivityType());
    }

    private void validateDeliveryRestriction(DeliveryRestrictionDTO restriction, Date now) {
        Validate.isTrue(restriction != null, "投放限制不能为空");
        Validate.isTrue(restriction.getType() != null, "投放限制类型不能为空");
        switch (restriction.getType()) {
            case 1:
                validateCityRestriction(restriction);
                break;
            case 2:
                validateShopWhiteListRestriction(restriction);
                break;
            default:
                throw new IllegalArgumentException("不支持的投放限制类型：" + restriction.getType());
        }
    }

    private void validateCityRestriction(DeliveryRestrictionDTO restriction) {
        validateOperationBaseRestriction(restriction);
        if (restriction.isLimitCity()) {
            Validate.isTrue(CollectionUtils.isNotEmpty(restriction.getCityDict())
                    || StringUtils.isNotBlank(restriction.getCities()), "城市信息不能为空");
        }
    }

    private void validateShopWhiteListRestriction(DeliveryRestrictionDTO restriction) {
        Validate.isTrue(restriction.getShopWhiteType() != null, "门店白名单类型不能为空");
        if (restriction.getShopWhiteType() == 1) {
            Validate.isTrue(StringUtils.isNotBlank(restriction.getShopIds()), "门店id不能为空");
        } else {
            Validate.isTrue(StringUtils.isNotBlank(restriction.getFloorIds()), "楼层id不能为空");
        }
    }

    private void validateResourceConfig(ResourceDTO resourceConfig, Date now) {
        Validate.isTrue(resourceConfig != null, "资源信息不能为空");
        validateAwardInfo(resourceConfig.getAwardInfo(), now);
    }

    private void validateAwardInfo(AwardInfoDTO awardInfo, Date now) {
        Validate.isTrue(awardInfo != null, "发券信息不能为空");
        Validate.isTrue(
                (CollectionUtils.isNotEmpty(awardInfo.getMainAwardConfig()) && awardInfo.getMainAwardConfig().stream()
                        .filter(Objects::nonNull).anyMatch(a -> StringUtils.isNotBlank(a.getCode())))
                        || (CollectionUtils.isNotEmpty(awardInfo.getMagicAwardConfig())
                                && awardInfo.getMagicAwardConfig().stream().filter(Objects::nonNull)
                                        .anyMatch(a -> StringUtils.isNotBlank(a.getCode()))),
                "发券活动配置错误：主抽奖活动和发券活动（可发神券）不能同时为空");
        Validate.isTrue(
                CollectionUtils.isEmpty(awardInfo.getMultiAwardConfig())
                        || awardInfo.getMultiAwardConfig().size() <= ACTIVITY_MULTI_AWARD_LIMIT,
                "发券活动配置错误：交叉发券活动最多只允许配置" + ACTIVITY_MULTI_AWARD_LIMIT + "个");
        validateAwardActivities(awardInfo, now);
    }

    private void validateAwardActivities(AwardInfoDTO awardInfo, Date now) {
        List<List<AwardActivityDTO>> awardConfigsList = new ArrayList<>();
        awardConfigsList.add(awardInfo.getMainAwardConfig());
        if (CollectionUtils.isNotEmpty(awardInfo.getMultiAwardConfig())) {
            awardConfigsList.addAll(awardInfo.getMultiAwardConfig());
        }
        awardConfigsList.forEach(awardVOS -> validateAwardActivity(awardVOS, now));

        // 领券活动校验
        if (CollectionUtils.isNotEmpty(awardInfo.getMagicAwardConfig())) {
            awardInfo.getMagicAwardConfig().stream()
                    .filter(Objects::nonNull)
                    .filter(award -> StringUtils.isNotBlank(award.getCode()))
                    .forEach(award -> validateAndGetDrawEvent(award.getCode(), now));
        }
    }

    private void validateDrawEventTimeSame(List<DrawEventConfigBean> drawEventConfigBeans) {
        if (drawEventConfigBeans.size() > 2) {
            throw new IllegalArgumentException("领券活动不能为空，且至多支持同时配置两个时间段的领券活动");
        }
        if (drawEventConfigBeans.size() == 2) {
            validateDrawEventTimeSame(drawEventConfigBeans.get(0).getActivity(), drawEventConfigBeans.get(1).getActivity());
        }
    }

    private void validateDrawEventTimeSame(DrawActivity drawActivity, DrawActivity drawActivity2) {
        Validate.isTrue(!isTimeOverlap(drawActivity.getStarttime(),
                        drawActivity.getEndtime(), drawActivity2.getStarttime(), drawActivity2.getEndtime()),
                "两个活动时间段有重合时间，活动id列表：" + drawActivity.getFullEid() + "," + drawActivity.getFullEid());
    }

    private void validateAwardActivity(List<AwardActivityDTO> awardVOS, Date now) {
        if (awardVOS.size() > 2) {
            throw new IllegalArgumentException("抽奖活动不能为空，且至多支持同时配置两个时间段的彩虹抽奖活动");
        }
        List<PortalEvent> events = awardVOS.stream().map(awardVO -> validateAndGetPortalEvent(awardVO.getCode(), now))
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (events.size() == 2) {
            validateEventTimeOverlap(events.get(0), events.get(1));
        }
    }

    private PortalEvent validateAndGetPortalEvent(String code, Date now) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        EventCodeInfo codeInfo = EventCodeUtil.getCodeInfo(code);
        PigeonResponse<PortalEvent> response = portalEventAclService.getEventById(codeInfo.getEid());
        Validate.isTrue(response != null, "彩虹抽奖活动id错误：" + code);
        PortalEvent event = response.getData();
        Validate.isTrue(event.getEndTime().after(now), "活动时间已结束，请重新配置，活动id：" + code);
        return event;
    }

    private DrawEventConfigBean validateAndGetDrawEvent(String code, Date date) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        DrawEventConfigBean drawEventConfigBean =  drawEventAclService.loadConfigByEid(code);
        Validate.isTrue(drawEventConfigBean != null && drawEventConfigBean.getActivity() != null, "领券活动id错误：" + code);
        if (CollectionUtils.isNotEmpty(drawEventConfigBean.getPrizeMsgDetails())) {
            String prizeDetailStr = drawEventConfigBean.getPrizeMsgDetails().get(0).getPrizedetail();
            if (StringUtils.isNotBlank(prizeDetailStr)) {
                List<CouponInfo> couponInfos = JSONObject.parseArray(prizeDetailStr, CouponInfo.class);
                Validate.isTrue(CollectionUtils.isNotEmpty(couponInfos) && couponInfos.get(0) != null
                        && couponInfos.get(0).getGodMemberCoupon() != null && couponInfos.get(0).getGodMemberCoupon(),
                        "请输入合法神券发券活动id：" + code);
            }
        }
        Validate.isTrue(drawEventConfigBean.getActivity().getEndtime().after(date), "活动时间已结束，请重新配置，活动id：" + code);
        Validate.isTrue(
                drawEventConfigBean.getActivity().getStatus() != ActivityStatus.STOP.code
                        && drawEventConfigBean.getActivity().getStatus() != ActivityStatus.ARCHIVED.code,
                "活动已下线，请重新配置，活动id：" + code);
        return drawEventConfigBean;
    }

    private void validateEventTimeOverlap(PortalEvent event1, PortalEvent event2) {
        Validate.isTrue(
                !event1.getPlatform().equals(event2.getPlatform()) || !isTimeOverlap(event1.getStartTime(),
                        event1.getEndTime(), event2.getStartTime(), event2.getEndTime()),
                "两个活动时间段有重合时间，活动id列表：" + event1.getEid() + "," + event2.getEid());
    }

    private boolean isTimeOverlap(Date start1, Date end1, Date start2, Date end2) {
        return start1.before(end2) && start2.before(end1);
    }

    private static void validateOperationBaseRestriction(DeliveryRestrictionDTO deliveryRestrictionDTO) {
        Validate.isTrue(Objects.nonNull(deliveryRestrictionDTO), "投放限制不能为空");
        Validate.isTrue(
                CollectionUtils.isNotEmpty(deliveryRestrictionDTO.getSecondCategories()) && CollectionUtils
                        .isNotEmpty(deliveryRestrictionDTO.getSecondCategories().stream().filter(Objects::nonNull)
                                .map(DictDTO::getValue).filter(StringUtils::isNotBlank).collect(Collectors.toList())),
                "类目信息不能为空");
    }

    public static void validateOperationUnique(OperationConfigDO operationConfigDTO,
            List<ResourceConfigDO> configDOList, boolean filterCurrentActivity) {
        switch (operationConfigDTO.getOperationType()) {
            case COUPON_RESOURCE_CONFIG:
            case GOD_MEMBER_RESOURCE_CONFIG:
                Map<String, ResourceConfigDO> configKeyMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(configDOList)) {
                    for (ResourceConfigDO configDO : configDOList) {
                        if (CollectionUtils.isEmpty(configDO.getLimitSecondCategorys())
                                || CollectionUtils.isEmpty(configDO.getLimitCitys())
                                || (filterCurrentActivity && configDO.getId().equals(operationConfigDTO.getId()))) {
                            continue;
                        }
                        for (Integer secondCate : configDO.getLimitSecondCategorys()) {
                            for (Integer city : configDO.getLimitCitys()) {
                                configKeyMap.put(configDO.getLimitBuLine().get(0) + "_" + secondCate + "_" + city,
                                        configDO);
                            }
                        }
                    }
                }
                for (Integer secondCate : operationConfigDTO.getLimitSecondCategorys()) {
                    for (Integer city : operationConfigDTO.getLimitCitys()) {
                        if (configKeyMap.containsKey(
                                operationConfigDTO.getLimitBuLine().get(0) + "_" + secondCate + "_" + city)) {
                            ResourceConfigDO existConfig = configKeyMap
                                    .get(operationConfigDTO.getLimitBuLine().get(0) + "_" + secondCate + "_" + city);
                            throw new IllegalArgumentException(String.format("当前活动配置与已存在的活动配置冲突，活动id:%d 活动名称:%s",
                                    existConfig.getId(), existConfig.getOperationName()));
                        }
                    }
                }
        }
    }

}
