package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.alibaba.fastjson.JSONObject;
import com.dianping.gmkt.event.api.enums.PromoCodeSourceType;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeDTO;
import com.dianping.gmkt.event.api.promoqrcode.enums.ExpressEnum;
import com.dianping.gmkt.event.api.promoqrcode.enums.QRCityTypeEnum;
import com.dianping.gmkt.manage.common.model.PageQueryPromoCodeRequest;
import com.dianping.gmkt.manage.common.model.PageResponse;
import com.dianping.lion.Environment;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.sc.express.dto.ExpressMainDTO;
import com.dianping.sc.express.dto.ExpressProgressItemDTO;
import com.dianping.sc.express.dto.LoadSubscribedExpressRequest;
import com.dianping.sc.express.enums.ExpressBizTypeEnum;
import com.dianping.sc.express.enums.ExpressSortTypeEnum;
import com.dianping.sc.express.enums.ExpressStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.api.dto.CityBasicInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.FileExportContext;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.MerchantMaterialPageQueryDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.fileload.FileLoadSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.application.adaptor.CityBasicInfoAdaptor;
import com.sankuai.medicalcosmetology.offline.code.application.model.sso.User;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DateUtil;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.BusinessDepartmentConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.BusinessDepartmentConfigService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.*;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.wrapper.S3Wrapper;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.format.Colour;
import jxl.write.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/22
 * @Description:
 */
@Service
@Slf4j
public class PromoCodeStatsExportHandler implements FileExportProcessHandlerIface<PromoQRCodeDTO, User> {

    private static final ThreadPoolExecutor executorService = Rhino
            .newThreadPool("promoCodeExport", DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(10)
                    .withBlockingQueue(new LinkedBlockingQueue<>(100)))
            .getExecutor();

    private static final int BATCH_QUERY_SIZE = 100;

    /**
     * 最大导出excel的条数
     */
    public final static int MAX_EXPORT_EXCEL_SIZE = 50000;

    private static final String SHEET_NAME = "优惠码信息表";

    /**
     * 批量导出时分页查询页大小，最大只能取100（poi批量查询shopId的最大列表大小为100）
     */
    private static final int DEFAULT_PAGE_SIZE = 80;

    @Autowired
    private S3Wrapper s3Wrapper;

    @Autowired
    private PromoCodeActivityAclService promoCodeActivityAclService;

    @Autowired
    private ExpressAclService expressAclService;

    @Autowired
    private ElephantPushAclService elephantPushAclService;

    @Autowired
    private PoiAclService poiAclService;

    @Autowired
    private CityBasicInfoAdaptor cityBasicInfoAdaptor;

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    @Override
    public boolean support(Integer sceneType) {
        return FileLoadSceneEnum.MATERIAL_PROMOCODE_EXPORT.getType().equals(sceneType);
    }

    public String executeWithResult(FileExportContext context, User param) {
        validateContextAndParam(context, param);
        MerchantMaterialPageQueryDTO req = parseRequest(context);
        validateTimeRange(req);

        PageResponse<List<PromoQRCodeDTO>> pageResult = queryPromoCodes(req);
        validatePageResult(pageResult);

        submitExportTask(context, param);
        return getResult(null, null);
    }

    private void validateContextAndParam(FileExportContext context, User param) {
        if (context == null || context.getParam() == null) {
            throw new IllegalArgumentException("参数错误");
        }
    }

    private void validateTimeRange(MerchantMaterialPageQueryDTO req) {
        if (req.getStartTime() == null || req.getEndTime() == null) {
            throw new IllegalArgumentException("请选择时间范围");
        }
    }

    private PageResponse<List<PromoQRCodeDTO>> queryPromoCodes(MerchantMaterialPageQueryDTO req) {
        PageQueryPromoCodeRequest request = transRequest(req);
        return promoCodeActivityAclService.pageQueryPromoCode(request);
    }

    private void validatePageResult(PageResponse<List<PromoQRCodeDTO>> pageResult) {
        if (pageResult == null || CollectionUtils.isEmpty(pageResult.getData())) {
            throw new RuntimeException("导出失败");
        }
        if (pageResult.getTotalCount() > MAX_EXPORT_EXCEL_SIZE) {
            throw new RuntimeException(String.format("每次最多可导出%s条，请重新选择范围", MAX_EXPORT_EXCEL_SIZE));
        }
    }

    private void submitExportTask(FileExportContext context, User param) {
        CompletableFuture.runAsync(() -> {
            List<PromoQRCodeDTO> beanList = readAsBean(context, param);
            processData(beanList, context, param);
        }, executorService).exceptionally(e -> {
            log.error(getClass().getSimpleName() + "导出优惠码信息失败", e);
            return null;
        });
    }

    /**
     * 获取处理结果
     *
     * @param beanList 处理后的数据对象列表
     * @param param 额外的参数
     * @return 处理结果的字符串表示，可能是生成的报告、统计信息或其他格式化输出
     */
    @Override
    public String getResult(List beanList, User param) {
        return "下载任务提交成功，稍后下载链接将以大象通知发出，请注意查收。";
    }

    /**
     * 获取要导出的数据
     *
     * @param param
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    @Override
    public List<PromoQRCodeDTO> readAsBean(FileExportContext context, User param) {
        MerchantMaterialPageQueryDTO req = parseRequest(context);
        PageQueryPromoCodeRequest pageQueryRequest = buildPageQueryRequest(req);
        List<PromoQRCodeDTO> resultList = new ArrayList<>();
        int currentPage = 0;
        int totalPage = 1;
        while (shouldContinueQuerying(currentPage, totalPage)) {
            PageResponse<List<PromoQRCodeDTO>> pageResult = queryPromoCodePage(pageQueryRequest, currentPage);
            if (isPageResultEmpty(pageResult)) {
                break;
            }
            totalPage = pageResult.getTotalPage() == null ? 0 : pageResult.getTotalPage();
            addToResultList(resultList, pageResult.getData());
            currentPage++;
            if (hasReachedExportLimit(currentPage)) {
                break;
            }
        }
        return resultList;
    }

    private MerchantMaterialPageQueryDTO parseRequest(FileExportContext context) {
        return JSONObject.toJavaObject(new JSONObject((Map)context.getParam()), MerchantMaterialPageQueryDTO.class);
    }

    private PageQueryPromoCodeRequest buildPageQueryRequest(MerchantMaterialPageQueryDTO req) {
        PageQueryPromoCodeRequest pageQueryRequest = transRequest(req);
        pageQueryRequest.setDpShopIdsLong(parseDpShopIds(req.getDpShopIds()));
        pageQueryRequest.setPageSize(DEFAULT_PAGE_SIZE);
        return pageQueryRequest;
    }

    private List<Long> parseDpShopIds(String dpShopIds) {
        if (StringUtils.isBlank(dpShopIds)) {
            return new ArrayList<>();
        }
        return Arrays.stream(dpShopIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
    }

    private boolean shouldContinueQuerying(Integer currentPage, Integer totalPage) {
        return currentPage + 1 <= totalPage;
    }

    public PageResponse<List<PromoQRCodeDTO>> queryPromoCodePage(PageQueryPromoCodeRequest request, int currentPage) {
        request.setCurrentPage(currentPage);
        int MAX_RETRIES = 3;
        long RETRY_DELAY_MS = 1000;
        for (int retry = 0; retry < MAX_RETRIES; retry++) {
            try {
                return promoCodeActivityAclService.pageQueryPromoCode(request);
            } catch (Exception e) {
                if (retry == MAX_RETRIES - 1) {
                    log.error("查询优惠码页面失败，已重试{}次", MAX_RETRIES, e);
                    throw e;
                }
                log.warn("查询优惠码页面失败，正在进行第{}次重试", retry + 1, e);
                try {
                    Thread.sleep(RETRY_DELAY_MS);
                } catch (InterruptedException ie) {
                    log.error(getClass().getSimpleName() + " is interrupted while waiting for retry", ie);
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程被中断", ie);
                }
            }
        }
        return null;
    }

    private boolean isPageResultEmpty(PageResponse<List<PromoQRCodeDTO>> pageResult) {
        return pageResult == null || CollectionUtils.isEmpty(pageResult.getData());
    }

    private void addToResultList(List<PromoQRCodeDTO> resultList, List<PromoQRCodeDTO> pageData) {
        if (CollectionUtils.isNotEmpty(pageData)) {
            resultList.addAll(pageData);
        }
    }

    private boolean hasReachedExportLimit(int currentPage) {
        return currentPage * DEFAULT_PAGE_SIZE >= MAX_EXPORT_EXCEL_SIZE;
    }

    private PageQueryPromoCodeRequest transRequest(MerchantMaterialPageQueryDTO req) {
        PageQueryPromoCodeRequest pageQueryRequest = getPageQueryPromoCodeRequest(req);
        if (StringUtils.isNotBlank(req.getDpShopIds())) {
            if (req.getDpShopIds().contains(" ") || req.getDpShopIds().contains("，")) {
                throw new IllegalArgumentException("dpShopId不能包含中文逗号、空格");
            }
            String[] split = StringUtils.split(req.getDpShopIds(), ",");
            extractDpShopIds(pageQueryRequest, split, BATCH_QUERY_SIZE);
        }
        return pageQueryRequest;
    }

    private static PageQueryPromoCodeRequest getPageQueryPromoCodeRequest(MerchantMaterialPageQueryDTO req) {
        PageQueryPromoCodeRequest pageQueryRequest = new PageQueryPromoCodeRequest();
        pageQueryRequest.setStartTime(req.getStartTime());
        pageQueryRequest.setEndTime(req.getEndTime());
        pageQueryRequest.setApplyTimesType(req.getApplyTimesType());
        pageQueryRequest.setCurrentPage(0);
        pageQueryRequest.setPageSize(1);
        pageQueryRequest.setFirstCategory(req.getFirstCategory());
        pageQueryRequest.setSecondCategory(req.getSecondCategory());
        pageQueryRequest.setCityType(req.getCityType());
        pageQueryRequest.setBusinessDepIds(req.getBusinessDepIds());
        return pageQueryRequest;
    }

    public static void extractDpShopIds(PageQueryPromoCodeRequest pageQueryRequest, String[] split,
            Integer batchQuerySize) {
        if (split.length > batchQuerySize) {
            throw new IllegalArgumentException("dpShopId数不能超过" + batchQuerySize);
        }
        ArrayList<Long> dpShopIds = new ArrayList<>();
        for (String dpShopIdStr : split) {
            dpShopIds.add(Long.valueOf(dpShopIdStr));
        }
        pageQueryRequest.setDpShopIdsLong(dpShopIds);
    }

    /**
     * 处理解析后的数据对象列表
     *
     * @param modelList 解析后的数据对象列表，每个对象代表一个文件的内容
     * @param param 额外的参数
     */
    @Override
    public void processData(List<PromoQRCodeDTO> modelList, FileExportContext req, User param) {
        downloadPromoQRCodeList(req, modelList, param.getLogin());
    }

    public void downloadPromoQRCodeList(FileExportContext request, List<PromoQRCodeDTO> promoQRCodeDTOList,
            String currentUser) {
        MerchantMaterialPageQueryDTO query = parseQuery(request);
        String fileName = getExcelS3FileName(query.getStartTime(), query.getEndTime());

        try {
            File file = createExcelFile(fileName, promoQRCodeDTOList);
            boolean uploadResult = uploadFileToS3(file, fileName);
            sendNotification(uploadResult, fileName, currentUser);
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".downloadPromoQRCodeList error", e);
            handleException(e, request, currentUser);
        } finally {
            cleanupTempFile(fileName);
        }
    }

    private MerchantMaterialPageQueryDTO parseQuery(FileExportContext request) {
        return JSONObject.toJavaObject(new JSONObject((Map)request.getParam()), MerchantMaterialPageQueryDTO.class);
    }

    private File createExcelFile(String fileName, List<PromoQRCodeDTO> promoQRCodeDTOList) throws Exception {
        File file = new File(buildFilePath(fileName));
        try (FileOutputStream fos = new FileOutputStream(file)) {
            WritableWorkbook workbook = createWorkbook(fos);
            try {
                WritableSheet sheet = workbook.createSheet(SHEET_NAME, 0);
                initPromoCodeSheet(sheet);
                fillSheetData(sheet, promoQRCodeDTOList);
                workbook.write();
            } finally {
                workbook.close();
            }
        }
        return file;
    }

    private static String buildFilePath(String fileName) {
        // 获取系统的临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        return tempDir + File.separator + fileName;
    }

    private WritableWorkbook createWorkbook(FileOutputStream fos) throws IOException {
        WorkbookSettings workbookSettings = new WorkbookSettings();
        workbookSettings.setEncoding("utf-8");
        return Workbook.createWorkbook(fos, workbookSettings);
    }

    public void fillSheetData(WritableSheet sheet, List<PromoQRCodeDTO> promoQRCodeDTOList) throws Exception {
        if (CollectionUtils.isNotEmpty(promoQRCodeDTOList)) {
            List<Long> dpShopIds = getDpShopIds(promoQRCodeDTOList);
            Map<Long, Long> dpShopIdAndMtShopIdMap = poiAclService.batchDp2Mt(dpShopIds);
            List<CityBasicInfoDTO> cityBasicInfoDTOS = cityBasicInfoAdaptor.getAllDpTopLevelCity();
            Map<Integer, CityBasicInfoDTO> cityBasicInfoDTOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cityBasicInfoDTOS)) {
                cityBasicInfoDTOMap = cityBasicInfoDTOS.stream()
                        .collect(Collectors.toMap(CityBasicInfoDTO::getCityId, Function.identity()));
            }
            for (int i = 0; i < promoQRCodeDTOList.size(); i++) {
                fillRowsForSheet(sheet, cityBasicInfoDTOMap, Collections.singletonList(promoQRCodeDTOList.get(i)),
                        i + 1, dpShopIdAndMtShopIdMap);
            }
        }
    }

    private boolean uploadFileToS3(File file, String fileName) {
        return s3Wrapper.uploadFile(fileName, file.getAbsolutePath());
    }

    private void sendNotification(boolean uploadResult, String fileName, String currentUser) {
        String message = buildNotificationMessage(uploadResult, fileName);
        List<String> recipients = Arrays.asList("chenhaoyang02", "lipengyu04", "fei.pan", currentUser);
        elephantPushAclService.pushText(message, recipients);
    }

    private String buildNotificationMessage(boolean uploadResult, String fileName) {
        StringBuilder sb = new StringBuilder();
        sb.append("【").append(Environment.getEnvironment()).append("环境").append("】");
        if (uploadResult) {
            sb.append("  优惠码Excel文件导出成功，下载链接：").append(s3Wrapper.getDownloadUrl(fileName));
        } else {
            sb.append("  优惠码Excel文件导出失败，请联系研发产品及研发同学! ");
        }
        return sb.toString();
    }

    private void handleException(Exception e, FileExportContext request, String currentUser) {
        log.error(getClass().getSimpleName() + ".downloadPromoQRCodeList Exception, request:{}",
                JSONObject.toJSONString(request), e);
        elephantPushAclService.pushText("系统异常！优惠码Excel文件导出失败，请联系研发产品及研发同学! ",
                Arrays.asList("chenhaoyang02", "lipengyu04", "fei.pan", currentUser));
    }

    private void cleanupTempFile(String fileName) {
        File deleteFile = new File(buildFilePath(fileName));
        if (deleteFile.exists()) {
            deleteFile.delete();
        }
    }

    /**
     * 填充excel行
     *
     * @param sheet
     * @param dataList
     * @param startIndex
     * @throws Exception
     */
    public void fillRowsForSheet(WritableSheet sheet, Map<Integer, CityBasicInfoDTO> cityBasicInfoDTOMap,
            List<PromoQRCodeDTO> dataList, int startIndex, Map<Long, Long> dpShopIdAndMtShopIdMap) throws Exception {
        int rowIndex = startIndex;
        WritableCellFormat cellFormat = createCellFormat();
        if (MapUtils.isEmpty(dpShopIdAndMtShopIdMap)) {
            log.warn(getClass().getSimpleName() + ".fillRowsForSheet,dpShopIdAndMtShopIdMap is empty");
        }
        for (PromoQRCodeDTO item : dataList) {
            fillRow(sheet, cityBasicInfoDTOMap, item, rowIndex, dpShopIdAndMtShopIdMap, cellFormat);
            rowIndex++;
            // 流控city，category服务的流量
            Thread.sleep(20);
        }
    }

    private WritableCellFormat createCellFormat() throws WriteException {
        WritableFont font = new WritableFont(WritableFont.TIMES, 10, WritableFont.NO_BOLD);
        return new WritableCellFormat(font);
    }

    private void fillRow(WritableSheet sheet, Map<Integer, CityBasicInfoDTO> cityBasicInfoDTOMap, PromoQRCodeDTO item,
            int rowIndex, Map<Long, Long> dpShopIdAndMtShopIdMap, WritableCellFormat cellFormat) throws Exception {
        List<Label> labels = new ArrayList<>();
        labels.add(new Label(0, rowIndex, String.valueOf(item.getPromoQRCodeId()), cellFormat));
        labels.add(new Label(1, rowIndex, item.getShopName(), cellFormat));
        labels.add(new Label(2, rowIndex, String.valueOf(item.getShopIdLong()), cellFormat));
        labels.add(new Label(3, rowIndex, formatDate(item.getCreateTime()), cellFormat));
        labels.add(new Label(4, rowIndex, item.getQrCodeUrl(), cellFormat));
        labels.add(new Label(5, rowIndex, item.getQrCodeImageUrl(), cellFormat));
        labels.add(new Label(6, rowIndex, getShowSourceType(item.getSourceType()), cellFormat));
        labels.add(new Label(7, rowIndex, defaultString(item.getFirstTag()), cellFormat));
        labels.add(new Label(8, rowIndex, defaultString(item.getSecondTag()), cellFormat));
        labels.add(new Label(9, rowIndex, defaultString(item.getApplyName()), cellFormat));
        labels.add(new Label(10, rowIndex, defaultString(item.getApplyMobileNo()), cellFormat));
        labels.add(new Label(11, rowIndex, defaultString(item.getRegion()), cellFormat));
        labels.add(new Label(12, rowIndex, defaultString(item.getAddress()), cellFormat));
        execQueryExpress(item);
        labels.add(new Label(13, rowIndex, defaultString(item.getExpressNo()), cellFormat));
        labels.add(new Label(14, rowIndex, defaultString(item.getExpressStatus()), cellFormat));
        labels.add(new Label(15, rowIndex, defaultString(item.getExpressContext()), cellFormat));
        fillCategoryAndBusinessInfo(cityBasicInfoDTOMap, item, rowIndex, dpShopIdAndMtShopIdMap, cellFormat, labels);
        for (Label label : labels) {
            sheet.addCell(label);
        }
    }

    private void fillCategoryAndBusinessInfo(Map<Integer, CityBasicInfoDTO> cityBasicInfoDTOMap, PromoQRCodeDTO item,
            int rowIndex, Map<Long, Long> dpShopIdAndMtShopIdMap, WritableCellFormat cellFormat, List<Label> labels)
            throws Exception {
        Map<Integer, DpPoiBackCategoryDTO> categoryMap = poiAclService
                .getFirstAndSecondCategory(item.getShopIdLong());
        labels.add(new Label(16, rowIndex, getCategoryName(categoryMap, CategoryAclService.FIRST_LEVEL), cellFormat));
        labels.add(new Label(17, rowIndex, getCategoryName(categoryMap, CategoryAclService.SECOND_LEVEL), cellFormat));
        String businessDepName = StringUtils.EMPTY;
        if (item.getBusinessDepId() != null) {
            BusinessDepartmentConfigDO businessDep = BusinessDepartmentConfigService.getByCode(item.getBusinessDepId());
            if (businessDep != null) {
                businessDepName = businessDep.getName();
            }
        }
        labels.add(new Label(18, rowIndex, businessDepName,
                cellFormat));
        Long mtShopId = dpShopIdAndMtShopIdMap.getOrDefault(item.getShopIdLong(), null);
        labels.add(
                new Label(19, rowIndex, mtShopId == null ? StringUtils.EMPTY : String.valueOf(mtShopId), cellFormat));
        String cityName = StringUtils.EMPTY;
        if (MapUtils.isNotEmpty(cityBasicInfoDTOMap)) {
            CityBasicInfoDTO cityBasicInfoDTO = cityBasicInfoDTOMap.get(item.getCityId());
            if (cityBasicInfoDTO != null) {
                cityName = cityBasicInfoDTO.getCityName();
            }
        }
        labels.add(new Label(20, rowIndex, cityName, cellFormat));
        if (item.getCityType() != null) {
            labels.add(new Label(21, rowIndex, QRCityTypeEnum.getByCode(item.getCityType()) == null ? ""
                    : QRCityTypeEnum.getByCode(item.getCityType()).desc, cellFormat));
        }
        labels.add(new Label(22, rowIndex, formatDate(item.getUpdateTime()), cellFormat));
        labels.add(new Label(23, rowIndex, defaultString(String.valueOf(item.getApplyTimes())), cellFormat));
        labels.add(new Label(24, rowIndex, defaultString(item.getApplyReason()), cellFormat));
    }

    private String formatDate(Date date) {
        return date == null ? "" : DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
    }

    private String defaultString(String str) {
        return StringUtils.defaultString(str);
    }

    private String getCategoryName(Map<Integer, DpPoiBackCategoryDTO> categoryMap, int level) {
        DpPoiBackCategoryDTO category = categoryMap.get(level);
        return category == null || StringUtils.isBlank(category.getCategoryName()) ? StringUtils.EMPTY
                : category.getCategoryName();
    }

    /**
     * 查询快递信息
     *
     * @param promoQRCodeDTO
     */
    private void execQueryExpress(PromoQRCodeDTO promoQRCodeDTO) {
        // 判断是否超过30天
        if (isOverDuration(promoQRCodeDTO.getCreateTime(), 30)) {
            setExpressInfoOverDuration(promoQRCodeDTO);
            return;
        }

        // 判断快递单号是否为空
        if (isExpressInfoEmpty(promoQRCodeDTO)) {
            setExpressInfoEmpty(promoQRCodeDTO);
        } else {
            // 查询快递信息
            queryAndSetExpressInfo(promoQRCodeDTO);
        }
    }

    private boolean isOverDuration(Date createTime, int days) {
        if (createTime == null) {
            return false;
        }
        long durationOfDay = (System.currentTimeMillis() - createTime.getTime()) / TimeUnit.DAYS.toMillis(1);
        return durationOfDay > days;
    }

    private void setExpressInfoOverDuration(PromoQRCodeDTO promoQRCodeDTO) {
        promoQRCodeDTO.setExpressStatus("无信息");
        promoQRCodeDTO.setExpressContext("超过30天不查快递信息");
    }

    private boolean isExpressInfoEmpty(PromoQRCodeDTO promoQRCodeDTO) {
        return StringUtils.isBlank(promoQRCodeDTO.getExpressNo()) || StringUtils.isBlank(promoQRCodeDTO.getExpress());
    }

    private void setExpressInfoEmpty(PromoQRCodeDTO promoQRCodeDTO) {
        promoQRCodeDTO.setExpressStatus("无信息");
        promoQRCodeDTO.setExpressContext("暂无物流信息");
    }

    private void queryAndSetExpressInfo(PromoQRCodeDTO promoQRCodeDTO) {
        LoadSubscribedExpressRequest request = buildExpressRequest(promoQRCodeDTO);
        ExpressMainDTO expressMainDTO = queryExpressInfo(request);
        if (expressMainDTO == null) {
            promoQRCodeDTO.setExpressContext("暂无物流信息");
        } else {
            setExpressInfo(promoQRCodeDTO, expressMainDTO);
        }
    }

    private LoadSubscribedExpressRequest buildExpressRequest(PromoQRCodeDTO promoQRCodeDTO) {
        LoadSubscribedExpressRequest request = new LoadSubscribedExpressRequest();
        request.setBizType(ExpressBizTypeEnum.PROMO_QR_CODE.getType());
        request.setBizId(String.valueOf(promoQRCodeDTO.getPromoQRCodeId()));
        request.setCompanyCode(promoQRCodeDTO.getExpress());
        request.setExpressNo(promoQRCodeDTO.getExpressNo());
        request.setSortType(ExpressSortTypeEnum.DESC_TYPE.getType());
        return request;
    }

    private ExpressMainDTO queryExpressInfo(LoadSubscribedExpressRequest request) {
        try {
            return expressAclService.queryExpressInfo(request.getBizId(), request.getExpressNo(),
                    request.getCompanyCode());
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".execQueryExpress. 获取物流信息失败 BizId:[{}],", request.getBizId(), e);
            return null;
        }
    }

    private void setExpressInfo(PromoQRCodeDTO promoQRCodeDTO, ExpressMainDTO expressMainDTO) {
        promoQRCodeDTO.setExpressStatus(ExpressStatusEnum.getEnumByStatus(expressMainDTO.getStatus()).getDesc());
        List<ExpressProgressItemDTO> items = expressMainDTO.getExpressProgressItemDTOList();
        if (CollectionUtils.isEmpty(items)) {
            promoQRCodeDTO.setExpressContext("暂无物流信息");
        } else {
            promoQRCodeDTO.setExpressContext(items.get(0).getContext());
        }
        promoQRCodeDTO.setExpress(ExpressEnum.getByCode(promoQRCodeDTO.getExpress()).name);
    }

    /**
     * 获取点评侧shopId列表
     *
     * @param promoQRCodeDTOList
     * @return
     */
    private List<Long> getDpShopIds(List<PromoQRCodeDTO> promoQRCodeDTOList) {
        List<Long> shopIdList = new ArrayList<>(promoQRCodeDTOList.size());
        for (PromoQRCodeDTO item : promoQRCodeDTOList) {
            shopIdList.add(item.getShopIdLong());
        }
        return shopIdList;
    }

    private String getShowSourceType(Integer sourceType) {
        PromoCodeSourceType promoCodeSourceType = PromoCodeSourceType.getByCode(sourceType);
        if (promoCodeSourceType == null) {
            return "";
        }
        return promoCodeSourceType.getDesc();
    }

    /**
     * 初始化表格列
     *
     * @param sheet
     * @throws Exception
     */
    private void initPromoCodeSheet(WritableSheet sheet) throws Exception {
        // 列名
        String[] metrics = new String[] {"PromoQRCodeId", "门店名", "点评shopId", "申请时间", "源码链接", "码链接", "申请渠道", "专项标签一",
            "专项标签二", "邮寄姓名", "邮寄电话", "所在地区", "详细地址", "快递单号", "快递状态", "快递详情", "一级类目", "二级类目", "业务部", "美团shopId", "城市",
            "城市类型", "更新时间", "申请次数", "二次申请说明"}; // 新增字段

        // 选择字体
        WritableFont color = new WritableFont(WritableFont.TIMES, 10, WritableFont.BOLD);
        // 设置字体颜色为黑色
        color.setColour(Colour.BLACK);
        WritableCellFormat colorFormat = new WritableCellFormat(color);
        // 初始化列
        for (int i = 0; i < metrics.length; i++) {
            Label temp = new Label(i, 0, metrics[i], colorFormat);
            sheet.addCell(temp);
        }
    }

    /**
     * @return
     */
    private String getExcelS3FileName(Long startTime, Long endTime) {
        return new StringBuilder().append(startTime).append("_").append(endTime).append("_")
                .append(System.currentTimeMillis()).append(".xls").toString();
    }
}
