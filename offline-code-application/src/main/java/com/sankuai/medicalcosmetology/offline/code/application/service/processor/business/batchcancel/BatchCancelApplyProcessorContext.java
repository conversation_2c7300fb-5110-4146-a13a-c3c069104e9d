package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchcancel;

import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.CancelApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.ProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import lombok.Data;

import java.util.List;

/**
 * 批量取消报名处理器上下文
 */
@Data
public class BatchCancelApplyProcessorContext implements ProcessorContext {

    /**
     * 取消报名请求
     */
    private CancelApplyActivityRequest request;

    /**
     * 点评账号id
     */
    private Long accountId;

    /**
     * 返利活动记录列表
     */
    private List<RebateActivityRecordDO> rebateActivityRecordDOList;
}
