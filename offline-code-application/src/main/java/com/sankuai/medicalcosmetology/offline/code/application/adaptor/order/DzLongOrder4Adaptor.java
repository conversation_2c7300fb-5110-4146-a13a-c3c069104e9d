package com.sankuai.medicalcosmetology.offline.code.application.adaptor.order;

import com.dianping.pay.order.domain.beans.NibOrderGetRequest;
import com.dianping.pay.order.service.query.GetNibOrderProxyService;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 功能描述: 到综长订单，订单号以'4'开始
 *
 * <AUTHOR>
 * @date 2022/05/24
 **/
@Service
public class DzLongOrder4Adaptor extends AbsUnifiedOrderAdaptor {

    @Resource
    private GetNibOrderProxyService getOrderService;

    @Override
    protected UnifiedOrderWithId getUnifiedOrder(String orderId) {
        long longOrderId = NumberUtils.toLong(orderId, 0);
        if (longOrderId <= 0L) {
            return null;
        }
        NibOrderGetRequest request = new NibOrderGetRequest();
        request.setOrderId(longOrderId);
        UnifiedOrderWithId unifiedOrder = getOrderService.getOrder(request);
        if (unifiedOrder == null || (CollectionUtils.isEmpty(unifiedOrder.getSkus()) && unifiedOrder.getPaySuccessTime() == null)) {
            return null;
        }
        unifiedOrder.setLongOrderId(longOrderId);
        return unifiedOrder;
    }

    @Override
    protected int getSupportOrderType() {
        return DistributionOrderTypeEnum.DZ_LONG_ORDER_START_WITH_CHAR_4.getCode();
    }

    @Override
    protected long analyseProductId(UnifiedOrderWithId unifiedOrder) {
        return NumberUtils.toLong(unifiedOrder.getFirstSku().getSpugId());
    }
}
