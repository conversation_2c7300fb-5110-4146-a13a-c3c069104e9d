package com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.business;

import com.google.common.collect.Lists;
import com.meituan.mtrace.Tracer;
import com.sankuai.medicalcosmetology.offline.code.api.enums.OperationStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.ApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.CancelApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ActivityModuleResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopCheckResultResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateActivityDetailResponse;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.ProcessorExecutor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchapply.BatchApplyProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchcancel.BatchCancelApplyProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.queryapplyshop.QueryApplyShopProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querydetail.QueryDetailProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querylist.QueryActivityListProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GodCouponRebateActivityConfigForBHandler extends AbsOperationConfigForBHandler {

    @Resource
    private ProcessorExecutor processorExecutor;

    @Override
    public String getActivityType() {
        return OperationConfigTypeEnum.GOD_COUPON_REBATE_ACTIVITY_CONFIG.name();
    }

    @Override
    public List<Long> batchApplyActivity(ApplyActivityRequest request) {
        BatchApplyProcessorContext context = new BatchApplyProcessorContext();
        context.setRequest(request);

        //参数校验
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.BATCH_APPLY_FOR_B, ProcessorSubSceneConstants.PARAM_CHECK);
        //数据准备
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.BATCH_APPLY_FOR_B, ProcessorSubSceneConstants.PREPARE_DATA);
        //业务校验
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.BATCH_APPLY_FOR_B, ProcessorSubSceneConstants.BIZ_CHECK);
        //创建报名记录
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.BATCH_APPLY_FOR_B, ProcessorSubSceneConstants.CREATE_RECORD);

        return Lists.newArrayList(context.getApplySuccessShopIdList());
    }

    @Override
    public void batchCancelApply(CancelApplyActivityRequest request) {
        String accountIdStr = Tracer.getContext("accountId");
        Long accountId = Long.valueOf(accountIdStr);
        BatchCancelApplyProcessorContext context = new BatchCancelApplyProcessorContext();
        context.setRequest(request);
        context.setAccountId(accountId);
        //参数校验
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.BATCH_CANCEL_APPLY_FOR_B, ProcessorSubSceneConstants.PARAM_CHECK);
        //数据准备
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.BATCH_CANCEL_APPLY_FOR_B, ProcessorSubSceneConstants.PREPARE_DATA);
        //解绑活动
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.BATCH_CANCEL_APPLY_FOR_B, ProcessorSubSceneConstants.UNBIND_ACTIVITY);
    }

    @Override
    public List<ActivityModuleResponse> queryActivityList(Long dpAccountId, Long dpShopId, int activityType, int status) {
        QueryActivityListProcessorContext context = new QueryActivityListProcessorContext();
        context.setActivityType(activityType);
        context.setDpAccountId(dpAccountId);
        context.setDpShopId(dpShopId);
        context.setStatus(status);
        //参数校验
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, ProcessorSubSceneConstants.PARAM_CHECK);

        //数据准备
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, ProcessorSubSceneConstants.PREPARE_DATA);

        //报名门店查询
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, ProcessorSubSceneConstants.QUERY_APPLY_SHOP);

        //组装活动进度信息
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, ProcessorSubSceneConstants.ASSEMBLE_ACTIVITY_PROCESS);

        //组装店铺状态
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, ProcessorSubSceneConstants.ASSEMBLE_SHOP_STATUS);

        return context.getActivityModuleList();
    }

    @Override
    public ApplyShopInfoResponse queryApplyShopInfo(String activityViewId, Long dpAccountId) {
        QueryApplyShopProcessorContext context = new QueryApplyShopProcessorContext();
        context.setActivityViewId(activityViewId);
        context.setDpAccountId(dpAccountId);

        //参数校验
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_APPLY_SHOP_FOR_B, ProcessorSubSceneConstants.PARAM_CHECK);

        //数据准备
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_APPLY_SHOP_FOR_B, ProcessorSubSceneConstants.PREPARE_DATA);

        //查询报名门店
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_APPLY_SHOP_FOR_B, ProcessorSubSceneConstants.QUERY_APPLY_SHOP);

        return context.getApplyShopInfoResponse();
    }

    @Override
    public ApplyShopCheckResultResponse checkApplyShop(ApplyActivityRequest applyActivityRequest) {
        return null;
    }

    @Override
    public RebateActivityDetailResponse queryActivityDetail(String activityViewId) {

        String accountIdStr = Tracer.getContext("accountId");
        Long accountId = Long.valueOf(accountIdStr);

        QueryDetailProcessorContext context = new QueryDetailProcessorContext();
        context.setAccountId(accountId);
        context.setActivityViewId(activityViewId);

        //参数校验
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, ProcessorSubSceneConstants.PARAM_CHECK);

        //数据准备
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, ProcessorSubSceneConstants.PREPARE_DATA);

        //店铺报名信息查询
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, ProcessorSubSceneConstants.QUERY_APPLY_SHOP);

        //返利金额查询
        processorExecutor.execute(context, ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, ProcessorSubSceneConstants.ASSEMBLE_ACTIVITY_PROCESS);


        return context.getRebateActivityDetailResponse();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long autoApplyGodCouponRebateActivity(Long dpAccountId, Long dpShopId, Integer activityType) {
        List<ActivityModuleResponse> appliableActivityList = this.queryActivityList(dpAccountId, dpShopId, activityType, OperationStatusEnum.ONGOING.code);
        if (CollectionUtils.isNotEmpty(appliableActivityList)) {
            //取ActivityModuleDTO中order字段最大的
            appliableActivityList = appliableActivityList.stream().filter(activityModuleDTO -> CollectionUtils.isNotEmpty(activityModuleDTO.getAppliableShopIdList()) && activityModuleDTO.getAppliableShopIdList().contains(dpShopId) && activityModuleDTO.getOrder() != null).collect(Collectors.toList());
            //筛选后如果为空，查询未开始的活动
            if (CollectionUtils.isEmpty(appliableActivityList)) {
                appliableActivityList = this.queryActivityList(dpAccountId, dpShopId, activityType, OperationStatusEnum.NOT_START.code);
                if (CollectionUtils.isEmpty(appliableActivityList)) {
                    log.error("autoApplyGodCouponRebateActivityNoAppliableActivity,dpAccountId:{},dpShopId:{},activityTyp:{}", dpAccountId, dpShopId, activityType);
                    return null;
                }
                //筛选当前店铺能报名的活动  并且选择order最大的活动
                appliableActivityList = appliableActivityList.stream().filter(activityModuleDTO -> CollectionUtils.isNotEmpty(activityModuleDTO.getAppliableShopIdList()) && activityModuleDTO.getAppliableShopIdList().contains(dpShopId) && activityModuleDTO.getOrder() != null).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(appliableActivityList)) {
                    log.error("autoApplyGodCouponRebateActivityNoAppliableActivity,dpAccountId:{},dpShopId:{},activityTyp:{}", dpAccountId, dpShopId, activityType);
                    return null;
                }
            }
            appliableActivityList.sort(Comparator.comparing(ActivityModuleResponse::getOrder).reversed());
            ActivityModuleResponse activityModuleResponse = appliableActivityList.get(0);
            ApplyActivityRequest applyActivityRequest = new ApplyActivityRequest();
            applyActivityRequest.setDpShopIds(Lists.newArrayList(dpShopId));
            applyActivityRequest.setActivityViewId(activityModuleResponse.getId());
            applyActivityRequest.setType(RebateSettleTypeEnum.BUSINESS.getCode());
            applyActivityRequest.setAccountId(dpAccountId);
            List<Long> applyResult = this.batchApplyActivity(applyActivityRequest);
            if (CollectionUtils.isNotEmpty(applyResult)) {
                Long autoApplyActivityId = ResourceEncoder.decodeResourceViewId(activityModuleResponse.getId());
                log.info("autoApplyGodCouponRebateActivitySuccess,dpAccountId:{},dpShopId:{},autoApplyActivityId:{}", dpAccountId, dpShopId, autoApplyActivityId);
                return applyResult.get(0);
            }
        }
        return null;
    }
}
