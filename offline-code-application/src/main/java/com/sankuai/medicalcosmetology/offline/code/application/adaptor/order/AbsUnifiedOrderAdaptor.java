package com.sankuai.medicalcosmetology.offline.code.application.adaptor.order;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.common.enums.AmountType;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.api.common.enums.UnifiedOrderField;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderSKUDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.idmapper.service.CityMapperService;
import com.sankuai.carnation.distribution.intention.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.application.model.order.*;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ProductTypeAnalyser;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2022/09/29
 **/
@Component
public abstract class AbsUnifiedOrderAdaptor implements OrderInfoAdaptorIface {

    private static final String PASS_PARAM_DISTRIBUTION_KEY = "DISTRIBUTION_BASIC_INFO";

    private static final String PROMOTE_CHANNEL_KEY = "PROMOTE_CHANNEL_INFO";

    private static final String M_LIVE_DISTRIBUTION_KEY = "m_live";

    private static final String M_LIVE_ID_KEY = "mLiveId";

    private static final int SKU_PURCHASE_TO_CONSUME_RATIO = 6032;

    @Autowired
    private CityMapperService cityMapperService;

    @Override
    public OrderInfoBO getOrderInfo(String orderId) {
        UnifiedOrderWithId unifiedOrder = getUnifiedOrder(orderId);
        if (unifiedOrder == null) {
            return null;
        }
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderType(getSupportOrderType());
        orderInfoBO.setOrderId(orderId);
        orderInfoBO.setLongOrderId(unifiedOrder.getLongOrderId());
        orderInfoBO.setOrderTime(unifiedOrder.getAddTime());
        orderInfoBO.setPayTime(unifiedOrder.getPaySuccessTime());
        orderInfoBO.setBuySuccessTime(unifiedOrder.getBuySuccessTime());
        boolean isMt = PayPlatform.isMtPlatform(unifiedOrder.getPlatform());
        orderInfoBO.setProductType(ProductTypeAnalyser.fromOrderBizType(unifiedOrder.getBizType()).getCode());
        orderInfoBO.setOrderBizType(unifiedOrder.getBizType());
        orderInfoBO.setProductId(analyseProductId(unifiedOrder));
        orderInfoBO.setDpProductId(NumberUtils.toLong(unifiedOrder.getFirstSku().getSpugId()));
        orderInfoBO.setPlatform(isMt ? PlatformEnum.MT.getCode() : PlatformEnum.DP.getCode());
        orderInfoBO.setUserId(isMt ? unifiedOrder.getMtUserId() : unifiedOrder.getUserId());
        orderInfoBO.setDpUserId(unifiedOrder.getUserId());
        orderInfoBO.setMtUserId(unifiedOrder.getMtUserId());
        orderInfoBO.setPayPlatform(PayPlatform.getByCode(unifiedOrder.getPlatform()));
        orderInfoBO.setShopId(isMt ? unifiedOrder.getLongMtShopId() : unifiedOrder.getLongShopId());
        orderInfoBO.setDpShopId(unifiedOrder.getLongShopId());
        orderInfoBO.setMtShopId(unifiedOrder.getLongMtShopId());
        if (orderInfoBO.getShopId() == null) {
            orderInfoBO.setShopId(0L);
        }
        orderInfoBO.setDistributionCode(getOrderDistributionCode(unifiedOrder));
//        orderInfoBO.setTechExtParam(getTechExtParam(unifiedOrder));
        orderInfoBO.setExtDistributionInfo(getPassParam(unifiedOrder));
        orderInfoBO.setPaymentDetailList(getOrderPayment(unifiedOrder));
        orderInfoBO.setSkuList(getOrderSkuList(unifiedOrder));
        orderInfoBO.setCityId(pickUpCityId(unifiedOrder));
        orderInfoBO.setDiscountList(pickUpDiscount(unifiedOrder));
//        orderInfoBO.setLiveParam(getLiveParam(unifiedOrder));
        orderInfoBO.setTradePlatform(unifiedOrder.getPlatform());
        orderInfoBO.setTotalAmount(unifiedOrder.getTotalAmount());
        return orderInfoBO;
    }

    private List<OrderSkuBO> getOrderSkuList(UnifiedOrderWithId unifiedOrder) {
        List<OrderSkuBO> orderSkuList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(unifiedOrder.getSkus())) {
            orderSkuList = unifiedOrder.getSkus().stream()
                    .filter(Objects::nonNull)
                    .map(orderSku -> {
                        OrderSkuBO skuBO = new OrderSkuBO();
                        int purchaseToConsumeRatio = Optional.of(orderSku)
                                .map(UnifiedOrderSKUDTO::getSkuExtraFields)
                                .map(map -> map.get(SKU_PURCHASE_TO_CONSUME_RATIO))
                                .map(val -> NumberUtils.toInt(val, 1))
                                .orElse(1);
                        skuBO.setQuantity(orderSku.getQuantity());
                        skuBO.setSkuVerifyCount(purchaseToConsumeRatio);
                        skuBO.setTotalVerifyCount(orderSku.getQuantity() * purchaseToConsumeRatio);
                        skuBO.setSkuId(NumberUtils.toLong(orderSku.getSkuId()));
                        skuBO.setProductId(NumberUtils.toLong(orderSku.getSpugId()));
                        skuBO.setProductName(orderSku.getSkuTitle());
                        skuBO.setUnitValue(orderSku.getUnitValue());
                        return skuBO;
                    })
                    .collect(Collectors.toList());
        }
        return orderSkuList;
    }

    private List<OrderPaymentDetailBO> getOrderPayment(UnifiedOrderWithId unifiedOrder) {
        List<OrderPaymentDetailBO> payDetailList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(unifiedOrder.getPaymentDetails())) {
            payDetailList = unifiedOrder.getPaymentDetails().stream()
                    .filter(Objects::nonNull)
                    .map(payment -> {
                        OrderPaymentDetailBO paymentDetailBO = new OrderPaymentDetailBO();
                        paymentDetailBO.setPaymentDetailId(payment.getPaymentDetailID());
                        paymentDetailBO.setAmountId(payment.getAmountId());
                        paymentDetailBO.setAmount(payment.getAmount());
                        paymentDetailBO.setAmountType(payment.getAmountType());
                        paymentDetailBO.setSkuId(payment.getSkuId());
                        paymentDetailBO.setPaymentReceiptId(payment.getPaymentReceiptId());
                        paymentDetailBO.setOrderPaymentId(payment.getOrderPaymentId());
                        paymentDetailBO.setPayPlatform(payment.getPayPlatform());
                        paymentDetailBO.setAddTime(payment.getAddTime());
                        return paymentDetailBO;
                    })
                    .collect(Collectors.toList());
        }
        return payDetailList;
    }

    private Map<String, String> getPassParam(UnifiedOrderWithId unifiedOrder) {
        Map<String, String> map = Maps.newHashMap();
        if (MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get(PASS_PARAM_DISTRIBUTION_KEY))) {
            String passParamDistributionString = unifiedOrder.getNibExtraFields().get(PASS_PARAM_DISTRIBUTION_KEY);
            PassParamDistributionBO passParamDistributionBO = JSON.parseObject(passParamDistributionString, PassParamDistributionBO.class);
            if (CollectionUtils.isNotEmpty(passParamDistributionBO.getDistributionInfoList())) {
                map = passParamDistributionBO.getDistributionInfoList().stream()
                        .filter(Objects::nonNull)
                        .filter(passParam -> StringUtils.isNotBlank(passParam.getDistributionType()) && StringUtils.isNotBlank(passParam.getBizExtend()))
                        .collect(Collectors.toMap(PassParamDistributionInfoBO::getDistributionType, PassParamDistributionInfoBO::getBizExtend));
            }
        }
        return map;
    }

    private String getOrderDistributionCode(UnifiedOrderWithId unifiedOrder) {
        String distributionCode = null;
        if (MapUtils.isNotEmpty(unifiedOrder.getExtraFields())) {
            distributionCode = unifiedOrder.getExtraFields().getOrDefault(UnifiedOrderField.distributionParam.fieldKey, "");
            if (StringUtils.isBlank(distributionCode) && MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields())) {
                String nibDistribution = unifiedOrder.getNibExtraFields().get("distribution_param");
                distributionCode = Optional.ofNullable(nibDistribution).orElse(distributionCode);
            }
        }
        return StringUtils.isNotBlank(distributionCode) ? distributionCode : "";
    }

//    private TechnicianDistributionExtParamBO getTechExtParam(UnifiedOrderWithId unifiedOrder) {
//        String techExtParamStr = Optional.of(unifiedOrder)
//                .map(UnifiedOrderWithId::getExtraFields)
//                .map(extFields -> extFields.get(UnifiedOrderField.craftsmanExtParam.fieldKey))
//                .filter(StringUtils::isNotBlank)
//                .orElse("");
//        if (StringUtils.isBlank(techExtParamStr)) {
//            techExtParamStr = Optional.of(unifiedOrder)
//                    .map(UnifiedOrderWithId::getNibExtraFields)
//                    .map(extFields -> extFields.get(String.valueOf(UnifiedOrderField.craftsmanExtParam.fieldKey)))
//                    .filter(StringUtils::isNotBlank)
//                    .orElse("");
//        }
//        if (StringUtils.isNotBlank(techExtParamStr)) {
//            return JSON.parseObject(techExtParamStr, TechnicianDistributionExtParamBO.class);
//        }
//        return null;
//    }

//    private OrderLiveParamBO getLiveParam(UnifiedOrderWithId unifiedOrder) {
//        // 团单：DISTRIBUTION_BASIC_INFO 参数示例，格式参考线上示例订单：170677436036898300088433
//        long liveId = 0;
//        String liveChannel = null;
//        if (MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get(PASS_PARAM_DISTRIBUTION_KEY))) {
//            String passParamDistributionString = unifiedOrder.getNibExtraFields().get(PASS_PARAM_DISTRIBUTION_KEY);
//            PassParamDistributionBO passParamDistributionBO = JSON.parseObject(passParamDistributionString, PassParamDistributionBO.class);
//            if (CollectionUtils.isNotEmpty(passParamDistributionBO.getDistributionInfoList())) {
//                PassParamDistributionInfoBO livePassParam = passParamDistributionBO.getDistributionInfoList().stream()
//                        .filter(Objects::nonNull)
//                        .filter(passParam -> StringUtils.isNotBlank(passParam.getDistributionType()) && StringUtils.isNotBlank(passParam.getBizExtend()))
//                        .filter(passParam -> passParam.getDistributionType().equalsIgnoreCase(M_LIVE_DISTRIBUTION_KEY))
//                        .findFirst()
//                        .orElse(null);
//                long analyseId = Optional.ofNullable(livePassParam)
//                        .map(param -> JSON.parseObject(param.getBizExtend()))
//                        .filter(json -> json.containsKey(M_LIVE_ID_KEY))
//                        .map(json -> json.get(M_LIVE_ID_KEY).toString())
//                        .map(NumberUtils::toLong)
//                        .filter(id -> id > 0L)
//                        .orElse(0L);
//                if (analyseId > 0L) {
//                    liveId = analyseId;
//                    liveChannel = livePassParam.getDistributionType();
//                }
//            }
//        }
//        if (liveId > 0L) {
//            return new OrderLiveParamBO(liveChannel, liveId);
//        }
//        // 团单 PROMOTE_CHANNEL_INFO 参数示例，格式参考线上示例订单：170677436036898300088433
//        if (MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get(PROMOTE_CHANNEL_KEY))) {
//            String promoteChannelString = unifiedOrder.getNibExtraFields().get(PROMOTE_CHANNEL_KEY);
//            PromoteChannelInfoBo promoteChannelInfoBo = JSON.parseObject(promoteChannelString, PromoteChannelInfoBo.class);
//            PromoteChannelInfoBo livePromoteChannelInfoBO = Optional.ofNullable(promoteChannelInfoBo)
//                    .filter(param -> StringUtils.isNotBlank(param.getPromoteType()))
//                    .filter(param -> param.getPromoteType().equalsIgnoreCase(M_LIVE_DISTRIBUTION_KEY))
//                    .orElse(null);
//            long analyseId = Optional.ofNullable(livePromoteChannelInfoBO)
//                    .filter(param -> StringUtils.isNotBlank(param.getPromoteExtend()))
//                    .map(param -> JSON.parseObject(param.getPromoteExtend()))
//                    .filter(json -> json.containsKey(M_LIVE_ID_KEY))
//                    .map(json -> json.get(M_LIVE_ID_KEY).toString())
//                    .map(NumberUtils::toLong)
//                    .filter(id -> id > 0L)
//                    .orElse(0L);
//            if (analyseId > 0L) {
//                liveId = analyseId;
//                liveChannel = promoteChannelInfoBo.getPromoteType();
//            }
//        }
//        if (liveId > 0L) {
//            return new OrderLiveParamBO(liveChannel, liveId);
//        }
//        // 次卡，扩展字段 60254 ，格式参考线上订单：170662498930617200120658
//        String promoteChannelString = null;
//        if (MapUtils.isNotEmpty(unifiedOrder.getExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getExtraFields().get(UnifiedOrderField.promoteChannelInfo.fieldKey))) {
//            promoteChannelString = unifiedOrder.getExtraFields().get(UnifiedOrderField.promoteChannelInfo.fieldKey);
//        }
//        if (StringUtils.isBlank(promoteChannelString) && MapUtils.isNotEmpty(unifiedOrder.getNibExtraFields()) && StringUtils.isNotBlank(unifiedOrder.getNibExtraFields().get(String.valueOf(UnifiedOrderField.promoteChannelInfo.fieldKey)))) {
//            promoteChannelString = unifiedOrder.getNibExtraFields().get(String.valueOf(UnifiedOrderField.promoteChannelInfo.fieldKey));
//        }
//        if (StringUtils.isNotBlank(promoteChannelString)) {
//            PromoteChannelInfoBo promoteChannelInfoBo = JSON.parseObject(promoteChannelString, PromoteChannelInfoBo.class);
//            PromoteChannelInfoBo livePromoteChannelInfoBO = Optional.ofNullable(promoteChannelInfoBo)
//                    .filter(param -> StringUtils.isNotBlank(param.getPromoteType()))
//                    .filter(param -> param.getPromoteType().equalsIgnoreCase(M_LIVE_DISTRIBUTION_KEY))
//                    .orElse(null);
//            long analyseId = Optional.ofNullable(livePromoteChannelInfoBO)
//                    .filter(param -> StringUtils.isNotBlank(param.getPromoteExtend()))
//                    .map(param -> JSON.parseObject(param.getPromoteExtend()))
//                    .filter(json -> json.containsKey(M_LIVE_ID_KEY))
//                    .map(json -> json.getString(M_LIVE_ID_KEY))
//                    .map(NumberUtils::toLong)
//                    .filter(id -> id > 0L)
//                    .orElse(0L);
//            if (analyseId > 0L) {
//                liveId = analyseId;
//                liveChannel = promoteChannelInfoBo.getPromoteType();
//            }
//        }
//        if (liveId > 0L) {
//            return new OrderLiveParamBO(liveChannel, liveId);
//        }
//        return null;
//    }

    private int pickUpCityId(UnifiedOrderWithId unifiedOrder) {
        boolean isMt = PayPlatform.isMtPlatform(unifiedOrder.getPlatform());
        int cityId = unifiedOrder.getCityId();
        int dpCityId = NumberUtils.toInt(Optional.ofNullable(unifiedOrder.getNibExtraFields()).orElse(Maps.newHashMap()).get("dpCityId"), 0);
        if (!isMt) {
            return cityId;
        }
        if (dpCityId > 0) {
            Cat.logEvent("OrderCity", cityId == dpCityId ? "Match" : "NotMatch");
            cityId = dpCityId;
        } else {
            Cat.logEvent("OrderCity", "NoDpCityExists");
        }
        Integer mapCityId = cityMapperService.dp2mt(cityId);
        return Optional.ofNullable(mapCityId).orElse(0);
    }

    private List<OrderDiscountBO> pickUpDiscount(UnifiedOrderWithId unifiedOrder) {
        if (CollectionUtils.isEmpty(unifiedOrder.getDiscounts())) {
            return Lists.newArrayList();
        }
        return unifiedOrder.getDiscounts().stream()
                .filter(discount -> discount.getDiscountType() == AmountType.COUPON.value
                        || discount.getDiscountType() == AmountType.SHOPCOUPON.value)
                .map(discount -> OrderDiscountBO.builder()
                        .type(discount.getDiscountType())
                        .discountAmount(discount.getDiscountAmount())
                        .discountId(discount.getDiscountId())
                        .build()
                )
                .collect(Collectors.toList());
    }

    @Override
    public boolean support(int orderType) {
        return orderType == getSupportOrderType();
    }

    protected abstract UnifiedOrderWithId getUnifiedOrder(String orderId);

    protected abstract int getSupportOrderType();

    protected abstract long analyseProductId(UnifiedOrderWithId unifiedOrder);
}
