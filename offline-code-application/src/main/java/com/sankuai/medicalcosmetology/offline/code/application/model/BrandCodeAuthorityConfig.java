package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/24
 * @Description:
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BrandCodeAuthorityConfig implements Serializable {

    /**
     * 灰度类型
     * @see com.sankuai.medicalcosmetology.offline.code.application.enums.GrayTypeEnum
     */
    private Integer type;

    /**
     * 灰度点评门店id
     */
    private List<Long> dpShopIdList;

    /**
     * 灰度后台一级类目id
     */
    private List<Integer> backFirstCategoryIdList;

    /**
     * 灰度统一客户id
     */
    private List<Long> customerIdList;
}
