package com.sankuai.medicalcosmetology.offline.code.application.enums;
import lombok.Getter;
import java.util.Arrays;
@Getter
public enum ManageTypeEnum {
    DIRECT_SALE(0, "直营","否"),
    CHANNEL_AGENT(1, "渠道代理","是");
    public final int code;
    public final String desc;
    public final String commonDesc;
    ManageTypeEnum(int code, String desc,String commonDesc) {
        this.code = code;
        this.desc = desc;
        this.commonDesc = commonDesc;
    }
    public static String getDescStr(Object code) {
        if (code instanceof Integer){
            int codeInt = (int) code;
            return Arrays.stream(values()).filter(t -> t.code == codeInt).findFirst().map(ManageTypeEnum::getDesc).orElse("");
        }
        return "";
    }
    public static String getCommonDescStr(Object code) {
        if (code instanceof Integer){
            int codeInt = (int) code;
            return Arrays.stream(values()).filter(t -> t.code == codeInt).findFirst().map(ManageTypeEnum::getCommonDesc).orElse("");
        }
        return "";
    }
}