package com.sankuai.medicalcosmetology.offline.code.application.model;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@NoArgsConstructor
@Data
public class StoreWithdrawInfo {
    private String accountPhone;
    private String shopName;
    private String fetchAccount;
    private String shopAmount;
    private String totalAmount;
    private String amountComposition;
    private Date fetchTime;
}
