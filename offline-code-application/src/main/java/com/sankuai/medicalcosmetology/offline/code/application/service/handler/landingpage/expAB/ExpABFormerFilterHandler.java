package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.QueryExpABInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.model.ExpABFormerFilterConfig;
import com.sankuai.medicalcosmetology.offline.code.application.model.ExpABFormerFilterField;
import com.sankuai.medicalcosmetology.offline.code.application.model.PromoCodeSwitchConfig;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.PromoCodeLandingPageIface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/12/18
 * @Description:
 */
@Service
public class ExpABFormerFilterHandler {

    @Autowired
    private List<ExpABFormerFilterIface> ifaceList;

    /**
     * 是否过滤
     * @param request
     * @return true：命中过滤条件
     */
    public boolean queryFilterResult(QueryExpABInfoRequest request) {
        List<ExpABFormerFilterConfig> configs = Lion.getList(Environment.getAppName(), LionConstant.EXP_AB_FORMER_FILTER_CONFIG, ExpABFormerFilterConfig.class);
        boolean filter = false;
        for (ExpABFormerFilterConfig config : configs) {
            if (config != null && request.getExpId().equals(config.getExpId())) {
                filter = filter || queryFilterResultByConfig(request, config);
            }
        }
        return filter;
    }

    private boolean queryFilterResultByConfig(QueryExpABInfoRequest request, ExpABFormerFilterConfig config) {
        List<ExpABFormerFilterField> fields = config.getFields();
        boolean filter = false;
        for (ExpABFormerFilterField field : fields) {
            String key = field.getKey();
            ExpABFormerFilterIface selectedIface = ifaceList.stream()
                    .filter(iface -> iface.support(key))
                    .findFirst()
                    .orElse(null);
            if (selectedIface != null) {
                filter = filter || selectedIface.queryFilterResult(request, field.getFilterRule());
            }
        }
        return filter;
    }
}
