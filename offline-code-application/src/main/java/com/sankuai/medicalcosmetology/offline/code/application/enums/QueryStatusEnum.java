package com.sankuai.medicalcosmetology.offline.code.application.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum QueryStatusEnum {
    SUCCESS(200, "查询完成"),
    QUERY_DOING(201, "查询中..."),
    RESULT_IS_EMPTY(202, "未查询到相关数据"),
    TIME_OUT(203, "查询超时");

    public final int code;
    public final String desc;

    QueryStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        return Arrays.stream(values()).filter(t -> t.code == code).findFirst().map(QueryStatusEnum::getDesc).orElse("");
    }

}
