package com.sankuai.medicalcosmetology.offline.code.application.constants;

import com.sankuai.medicalcosmetology.offline.code.application.enums.ExpABSceneEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/12/20
 * @Description: 线下码点位
 */
public class CouponPositionConstant {

    /**
     * 领券栏点位-美团
     */
    public static final String COLUMN_MT_APP_POSITION = "2153";
    /**
     * 领券栏点位-点评
     */
    public static final String COLUMN_DP_APP_POSITION = "3153";

    /**
     * 领券栏点位-美团小程序
     */
    public static final String COLUMN_MT_WX_MP_POSITION = "1153";

    /**
     * 塞券点位-美团
     */
    public static final String PUT_MT_APP_POSITION = "5020";

    /**
     * 塞券点位-点评
     */
    public static final String PUT_DP_APP_POSITION = "4020";

    /**
     * 塞券点位-美团小程序
     */
    public static final String PUT_MT_WX_MP_POSITION = "1020";

    /**
     * 购后塞券点位-美团
     */
    public static final String PUT_AFTER_PURCHASE_MT_APP_POSITION = "5025";

    /**
     * 购后塞券点位-美团小程序
     */
    public static final String PUT_AFTER_PURCHASE_MT_WX_MP_POSITION = "1025";

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum CouponPositionSceneEnum {
        QUERY(1, "查询"),
        PUT_BEFORE_PURCHASE(2, "购前塞券"),
        PUT_AFTER_PURCHASE(3, "购后塞券");

        private int code;
        private String desc;

        public static CouponPositionSceneEnum fromCode(int code) {
            CouponPositionSceneEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                CouponPositionSceneEnum enumValue = var1[var3];
                if (enumValue.getCode() == code) {
                    return enumValue;
                }
            }

            return null;
        }
    }
}
