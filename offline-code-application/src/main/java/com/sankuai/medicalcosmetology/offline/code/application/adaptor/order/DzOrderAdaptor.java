package com.sankuai.medicalcosmetology.offline.code.application.adaptor.order;

import com.dianping.cat.Cat;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.api.common.enums.UnifiedOrderSkuField;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderSKUDTO;
import com.sankuai.carnation.distribution.common.enums.ProductTypeEnum;
import com.sankuai.carnation.distribution.intention.enums.DistributionOrderTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.UnifiedOrderAclService;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ProductTypeAnalyser;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 功能描述: 到综订单信息转换
 *
 * <AUTHOR>
 * @date 2022/04/28
 **/
@Service
public class DzOrderAdaptor extends AbsUnifiedOrderAdaptor {

    private static final String  DEAL_ID_MATCH_TYPE = "Order.Deal.IdMatch";

    @Autowired
    private UnifiedOrderAclService unifiedOrderAclService;

    @Override
    public boolean support(int orderType) {
        return DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode() == orderType;
    }

    @Override
    protected UnifiedOrderWithId getUnifiedOrder(String orderId) {
        UnifiedOrderWithId unifiedOrder = unifiedOrderAclService.getOrder(orderId);
        if (unifiedOrder == null || StringUtils.isBlank(unifiedOrder.getUnifiedOrderId())) {
            return null;
        }
        return unifiedOrder;
    }

    @Override
    protected int getSupportOrderType() {
        return DistributionOrderTypeEnum.DZ_TRADE_ORDER.getCode();
    }

    protected long analyseProductId(UnifiedOrderWithId unifiedOrder) {
        boolean isMt = PayPlatform.isMtPlatform(unifiedOrder.getPlatform());
        ProductTypeEnum productType = ProductTypeAnalyser.fromOrderBizType(unifiedOrder.getBizType());
        long productId = NumberUtils.toLong(unifiedOrder.getFirstSku().getSpugId());
        if (ProductTypeEnum.TUAN_DEAL.equals(productType) && isMt) {
            Long mtProductId = Optional.of(unifiedOrder)
                    .map(UnifiedOrderWithId::getFirstSku)
                    .map(UnifiedOrderSKUDTO::getSkuExtraFields)
                    .map(skuFields -> skuFields.get(UnifiedOrderSkuField.mtDealId.fieldKey))
                    .map(NumberUtils::toLong)
                    .orElse(0L);
            String eventName;
            if (mtProductId == 0L) {
                eventName = "Unknown." + unifiedOrder.getUnifiedOrderId();
            } else if (mtProductId.equals(productId)) {
                eventName = "Match";
            } else {
                eventName = "NotMatch";
            }
            Cat.logEvent(DEAL_ID_MATCH_TYPE, eventName);
            if (mtProductId > 0L) {
                productId = mtProductId;
            }
        }
        return productId;
    }
}
