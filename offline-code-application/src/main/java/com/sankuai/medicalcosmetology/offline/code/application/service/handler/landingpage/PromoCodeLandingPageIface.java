package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;
import com.dianping.lion.Environment;
import com.meituan.mdp.boot.starter.crypto.utils.MdpCryptoUtils;
import com.meituan.mdp.boot.starter.crypto.utils.MdpPoiIdCryptoResult;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/8
 * @Description:
 */
public interface PromoCodeLandingPageIface {

    String NODE_LANDING_URL = "https://%sg.meituan.com/qrcode/transfer?scene=%s&q=%s";

    boolean support(PromoCodeLandingPageRequest request);

    String queryPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request);

    PromoCodeLandingUrlDTO queryAllPlatformPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request);

    default String buildNodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        String pageUrl = "";
        switch (PromoCodeType.getByCode(request.getCodeType())) {
            case SHOP_CODE:
                pageUrl = String.format(NODE_LANDING_URL, Environment.isTestEnv() ? "test-" : "", "promo_shop", request.getPoiId());
                return pageUrl;
            case GOODS_CODE:
                pageUrl = String.format(NODE_LANDING_URL, Environment.isTestEnv() ? "test-" : "", "promo_goods", request.getPoiId() + "_" + request.getGoodsId());
                return pageUrl;
            case STAFF_CODE:
                pageUrl = String.format(NODE_LANDING_URL, Environment.isTestEnv() ? "test-" : "", "staff", request.getStaffCodeId());
                return pageUrl;
            case BRAND_CODE:
                pageUrl = String.format(NODE_LANDING_URL, Environment.isTestEnv() ? "test-" : "", "brand", request.getCustomerId());
                return pageUrl;
        }
        return pageUrl;
    }
}
