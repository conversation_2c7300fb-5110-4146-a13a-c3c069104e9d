package com.sankuai.medicalcosmetology.offline.code.application.service.handler.staffbargain;

import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.BargainStaffTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ProductTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.StaffBargainDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/12 17:48
 */
@Component
@Slf4j
public class PrivateLiveConsultantBargainCodeHandler extends AbstractStaffBargainCodeHandler {

    @Override
    public Boolean support(StaffBargainCodeContext context) {
        return Objects.equals(BargainStaffTypeEnum.CONSULTANT_TASK.getCode(), context.getStaffType().getCode());
    }

    @Override
    Integer getCodeBizType() {
        return BizTypeEnum.CONSULTANT_BARGAIN_CODE.getType();
    }

    @Override
    public StaffBargainDetail init(StaffBargainCodeContext context) {
        //TODO 暂时放这里，下期支持多商品一起改造
        StaffBargainDetail staffBargainDetail = new StaffBargainDetail();
        staffBargainDetail.setBizType(context.getBizTypeEnum().getCode());
        staffBargainDetail.setDpShopId(0L);
        staffBargainDetail.setStaffType(context.getStaffType().getCode());
        staffBargainDetail.setStaffId(context.getStaffId());
        staffBargainDetail.setSkuId(ObjectUtils.isEmpty(context.getDealInfoList())
                ? context.getPreInfoList().get(0).getDeals().get(0).getBizDealId()
                : context.getDealInfoList().get(0).getDeals().get(0).getDealId());
        staffBargainDetail.setProductId(ObjectUtils.isEmpty(context.getDealInfoList())
                ? context.getPreInfoList().get(0).getBizProductId()
                : context.getDealInfoList().get(0).getDpDealGroupId());
        staffBargainDetail.setProductType(ObjectUtils.isEmpty(context.getDealInfoList())
                ? ProductTypeEnum.PREPAY.getCode() : ProductTypeEnum.TUAN_DEAL.getCode());
        staffBargainDetail.setBargainPrice(context.getBargainPrice());
        staffBargainDetail.setMinBargainPrice(context.getMinBargainPrice());
        staffBargainDetail.setMaxBargainPrice(context.getMaxBargainPrice());
        return staffBargainDetail;
    }
}
