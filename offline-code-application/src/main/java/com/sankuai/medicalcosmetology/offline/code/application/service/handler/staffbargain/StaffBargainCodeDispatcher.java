package com.sankuai.medicalcosmetology.offline.code.application.service.handler.staffbargain;

import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.StaffBargainCodeDTO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.exception.BizSceneException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:47
 */
@Component
@Slf4j
public class StaffBargainCodeDispatcher {

    @Resource
    private List<StaffBargainCodeHandlerIface> staffBargainCodeHandlerList;

    /**
     * 这里先处理私域直播的改价，职人改价走老逻辑，后续随需求迁移过来
     * @param context
     * @return
     */
    public StaffBargainCodeDTO handle(StaffBargainCodeContext context) {
        StaffBargainCodeHandlerIface handlerIface = Optional.ofNullable(staffBargainCodeHandlerList)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(handler -> handler.support(context))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(handlerIface)) {
            throw new BizSceneException("职人议价不支持当前场景");
        }
        return handlerIface.handle(context);
    }
}
