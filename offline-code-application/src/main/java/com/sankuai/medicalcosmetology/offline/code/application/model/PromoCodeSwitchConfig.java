package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import lombok.Data;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/8
 * @Description:
 */
@Data
public class PromoCodeSwitchConfig {

    // 码类型
    PromoCodeType qrCodeType;

    // 美团门店
    List<Long> mtShopIds;

    // 门店后台二级类目Id
    List<Integer> categoryId;

    // 完全切换开关
    Boolean fullSwitch;
}
