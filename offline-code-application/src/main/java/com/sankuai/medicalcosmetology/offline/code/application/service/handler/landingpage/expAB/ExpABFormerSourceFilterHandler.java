package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageScanSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.QueryExpABInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.MagicMemberCouponScenarioDomainEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/12/31
 * @Description:
 */
@Service
public class ExpABFormerSourceFilterHandler implements ExpABFormerFilterIface {

    @Override
    public boolean support(String key) {
        return "source".equals(key);
    }

    @Override
    public boolean queryFilterResult(QueryExpABInfoRequest request, String filterRule) {
        if (StringUtils.isBlank(filterRule)) {
            return false;
        }
        switch (filterRule) {
            case "groundPromotionMagicMemberCoupon":
                // 非兼职地推渠道时，不执行ab
                return !request.getSource().equals(LandingPageScanSourceEnum.GROUND_PROMOTION_PART_TIME.getCode());
            default:
                return false;
        }
    }
}
