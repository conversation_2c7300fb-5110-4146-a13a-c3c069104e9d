package com.sankuai.medicalcosmetology.offline.code.application.service.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.cat.Cat;
import com.dianping.gmkt.event.api.enums.*;
import com.dianping.gmkt.event.api.model.DrawActivity;
import com.dianping.gmkt.event.api.model.DrawEventConfigBean;
import com.dianping.gmkt.event.api.model.DrawExecOption;
import com.dianping.gmkt.event.api.model.PrecisePrize;
import com.dianping.gmkt.event.api.promoqrcode.enums.QRActivityType;
import com.dianping.gmkt.event.api.request.DrawEventRequest;
import com.dianping.gmkt.event.api.response.DrawEventResponse;
import com.dianping.gmkt.event.api.response.QueryCouponsResponse;
import com.dianping.gmkt.event.api.v2.model.*;
import com.dianping.gmkt.event.api.v2.request.EidRequest;
import com.dianping.ops.remote.RemoteIpGetter;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.beauty.idmapper.service.UserMapperService;
import com.meituan.beauty.idmapper.service.VirtualUserMapperService;
import com.meituan.mtrace.Tracer;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.ResourceDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.resource.AwardInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.MobileOSEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ResourceTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.LandingCouponRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.CouponPositionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage.CouponConverter;
import com.sankuai.medicalcosmetology.offline.code.application.model.LandingContext;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DrawUtil;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.DrawCouponFailLogDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.CityAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.DrawEventAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.LandingDrawAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.UserAccountAclService;
import com.sankuai.nib.mkt.common.base.enums.PageSourceEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.QueryMagicalMemberTagResponse;
import com.sankuai.nibpt.transparentvalidator.domain.TransparentValidatorParam;
import com.sankuai.nibpt.transparentvalidator.util.TransparentValidatorUtils;
import jodd.util.URLDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/12
 * @Description:
 */
@Service
@Slf4j
public class CouponService {

    @Autowired
    private LandingDrawAclService landingDrawAclService;

    @Autowired
    private DrawEventAclService drawEventAclService;

    @Autowired
    private CityAclService cityAclService;
    
    @Autowired
    private UserAccountAclService userAccountAclService;

    @Autowired
    private VirtualUserMapperService virtualUserMapperService;

    @Autowired
    private UserMapperService userMapperService;

    @Autowired
    private CouponConverter couponConverter;

    private final ThreadPoolExecutor landingPageDrawPool = Rhino.newThreadPool("CouponService.landingPageDrawPool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(10)
                    .withMaxSize(10)
                    .withMaxQueueSize(10000)
                    .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())).getExecutor();

    /**
     * 获取有效的抽奖code
     * @param platform
     * @param operationInfoDTO
     * @return
     */
    public Set<String> getActivityAwardEcode(Integer platform, OperationInfoDTO operationInfoDTO) {
        Set<String> eCodes = Sets.newHashSet();
        if (operationInfoDTO == null) {
            Cat.logEvent("OfflineCode_DrawCoupon", "ActivityNull");
            log.info(getClass().getSimpleName() + ".getActivityAwardEcode error, activity is null");
            return null;
        }
        ResourceDTO resourceConfig = operationInfoDTO.getResourceConfig();
        if (resourceConfig == null || resourceConfig.getAwardInfo() == null) {
            Cat.logEvent("OfflineCode_DrawCoupon", "AwardConfigNull, activityId:" + operationInfoDTO.getActivityId());
            log.info(getClass().getSimpleName() + ".getActivityAwardEcode error, awardConfig is null, activityId is {}", operationInfoDTO.getActivityId());
            return null;
        }
        AwardInfoDTO awardInfo = resourceConfig.getAwardInfo();
        String mainECode = getActivityMainAwardEcode(operationInfoDTO.getActivityId(), platform, awardInfo);
        if (StringUtils.isBlank(mainECode)) {
            Cat.logEvent("OfflineCode_DrawCoupon", "MainEcodeNull, activityId:" + operationInfoDTO.getActivityId());
            log.info(getClass().getSimpleName() + ".getActivityAwardEcode error, mainEcode is null, activityId = {}", operationInfoDTO.getActivityId());
            return null;
        }
        eCodes.add(mainECode);
        List<String> multiEcodes = getActivityMultiAwardEcodes(operationInfoDTO.getActivityId(), platform, awardInfo);
        eCodes.addAll(multiEcodes);
        return eCodes;
    }

    /**
     * 获取有效的领券活动id
     * @param platform
     * @param operationInfoDTO
     * @return
     */
    public Set<String> getActivityAwardEid(Integer platform, OperationInfoDTO operationInfoDTO) {
        Set<String> eCodes = Sets.newHashSet();
        if (operationInfoDTO == null) {
            Cat.logEvent("OfflineCode_DrawCoupon", "ActivityNull");
            log.info(getClass().getSimpleName() + ".getActivityAwardEid error, activity is null");
            return null;
        }
        ResourceDTO resourceConfig = operationInfoDTO.getResourceConfig();
        if (resourceConfig == null || resourceConfig.getAwardInfo() == null) {
            Cat.logEvent("OfflineCode_DrawCoupon", "AwardConfigNull, activityId:" + operationInfoDTO.getActivityId());
            log.info(getClass().getSimpleName() + ".getActivityAwardEid error, awardConfig is null, activityId is {}", operationInfoDTO.getActivityId());
            return null;
        }
        AwardInfoDTO awardInfo = resourceConfig.getAwardInfo();
        String magicEid = getMagicAwardEid(operationInfoDTO.getActivityId(), platform, awardInfo);
        if (StringUtils.isNotBlank(magicEid)) {
            eCodes.add(magicEid);
        }
        if (resourceConfig.getResourceType().equals(ResourceTypeEnum.POST_PURCHASE_COUPON.getCode())) {
            // 购后券，可能发普通的领券活动，非神券
            String mainEid = getActivityMainAwardEid(operationInfoDTO.getActivityId(), platform, awardInfo);
            if (StringUtils.isNotBlank(magicEid)) {
                eCodes.add(mainEid);
            }
        }
        return eCodes;
    }

    public CouponPrizeDetail batchDrawCoupon(Integer platform, LandingContext context, Set<String> eCodeSet, Map<String, EventErrorCode> errorCodeMap) {
        try {
            Map<String, CompletableFuture<PigeonResponse<DrawResult>>> futureMap = Maps.newHashMap();
            for (String eCode : eCodeSet) {
                futureMap.put(eCode, CompletableFuture.supplyAsync(() -> {
                    EidRequest eidRequest = DrawUtil.buildEidRequest(platform.byteValue(), context, eCode);
                    PigeonResponse<DrawResult> response = landingDrawAclService.attendEvent(eidRequest);
                    return response;
                }, landingPageDrawPool));
            }

            CouponPrizeDetail prizeDetail = new CouponPrizeDetail();
            prizeDetail.setPrizeCouponDetail(Lists.newArrayList());
            // 需保证每个请求都执行完毕
            for (String eCode : eCodeSet) {
                PigeonResponse<DrawResult> response = futureMap.get(eCode).get(2, TimeUnit.SECONDS);
                if (response == null) {
                    errorCodeMap.put(eCode, EventErrorCode.FAIL);
                    continue;
                }
                DrawResult drawResult = response.getData();
                EventErrorCode errorCode = (drawResult != null && drawResult.getCode() != null) ? drawResult.getCode()
                        : EventErrorCode.getByCode(response.getErrorCode());
                if (errorCode != null) {
                    errorCodeMap.put(eCode, errorCode);
                    log.info(getClass().getSimpleName() + ".batchDrawCoupon result, userId is {}, platform is {}, ecode is {}, errorCode is {}, drawResult is {}.",
                            context.getUserId(), platform, eCode, errorCode, JSON.toJSONString(drawResult));
                }
                if (response.getCode().equals("200") && response.getErrorCode().equals(EventErrorCode.SUCCESS.code)
                        && drawResult != null && drawResult.getType() != null
                        && drawResult.getType().intValue() == PrizeType.Coupon_Group.code
                        && drawResult.getPrizeData() != null) {
                    CouponPrizeDetail detail = (CouponPrizeDetail) drawResult.getPrizeData();
                    prizeDetail.setPrizeCouponAmount(
                            prizeDetail.getPrizeCouponAmount() + detail.getPrizeCouponAmount());
                    prizeDetail.getPrizeCouponDetail().addAll(detail.getPrizeCouponDetail());

                }
            }
            return prizeDetail;
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".batchDrawCoupon exception, userId is {}, platform is {}, eCodeSet is {}", context.getUserId(), platform, eCodeSet, e);
            return null;
        }
    }

    /**
     * 批量参与领券活动
     * @param platform
     * @param request
     * @param context
     * @param eIdSet
     * @param errorCodeMap
     * @param operationInfoDTO
     * @param drawCouponFailLogDO
     * @param positionSceneEnum
     * @return
     */
    public CouponPrizeDetail batchDrawExecCoupon(Integer platform, LandingCouponRequest request, LandingContext context, Set<String> eIdSet, Map<String, EventErrorCode> errorCodeMap,
                                                  OperationInfoDTO operationInfoDTO, DrawCouponFailLogDO drawCouponFailLogDO, CouponPositionConstant.CouponPositionSceneEnum positionSceneEnum) {
        try {
            Map<String, CompletableFuture<DrawEventResponse>> futureMap = createDrawEventFutures(request, context, eIdSet, operationInfoDTO, positionSceneEnum);
            return processFutureResponses(futureMap, eIdSet, errorCodeMap, context.getUserId(), platform);
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".drawExecCoupon exception, userId is {}, platform is {}, eIdSet is {}", context.getUserId(), platform, eIdSet, e);
            drawCouponFailLogDO.setFailed(true);
            return null;
        }
    }

    private Map<String, CompletableFuture<DrawEventResponse>> createDrawEventFutures(LandingCouponRequest request, LandingContext context, Set<String> eIdSet, OperationInfoDTO operationInfoDTO, CouponPositionConstant.CouponPositionSceneEnum positionSceneEnum) {
        Map<String, CompletableFuture<DrawEventResponse>> futureMap = Maps.newHashMap();
        for (String eId : eIdSet) {
            futureMap.put(eId, CompletableFuture.supplyAsync(() -> {
                DrawEventRequest drawEventRequest = couponConverter.convert2DrawEventRequest(request, context, eId, operationInfoDTO, DrawSceneEnum.DRAW_LIMIT_WITH_VALID_UNUSED_PRIZE, positionSceneEnum);
                if (positionSceneEnum != null) {
                    reportMagicMemberLog("com.sankuai.medicalcosmetology.offline.code.application.service.handler.CouponService", "batchDrawExecCoupon",
                            request, context, positionSceneEnum);
                }
                return landingDrawAclService.drawExec(drawEventRequest);
            }, landingPageDrawPool).exceptionally(e -> {
                log.error(getClass().getSimpleName() + ".drawEventFuture error, eid is {}, request is {}, context is {}, positionSceneEnum is {}, exception is", eId, request, context, positionSceneEnum, e);
                return null;
            }));
        }
        return futureMap;
    }

    private CouponPrizeDetail processFutureResponses(Map<String, CompletableFuture<DrawEventResponse>> futureMap, Set<String> eCodeSet, Map<String, EventErrorCode> errorCodeMap, Long userId, Integer platform) throws Exception {
        CouponPrizeDetail prizeDetail = new CouponPrizeDetail();
        prizeDetail.setPrizeCouponDetail(Lists.newArrayList());

        for (String eCode : eCodeSet) {
            DrawEventResponse response = futureMap.get(eCode).get(3, TimeUnit.SECONDS);
            if (response == null) {
                errorCodeMap.put(eCode, EventErrorCode.FAIL);
                log.info(getClass().getSimpleName() + ".batchDrawCoupon processFutureResponses response is null");
                continue;
            }
            log.info(getClass().getSimpleName() + ".drawMagic processFutureResponses response is {}", JSON.toJSONString(response));
            processDrawEventResponse(response, eCode, errorCodeMap, userId, platform, prizeDetail);
        }
        return prizeDetail;
    }

    private void processDrawEventResponse(DrawEventResponse response, String eCode,
            Map<String, EventErrorCode> errorCodeMap, Long userId, Integer platform, CouponPrizeDetail prizeDetail) {
        log.info(getClass().getSimpleName()
                + ".drawMagic result, userId is {}, platform is {}, ecode is {}, errorCode is {}, drawResult is {}.",
                userId, platform, eCode, response.getResult(), JSON.toJSONString(response));
        errorCodeMap.put(eCode, EventErrorCode.getByCode(response.getResult()));
        if (NumberUtils.isCreatable(response.getType()) && response.getPrizeData() != null) {
            CouponPrizeDetail detail = JSONObject.parseObject(JSONObject.toJSONString(response.getPrizeData()),
                    CouponPrizeDetail.class);
            prizeDetail.setPrizeCouponAmount(prizeDetail.getPrizeCouponAmount() + detail.getPrizeCouponAmount());
            prizeDetail.getPrizeCouponDetail().addAll(detail.getPrizeCouponDetail());
        }
    }

    public List<PrecisePrize> queryPrecisePrizes(Integer platform, LandingCouponRequest request, LandingContext context, Set<String> eIdSet, OperationInfoDTO operationInfoDTO) {
        try {
            Map<String, CompletableFuture<QueryCouponsResponse>> futureMap = Maps.newHashMap();
            for (String eId : eIdSet) {
                futureMap.put(eId, CompletableFuture.supplyAsync(() -> {
                    DrawEventRequest drawEventRequest = couponConverter.convert2DrawEventRequest(request, context, eId, operationInfoDTO, DrawSceneEnum.QUERY_HAVE_PRIZE_ONLY, CouponPositionConstant.CouponPositionSceneEnum.QUERY);
                    reportMagicMemberLog("com.sankuai.medicalcosmetology.offline.code.application.service.handler.CouponService", "queryPrecisePrizes",
                            request, context, CouponPositionConstant.CouponPositionSceneEnum.QUERY);
                    return landingDrawAclService.queryCoupons(drawEventRequest);
                }, landingPageDrawPool));
            }

            List<PrecisePrize> precisePrizeList = Lists.newArrayList();
            // 需保证每个请求都执行完毕
            for (String eId : eIdSet) {
                QueryCouponsResponse response = futureMap.get(eId).get(2, TimeUnit.SECONDS);
                if (response == null) {
                    continue;
                }
                if (!response.getResult().equals(EventErrorCode.SUCCESS.code)) {
                    log.info(getClass().getSimpleName() + ".queryMagicCoupon result, userId is {}, platform is {}, ecode is {}, errorCode is {}, drawResult is {}.",
                            context.getUserId(), platform, eId, response.getResult(), JSON.toJSONString(response));
                }
                if (response.getFetchedCoupons() != null && CollectionUtils.isNotEmpty(response.getFetchedCoupons().getPrizes())) {
                    precisePrizeList.addAll(response.getFetchedCoupons().getPrizes());
                }
            }
            return precisePrizeList;
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".queryMagicCoupon exception, userId is {}, platform is {}, eCodeSet is {}", context.getUserId(), platform, eIdSet, e);
            return null;
        }
    }

    public QueryMagicalMemberTagResponse queryMagicalMemberCoupon(LandingCouponRequest request, LandingContext context, Boolean isAgg) {
        Long mtUserid = request.getPlatform().equals(PlatformEnum.MT.getCode())
                ? context.getUserId() : userMapperService.dp2mt(context.getUserId());
        reportMagicMemberLog("com.sankuai.medicalcosmetology.offline.code.application.service.handler.CouponService", "queryMagicalMemberCoupon",
                request, context, CouponPositionConstant.CouponPositionSceneEnum.QUERY);
        return landingDrawAclService.queryMagicalMemberCouponDTOList(
                CouponConverter.convert2QueryMagicalMemberTagRequest(request, context, mtUserid, isAgg));
    }
    public List<RichDrawLog> batchGetDrawLogs(Integer platform, LandingContext context, Set<String> eCodeSet) {
        List<CompletableFuture<List<RichDrawLog>>> futureList = eCodeSet.stream()
                .map(eCode -> CompletableFuture.supplyAsync(() -> {
                    EidRequest eidRequest = DrawUtil.buildEidRequest(platform.byteValue(), context, eCode);
                    return getDrawLogsByEidRequest(eidRequest);
                }, landingPageDrawPool)).collect(Collectors.toList());
        List<RichDrawLog> richDrawLogList = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]))
                .thenApply(val -> futureList.stream().map(CompletableFuture::join).filter(CollectionUtils::isNotEmpty)
                        .flatMap(List::stream).collect(Collectors.toList()))
                .join();
        return richDrawLogList;
    }

    private List<RichDrawLog> getDrawLogsByEidRequest(EidRequest eidRequest) {
        try {
            PigeonResponse<List<RichDrawLog>> prizeResult = landingDrawAclService.checkWinedRichPrizes(eidRequest);
            if (prizeResult == null || !prizeResult.getCode().equals("200")
                    || !prizeResult.getErrorCode().equals(EventErrorCode.SUCCESS.getCode())
                    || CollectionUtils.isEmpty(prizeResult.getData())) {
                log.error(getClass().getSimpleName()
                                + ".getDrawLogsByEidRequest eventRpcService.checkWinedRichPrizes error, req:{}, resp:{}",
                        JSON.toJSONString(eidRequest), JSON.toJSONString(prizeResult));
                return Lists.newArrayList();
            }
            List<RichDrawLog> richDrawLogs = prizeResult.getData();
            List<RichDrawLog> result = Lists.newArrayList();
            Map<String, RichDrawLog> couponInfoMap = Maps.newHashMap();
            // 过滤条件:券未使用，券可使用
            for (RichDrawLog drawLog : richDrawLogs) {
                if (!drawLog.isUsed() && drawLog.isAvailable()) {
                    couponInfoMap.put(drawLog.getCouponID(), drawLog);
                    result.add(drawLog);
                }
            }
            return result;
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + "getDrawLogsByEidRequest error, eidRequest is {}, exception is",
                    JSON.toJSONString(eidRequest), e);
            return Lists.newArrayList();
        }
    }

    private String getActivityMainAwardEcode(Long activityId, int platform, AwardInfoDTO awardInfo) {
        if (awardInfo == null || CollectionUtils.isEmpty(awardInfo.getMainAwardConfig())) {
            return StringUtils.EMPTY;
        }
        Date now = new Date();
        return getValidEcode(activityId, platform, awardInfo.getMainAwardConfig(), now);
    }


    private String getMagicAwardEid(Long activityId, int platform, AwardInfoDTO awardInfo) {
        if (awardInfo == null || CollectionUtils.isEmpty(awardInfo.getMagicAwardConfig())) {
            return StringUtils.EMPTY;
        }
        Date now = new Date();
        return getValidEid(activityId, platform, awardInfo.getMagicAwardConfig(), now);
    }

    private String getActivityMainAwardEid(Long activityId, int platform, AwardInfoDTO awardInfo) {
        if (awardInfo == null || CollectionUtils.isEmpty(awardInfo.getMainAwardConfig())) {
            return StringUtils.EMPTY;
        }
        Date now = new Date();
        return getValidEid(activityId, platform, awardInfo.getMainAwardConfig(), now);
    }

    private List<String> getActivityMultiAwardEcodes(Long activityId, int platform, AwardInfoDTO awardInfo) {
        List<String> eCodes = Lists.newArrayList();
        if (awardInfo == null || CollectionUtils.isEmpty(awardInfo.getMultiAwardConfig())) {
            return eCodes;
        }
        Date now = new Date();
        for (List<AwardActivityDTO> awardDTOList : awardInfo.getMultiAwardConfig()) {
            String multiECode = getValidEcode(activityId, platform, awardDTOList, now);
            if (StringUtils.isBlank(multiECode)) {
                continue;
            }
            if (eCodes.contains(multiECode)) {
                continue;
            }
            eCodes.add(multiECode);
        }
        return eCodes;
    }

    private String getValidEcode(Long activityId, int platform,
                                 List<AwardActivityDTO> awardDTOList, Date now) {
        if (CollectionUtils.isEmpty(awardDTOList)) {
            return StringUtils.EMPTY;
        }
        for (AwardActivityDTO awardDTO : awardDTOList) {
            if (StringUtils.isBlank(awardDTO.getCode())) {
                continue;
            }
            PigeonResponse<EventInfo> eventInfoResponse = drawEventAclService.queryActivityByECode(awardDTO.getCode());

            if (eventInfoResponse == null || !"200".equals(eventInfoResponse.getCode())
                    || !eventInfoResponse.getErrorCode().equals(EventErrorCode.SUCCESS.getCode())
                    || eventInfoResponse.getData() == null) {
                Cat.logEvent("OfflineCode_DrawCoupon",
                        "EventInfoNull, activityId[" + activityId + "]ecode[" + awardDTO.getCode() + "].");
                continue;
            }
            EventInfo eventInfo = eventInfoResponse.getData();
            if (eventInfo.getStartTime().before(now) && eventInfo.getEndTime().after(now)
                    && eventInfo.getPlatform().getCode() == platform) {
                return awardDTO.getCode();
            } else {
                Cat.logEvent("OfflineCode_DrawCoupon",
                        "eventTimeExpire, activityId[" + activityId + "]ecode[" + awardDTO.getCode() + "].");
            }
        }
        return StringUtils.EMPTY;
    }

    private String getValidEid(Long activityId, int platform,
                                 List<AwardActivityDTO> awardDTOList, Date now) {
        if (CollectionUtils.isEmpty(awardDTOList)) {
            return StringUtils.EMPTY;
        }
        for (AwardActivityDTO awardDTO : awardDTOList) {
            if (StringUtils.isBlank(awardDTO.getCode())) {
                continue;
            }
            DrawEventConfigBean drawEventConfigBean = drawEventAclService.loadConfigByEid(awardDTO.getCode());

            if (drawEventConfigBean == null || drawEventConfigBean.getActivity() == null) {
                Cat.logEvent("OfflineCode_DrawCoupon",
                        "drawEventConfigBean, activityId[" + activityId + "]ecode[" + awardDTO.getCode() + "].");
                continue;
            }
            DrawActivity eventInfo = drawEventConfigBean.getActivity();
            if (eventInfo.getStarttime().before(now) && eventInfo.getEndtime().after(now)
                    && getPlatformByActivityType(eventInfo.getType()) == platform) {
                return awardDTO.getCode();
            } else {
                Cat.logEvent("OfflineCode_DrawCoupon",
                        "eventTimeExpire, activityId[" + activityId + "]ecode[" + awardDTO.getCode() + "].");
            }
        }
        return StringUtils.EMPTY;
    }

    private int getPlatformByActivityType(int activityType) {
        if (activityType == ActivityType.MT_FETCH.code) {
            // 美团
            return 2;
        }
        if (activityType == ActivityType.Fetch.code) {
            // 点评
            return 1;
        }
        return 0;
    }

    private void reportMagicMemberLog(String serviceName, String methodName, LandingCouponRequest request, LandingContext context, CouponPositionConstant.CouponPositionSceneEnum scene) {
        TransparentValidatorParam param = CouponConverter.convert2TransparentValidatorParam(serviceName, methodName, request, context, scene);
        TransparentValidatorUtils.reportValidateParams(param);
    }
}
