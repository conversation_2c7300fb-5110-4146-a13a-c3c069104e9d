package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/8
 * @Description: 默认落地页处理器
 */
@Service
@Slf4j
public class DefaultLandingPageHandler extends AbstractLandingPageHandler {

    @Override
    public boolean support(PromoCodeLandingPageRequest request) {
        boolean isDefaultSource = request.getSource().equals(LandingPageSourceEnum.DEFAULT.getCode());
        if (isDefaultSource && request.getCodeType() == PromoCodeType.GOODS_CODE.code) {
            // 没命中config，则跳转中间页再跳转团详
            return !landingPageSwitchFilter.hitLionSwitchConfig(LionConstant.PROMO_LANDING_GOODS_CODE_JUMP_SWITCH, request.getCodeType(), request.getPoiId());
        }
        return isDefaultSource;
    }

    @Override
    protected boolean needEncrypt() {
        return true;
    }
}
