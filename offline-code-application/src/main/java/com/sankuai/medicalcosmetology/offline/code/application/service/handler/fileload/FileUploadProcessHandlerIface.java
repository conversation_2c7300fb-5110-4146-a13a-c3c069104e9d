package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/20
 * @Description:文件上传处理器
 */
public interface FileUploadProcessHandlerIface<E, P> {

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    boolean support(Integer sceneType);

    /**
     * 执行文件上传处理并返回结果
     *
     * @param items 文件项列表，包含待处理的文件信息
     * @param args 额外的参数数组，用于处理过程中的配置或控制
     * @return 处理结果的字符串表示
     */
    String executeWithResult(InputStream inputStream, P param) throws IOException;

    /**
     * 将文件项列表读取为对象列表
     *
     * @param items 包含文件数据的FileItem列表
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    List<E> readFileAsBean(InputStream inputStream, P param) throws IOException;

    /**
     * 处理解析后的数据对象列表
     *
     * @param modelList 解析后的数据对象列表，每个对象代表一个文件的内容
     * @param args 额外的参数数组，用于数据处理过程中的配置或控制
     */
    void processData(List<E> modelList, P param);
}
