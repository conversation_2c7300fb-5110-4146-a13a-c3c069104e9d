package com.sankuai.medicalcosmetology.offline.code.application.exception;

import com.sankuai.medicalcosmetology.offline.code.api.enums.BaseResponseEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/13
 * @Description:
 */
@Data
public class AuthorizeException extends RuntimeException {

    private BaseResponseEnum response;

    public AuthorizeException(BaseResponseEnum response, String message) {
        super(message);
        this.response = response;
    }

    public int getResponseCode() {
        return response.getCode();
    }

    public String getResponseDesc() {
        return response.getDesc();
    }
}
