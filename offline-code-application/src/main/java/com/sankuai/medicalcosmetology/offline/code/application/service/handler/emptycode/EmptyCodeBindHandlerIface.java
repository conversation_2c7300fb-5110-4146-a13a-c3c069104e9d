package com.sankuai.medicalcosmetology.offline.code.application.service.handler.emptycode;

import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.EmptyCodeBindInfoRequest;

import java.util.List;

/**
 * @Author: fuchangming
 * @Date: 2024/7/17
 * @Description:
 */
public interface EmptyCodeBindHandlerIface {

    Boolean support(EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO);

    Boolean bind(List<QRCodeConfigDTO> qrCodeConfigDTOList, EmptyCodeBindInfoRequest emptyCodeBindInfoRequestDTO,
                 Long accountId);

}
