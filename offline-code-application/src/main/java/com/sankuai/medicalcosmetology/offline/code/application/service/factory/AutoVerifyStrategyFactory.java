package com.sankuai.medicalcosmetology.offline.code.application.service.factory;

import com.sankuai.medicalcosmetology.offline.code.application.service.strategy.AutoVerifyStrategy;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.StaffBargainDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14
 */
@Component
public class AutoVerifyStrategyFactory {

    @Autowired
    private List<AutoVerifyStrategy> strategies;

    public AutoVerifyStrategy getStrategy(StaffBargainDetail staffBargainDetail) {
        return strategies.stream()
                .filter(strategy -> strategy.support(staffBargainDetail))
                .findFirst()
                .orElse(null);
    }
}