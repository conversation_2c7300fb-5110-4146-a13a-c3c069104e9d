package com.sankuai.medicalcosmetology.offline.code.application.service.factory;

import com.sankuai.medicalcosmetology.offline.code.api.enums.BargainStaffTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ProductTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.StaffBargainBizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.staffbargain.config.StaffBargainConfig;
import com.sankuai.medicalcosmetology.offline.code.application.service.strategy.StaffBargainDealSummaryStrategy;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.StaffBargainDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/9
 */
@Component
public class StaffBargainDealSummaryFactory {
    @Autowired
    private List<StaffBargainDealSummaryStrategy> strategyList;

    /**
     * 获取策略实现
     *
     * @param scene 场景
     * @return 策略实现
     */
    public StaffBargainDealSummaryStrategy getStrategy(String scene) {
        return strategyList.stream()
                .filter(strategy -> strategy.support(scene))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到对应的策略"));
    }
}