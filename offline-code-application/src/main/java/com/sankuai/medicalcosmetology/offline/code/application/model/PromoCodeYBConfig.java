package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sankuai.medicalcosmetology.offline.code.application.enums.YBConfigCoditionTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Slf4j
public class PromoCodeYBConfig {
    private String content;
    private PromoCodeYBCondition condition;

    @Data
    public static class PromoCodeYBCondition{
        /**
         * 条件类型
         * see {@link YBConfigCoditionTypeEnum}
         */
        private int type;
        private List<Integer> categoryList;
    }

    public static Map<String,PromoCodeYBConfig> parseConfig(String configStr) {
        if (StringUtils.isBlank(configStr)) {
            return new HashMap<>();
        }
        try {
            return JSON.parseObject(configStr, new TypeReference<Map<String,PromoCodeYBConfig>>(){});
        }catch (Exception e){
            log.error("PromoCodeYBConfig parseConfig error, configStr:{}", configStr, e);
        }
        return new HashMap<>();
    }
}
