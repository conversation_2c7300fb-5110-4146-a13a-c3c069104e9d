package com.sankuai.medicalcosmetology.offline.code.application.service.handler.staffbargain;

import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import lombok.*;
import org.apache.logging.log4j.util.Strings;

/**
 * <AUTHOR>
 * @date 2025/5/19 11:56
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StaffBargainEnv {
    private String userIp = Strings.EMPTY;
    private String dpId = Strings.EMPTY;
    private String uuid = Strings.EMPTY;
    private String mtgsig = Strings.EMPTY;
    /**
     * @see com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum
     */
    private PlatformEnum platform = null;

    /**
     * 对应平台下的用户id
     */
    private Long userId = 0L;
}
