package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.meituan.mtrace.Tracer;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DrawUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/7/31
 * @Description:
 */
@Data
public class LandingContext {

    private Long userId;

    private String dpId;

    private String uuId;

    private String token;

    private String wxOpenId;

    private Map<String, String> cxMap;

    public static LandingContext init() {
        LandingContext context = new LandingContext();
        String userIdStr = Tracer.getContext("userId");
        context.setUserId(StringUtils.isEmpty(userIdStr) ? 0L : Long.valueOf(userIdStr));
        context.setDpId(Tracer.getContext("dpId"));
        context.setUuId(Tracer.getContext("uuId"));
        context.setToken(Tracer.getContext("token"));
        context.setWxOpenId(Tracer.getContext("wxOpenId"));
        context.setCxMap(DrawUtil.genCommonCxMapFromTracer());
        return context;
    }
}
