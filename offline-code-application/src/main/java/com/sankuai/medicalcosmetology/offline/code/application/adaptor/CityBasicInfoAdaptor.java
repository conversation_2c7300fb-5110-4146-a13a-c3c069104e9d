package com.sankuai.medicalcosmetology.offline.code.application.adaptor;

import com.dianping.gis.remote.dto.CityInfoDTO;
import com.dianping.squirrel.client.StoreKey;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.sankuai.medicalcosmetology.offline.code.api.dto.CityBasicInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.RedisKeyConstant;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.cache.RedisCache;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.CityAclService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/12
 * @Description:
 */
@Service
public class CityBasicInfoAdaptor {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private CityAclService cityAclService;

    private final Integer DP_MAX_LIMIT = 1000;

    public List<CityBasicInfoDTO> getAllDpTopLevelCity() {
        StoreKey storeKey = new StoreKey(RedisKeyConstant.CITY_INFO, PlatformEnum.DP.getCode(), "top");
        List<CityBasicInfoDTO> cityBasicInfoDTOList = redisCache.getDataFromRedis(storeKey);

        if (CollectionUtils.isEmpty(cityBasicInfoDTOList)) {
            List<CityInfoDTO> allCityInfoDTOList = Lists.newArrayList();
            int skip = 0;
            List<CityInfoDTO> cityInfoDTOList = cityAclService.getDpInlandCityList(skip, DP_MAX_LIMIT);
            while (CollectionUtils.isNotEmpty(cityInfoDTOList)) {
                allCityInfoDTOList.addAll(cityInfoDTOList);
                skip += DP_MAX_LIMIT;
                cityInfoDTOList = cityAclService.getDpInlandCityList(skip, DP_MAX_LIMIT);
            }

            List<CityInfoDTO> topLevelCityList = allCityInfoDTOList.stream().filter(e -> e.getParentCityId() == 0).collect(Collectors.toList());
            cityBasicInfoDTOList = topLevelCityList.stream().map(e -> buildCityBasicInfoDTO(e))
                    .sorted(Comparator.comparing(CityBasicInfoDTO::getCityPyName)).collect(Collectors.toList());
            redisCache.setDataToRedis(storeKey, cityBasicInfoDTOList);
        }

        return cityBasicInfoDTOList;
    }

    public List<CityBasicInfoDTO> getAllDpOpenCity() {
        StoreKey storeKey = new StoreKey(RedisKeyConstant.CITY_INFO, PlatformEnum.DP.getCode(), "open");
        List<CityBasicInfoDTO> cityBasicInfoDTOList = redisCache.getDataFromRedis(storeKey);

        if (CollectionUtils.isEmpty(cityBasicInfoDTOList)) {
            List<CityInfoDTO> openCityInfoDTOList = Lists.newArrayList();
            int skip = 0;
            List<CityInfoDTO> cityInfoDTOList = cityAclService.getDpOpenCityList(skip, DP_MAX_LIMIT);
            while (CollectionUtils.isNotEmpty(cityInfoDTOList)) {
                openCityInfoDTOList.addAll(cityInfoDTOList);
                skip += DP_MAX_LIMIT;
                cityInfoDTOList = cityAclService.getDpOpenCityList(skip, DP_MAX_LIMIT);
            }

            List<CityInfoDTO> inlandOpenCityList = openCityInfoDTOList.stream().filter(e -> !e.isOverseasCity()).collect(Collectors.toList());
            cityBasicInfoDTOList = inlandOpenCityList.stream().map(e -> buildCityBasicInfoDTO(e))
                    .sorted(Comparator.comparing(CityBasicInfoDTO::getCityPyName)).collect(Collectors.toList());
            redisCache.setDataToRedis(storeKey, cityBasicInfoDTOList);
        }

        return cityBasicInfoDTOList;
    }

    public List<CityBasicInfoDTO> getAllMtTopLevelCity() {
        StoreKey storeKey = new StoreKey(RedisKeyConstant.CITY_INFO, PlatformEnum.MT.getCode(), "top");
        List<CityBasicInfoDTO> cityBasicInfoDTOList = redisCache.getDataFromRedis(storeKey);

        if (CollectionUtils.isEmpty(cityBasicInfoDTOList)) {
            List<CityInfo> topLevelCityList = cityAclService.getMtAllCityList().stream().filter(e -> e.getOriginCityID() == 0).collect(Collectors.toList());
            cityBasicInfoDTOList = topLevelCityList.stream().map(e -> buildCityBasicInfoDTO(e))
                    .sorted(Comparator.comparing(CityBasicInfoDTO::getCityPyName)).collect(Collectors.toList());
            redisCache.setDataToRedis(storeKey, cityBasicInfoDTOList);
        }

        return cityBasicInfoDTOList;
    }

    public List<CityBasicInfoDTO> getAllMtOpenCity() {
        StoreKey storeKey = new StoreKey(RedisKeyConstant.CITY_INFO, PlatformEnum.MT.getCode(), "open");
        List<CityBasicInfoDTO> cityBasicInfoDTOList = redisCache.getDataFromRedis(storeKey);

        if (CollectionUtils.isEmpty(cityBasicInfoDTOList)) {
            List<CityInfo> openCityList = cityAclService.getMtOpenCityList();
            cityBasicInfoDTOList = openCityList.stream().map(e -> buildCityBasicInfoDTO(e))
                    .sorted(Comparator.comparing(CityBasicInfoDTO::getCityPyName)).collect(Collectors.toList());
            redisCache.setDataToRedis(storeKey, cityBasicInfoDTOList);
        }

        return cityBasicInfoDTOList;
    }

    private CityBasicInfoDTO buildCityBasicInfoDTO(CityInfoDTO cityInfoDTO) {
        CityBasicInfoDTO cityBasicInfoDTO = new CityBasicInfoDTO();
        cityBasicInfoDTO.setCityName(cityInfoDTO.getCityName());
        cityBasicInfoDTO.setCityId(cityInfoDTO.getCityId());
        cityBasicInfoDTO.setCityPyName(cityInfoDTO.getCityPyName());
        cityBasicInfoDTO.setCityPyRank(cityInfoDTO.getCityPyName().substring(0, 1).toUpperCase());
        cityBasicInfoDTO.setPlatform(PlatformEnum.DP.getCode());
        return cityBasicInfoDTO;
    }

    private CityBasicInfoDTO buildCityBasicInfoDTO(CityInfo cityInfo) {
        CityBasicInfoDTO cityBasicInfoDTO = new CityBasicInfoDTO();
        cityBasicInfoDTO.setCityName(cityInfo.getName());
        cityBasicInfoDTO.setCityId(cityInfo.getId());
        cityBasicInfoDTO.setCityPyName(cityInfo.getPinyin());
        cityBasicInfoDTO.setCityPyRank(cityInfo.getFirstChar());
        cityBasicInfoDTO.setPlatform(PlatformEnum.MT.getCode());
        return cityBasicInfoDTO;
    }

}
