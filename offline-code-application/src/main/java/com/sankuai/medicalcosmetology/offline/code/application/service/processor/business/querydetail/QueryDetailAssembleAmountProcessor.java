package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querydetail;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.wallet.distribute.platform.dto.AccountInfoDTO;
import com.sankuai.carnation.distribution.wallet.distribute.platform.dto.GetAccountListDTO;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletAccountActivitySourceEnum;
import com.sankuai.dzusergrowth.common.api.enums.DistributionSettleBizLineEnum;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateActivityDetailResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.application.config.ActivityParallelProcessConfig;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.OperationConfigForBApplicationService;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ActivityWalletService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.DistributionSettleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 查询详情页组装返利金额
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, subScene = ProcessorSubSceneConstants.ASSEMBLE_ACTIVITY_PROCESS)
public class QueryDetailAssembleAmountProcessor implements BaseProcessor<QueryDetailProcessorContext> {

    @Resource
    private OperationConfigForBApplicationService operationConfigForBApplicationService;

    @Resource
    private ActivityWalletService activityWalletService;

    @Resource
    @Qualifier("rebateActivityCommonThreadPool")
    private ExecutorService rebateActivityCommonThreadPool;

    @Override
    public void execute(QueryDetailProcessorContext context) {
        Long accountId = context.getAccountId();
        List<RebateActivityRecordDO> rebateActivityRecords = context.getRebateActivityRecords();
        RebateActivityDetailResponse rebateActivityDetailResponse = context.getRebateActivityDetailResponse();
        RebateInfoResponse rebateInfoResponse = new RebateInfoResponse();
        rebateInfoResponse.setRebateAmount(0L);
        rebateInfoResponse.setRemainAmount(0L);
        AtomicBoolean canWithDraw = new AtomicBoolean(true);
        if (CollectionUtils.isNotEmpty(rebateActivityRecords)) {
            //返利金额数查询
            String rebateAmount = operationConfigForBApplicationService.queryRebateAmountByRecords(rebateActivityRecords);
            if (StringUtils.isNotBlank(rebateAmount)) {
                //返回单位为"分"
                rebateInfoResponse.setRebateAmount(new BigDecimal(rebateAmount).multiply(new BigDecimal(100)).longValue());
            }

            //当前账号的报名记录
            List<RebateActivityRecordDO> currentAccountRecordList = rebateActivityRecords.stream().filter(rebateActivityRecordDO -> accountId.equals(rebateActivityRecordDO.getDpAccountId())).collect(Collectors.toList());
            AtomicLong remainAmount = new AtomicLong(0L);

            ActivityParallelProcessConfig lionConfig = Lion.getBean(Environment.getAppName(), LionConstant.ACTIVITY_PARALLEL_PROCESS_CONFIG, ActivityParallelProcessConfig.class, null);
            int batchQueryWalletRemainSize = 100;
            if (lionConfig != null && lionConfig.getBatchQueryWalletRemainSize() != null) {
                batchQueryWalletRemainSize = lionConfig.getBatchQueryWalletRemainSize();
            }
            List<List<RebateActivityRecordDO>> partition = Lists.partition(currentAccountRecordList, batchQueryWalletRemainSize);
            CompletableFuture.allOf(partition.stream().map(onePartition -> CompletableFuture.supplyAsync(() -> {
                List<Long> onePartitionRecordIdList = onePartition.stream().map(RebateActivityRecordDO::getId).collect(Collectors.toList());
                GetAccountListDTO getAccountListDTO = activityWalletService.queryWalletRemainAmount(onePartitionRecordIdList, WalletAccountActivitySourceEnum.MAGIC_COUPON_SHOP_ACTIVITY.getCode());
                if (getAccountListDTO != null && CollectionUtils.isNotEmpty(getAccountListDTO.getAccountInfoList())) {
                    for (AccountInfoDTO accountInfoDTO : getAccountListDTO.getAccountInfoList()) {
                        if (accountInfoDTO.getAmount() != null) {
                            remainAmount.addAndGet(accountInfoDTO.getAmount());
                        }
                        if (accountInfoDTO.getWithdrawStatus() == 0) {
                            canWithDraw.set(false);
                        }
                    }
                }
                return null;
            }, rebateActivityCommonThreadPool)).toArray(CompletableFuture[]::new)).join();
            rebateInfoResponse.setRemainAmount(remainAmount.get());
            canWithDraw.set(canWithDraw.get() && remainAmount.get() > 0);
        } else {
            canWithDraw.set(false);
        }
        rebateActivityDetailResponse.setActivityRebateInfo(rebateInfoResponse);

        rebateActivityDetailResponse.setCanWithDraw(canWithDraw.get());
    }
}
