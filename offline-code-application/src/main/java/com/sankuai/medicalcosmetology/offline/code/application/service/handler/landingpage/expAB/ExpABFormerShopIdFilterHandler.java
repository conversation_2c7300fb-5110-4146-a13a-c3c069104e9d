package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.QueryExpABInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.MagicMemberCouponScenarioDomainEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.ResourceConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.OperationConfigDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/12/19
 * @Description:
 */
@Service
public class ExpABFormerShopIdFilterHandler implements ExpABFormerFilterIface {

    @Autowired
    private ShopMapperService shopMapperService;

    @Autowired
    private OperationConfigDomainService<ResourceConfigDO> operationConfigDomainService;

    @Override
    public boolean support(String key) {
        return "shopId".equals(key);
    }

    @Override
    public boolean queryFilterResult(QueryExpABInfoRequest request, String filterRule) {
        if (StringUtils.isBlank(filterRule)) {
            return false;
        }
        switch (filterRule) {
            case "magicMemberCouponV2":
                Long dpShopId = shopMapperService.mt2dp(request.getShopId());
                List<Long> whiteList = Lion.getList(Environment.getAppName(), LionConstant.MAGIC_FLAG_EXP_AB_WHITE_CONFIG, Long.class);
                if (CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(dpShopId)) {
                    return true;
                }
                String magicFlag = operationConfigDomainService.getMagicMemberCouponSwitch(dpShopId);
                if (!MagicMemberCouponScenarioDomainEnum.NO_LIMIT.getMagicFlag().equals(magicFlag)) {
                    return true;
                }
                return false;
            default:
                return false;
        }
    }
}
