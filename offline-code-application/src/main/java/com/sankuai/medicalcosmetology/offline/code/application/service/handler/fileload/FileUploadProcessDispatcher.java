package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.sankuai.medicalcosmetology.offline.code.application.service.handler.exception.NotSupportSceneException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FileUploadProcessDispatcher<P> {

    @Autowired
    private List<FileUploadProcessHandlerIface> ifaceList;

    public String dispatch(InputStream inputStream, Integer sceneType, P param) throws IOException {
        return findIface(sceneType).executeWithResult(inputStream, param);
    }

    private FileUploadProcessHandlerIface findIface(Integer sceneType) {
        List<FileUploadProcessHandlerIface> hitIfaceList = ifaceList.stream().filter(iface -> iface.support(sceneType))
                .collect(Collectors.toList());
        validateIfaceList(hitIfaceList, sceneType);
        return hitIfaceList.get(0);
    }

    private void validateIfaceList(List<FileUploadProcessHandlerIface> hitIfaceList, Integer sceneType) {
        if (CollectionUtils.isEmpty(hitIfaceList)) {
            throw new NotSupportSceneException("暂未支持该场景文件上传");
        }
    }
}
