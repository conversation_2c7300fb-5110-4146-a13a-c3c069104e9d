package com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.business;

import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.ApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.CancelApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ActivityModuleResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopCheckResultResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateActivityDetailResponse;

import java.util.List;

public interface OperationConfigForBHandler {

    String getActivityType();


    /**
     * 批量报名活动
     *
     * @param request 报名请求
     * @return
     */
    List<Long> batchApplyActivity(ApplyActivityRequest request);

    /**
     * 批量取消报名
     *
     * @param request 取消报名请求
     * @return 取消成功的店铺ID列表
     */
    void batchCancelApply(CancelApplyActivityRequest request);


    /**
     * 查询活动列表
     *
     * @param dpAccountId  账号ID
     * @param dpShopId     店铺ID
     * @param activityType 活动类型
     * @param status       活动状态
     * @return 活动列表
     */
    List<ActivityModuleResponse> queryActivityList(Long dpAccountId, Long dpShopId, int activityType, int status);


    /**
     * 查询可报名活动的店铺信息
     *
     * @param activityViewId 活动ID
     * @param dpAccountId    账号ID
     * @return 店铺信息
     */
    ApplyShopInfoResponse queryApplyShopInfo(String activityViewId, Long dpAccountId);

    /**
     * 校验店铺是否可报名
     *
     * @param applyActivityRequest 报名请求
     * @return 校验结果
     */
    ApplyShopCheckResultResponse checkApplyShop(ApplyActivityRequest applyActivityRequest);

    /**
     * 查询活动详情
     *
     * @param activityViewId 活动ID
     * @return 活动详情
     */
    RebateActivityDetailResponse queryActivityDetail(String activityViewId);

    /**
     * 自动续报神券返利活动
     * @param dpAccountId
     * @param dpShopId
     * @param activityType
     * @return
     */
    Long autoApplyGodCouponRebateActivity(Long dpAccountId, Long dpShopId, Integer activityType);
}
