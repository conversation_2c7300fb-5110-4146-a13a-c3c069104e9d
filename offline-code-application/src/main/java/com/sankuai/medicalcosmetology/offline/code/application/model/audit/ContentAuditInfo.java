package com.sankuai.medicalcosmetology.offline.code.application.model.audit;

import com.sankuai.medicalcosmetology.offline.code.application.model.audit.ContentAuditDetail;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/6/17 10:52
 */
@Data
public class ContentAuditInfo implements Serializable {
    /**
     * 业务方ID
     */
    private int type;

    /**
     * 内容结果产生时间
     */
    private String datetime;

    /**
     * auditFreq = 1，业务送审结果
     * auditFreq = 2，审核修正结果
     */
    private int auditFreq;

    /**
     * 审核结果及明细
     */
    private ContentAuditDetail pair;
}
