package com.sankuai.medicalcosmetology.offline.code.application.converter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.*;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.CouponTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PostPurchaseCouponPageSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.application.constants.PostPurchaseDealChannelSourceConstant;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DateUtil;
import com.sankuai.medicalcosmetology.offline.code.domain.coupon.model.PostpurchaseCouponDetailRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.coupon.model.PostpurchaseCouponRecordDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.producer.message.PostPurchaseCouponPersonaMessage;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: chenhaoyang02
 * @Date: 2025/3/18
 * @Description:
 */
public class PostPurchaseCouponConverter {

    public static QueryCouponResultInfo convert2QueryCouponResultInfo(Integer pageSource, Integer qrClientType, OperationInfoDTO operationInfoDTO,
                                                                      List<PostpurchaseCouponDetailRecordDO> detailRecordDOList, List<PostpurchaseCouponDetailRecordDO> validCouponDetailRecordDOList,
                                                                      Map<String, String> extMap) {
        QueryCouponResultInfo queryCouponResultInfo = new QueryCouponResultInfo();
        CouponGroupCardDTO couponGroupCardDTO = new CouponGroupCardDTO();

        // 根据页面来源处理逻辑
        PostPurchaseCouponPageSourceEnum pageSourceEnum = PostPurchaseCouponPageSourceEnum.fromCode(pageSource);
        switch (pageSourceEnum) {
            case ORDER_DETAIL_PAGE:
            case ORDER_VERIFY_PAGE:
                handleOrderPage(couponGroupCardDTO, operationInfoDTO, pageSource, qrClientType);
                break;
            case HOMEPAGE:
                handleHomePage(couponGroupCardDTO, detailRecordDOList, validCouponDetailRecordDOList, pageSource, qrClientType);
                break;
            case GROUP_CHANNEL:
                handleGroupChannel(couponGroupCardDTO, validCouponDetailRecordDOList, extMap, queryCouponResultInfo);
                break;
            default:
                // 如果没有匹配的页面来源，直接返回空结果
                return queryCouponResultInfo;
        }

        queryCouponResultInfo.setCouponGroupCardDTO(couponGroupCardDTO);
        return queryCouponResultInfo;
    }

    private static List<PostpurchaseCouponDetailRecordDO> filterValidCoupons(List<PostpurchaseCouponDetailRecordDO> detailRecordDOList) {
        return detailRecordDOList.stream()
                .filter(record -> record.getCouponStatus().equals(0))
                .filter(record -> record.getEndUseTime().after(new Date()))
                .collect(Collectors.toList());
    }

    private static void handleOrderPage(CouponGroupCardDTO couponGroupCardDTO, OperationInfoDTO operationInfoDTO, Integer pageSource, Integer qrClientType) {
        String imgUrl = operationInfoDTO.getResourceConfig().getBannerPic().getUrl();
        ButtonContent buttonContent = ButtonContent.builder().textContentList(
                        Lists.newArrayList(TextContent.builder().text("去使用").build()))
                .imgUrl(imgUrl)
                .jumpUrl(getDealChannelJumpUrl(pageSource, qrClientType)).build();

        couponGroupCardDTO.setButtonContentList(Lists.newArrayList(buttonContent));
        Map<String, String> extMapStr = Maps.newHashMap();
        extMapStr.put("priorToWeCom", (boolean) operationInfoDTO.getExtInfo().get("priorToWeCom") ? "1" : "0");
        couponGroupCardDTO.setExtMapStr(JSON.toJSONString(extMapStr));
    }

    private static void handleHomePage(CouponGroupCardDTO couponGroupCardDTO, List<PostpurchaseCouponDetailRecordDO> detailRecordDOList,
                                       List<PostpurchaseCouponDetailRecordDO> validCouponDetailRecordDOList, Integer pageSource, Integer qrClientType) {
        couponGroupCardDTO.setTitle(TextContent.builder().text("到店团购专享").build());
        couponGroupCardDTO.setSubTitleList(Lists.newArrayList(
                TextContent.builder().text("聚会玩乐1折起").build(),
                TextContent.builder().text("按摩洗浴1折起").build(),
                TextContent.builder().text("亲子出游1折起").build(),
                TextContent.builder().text("美容美发1折起").build()
        ));

        String totalAmount = calculateTotalAmount(detailRecordDOList);
        couponGroupCardDTO.setTotalAmount(totalAmount);
        couponGroupCardDTO.setCouponTagImage("https://p0.meituan.net/ingee/3d7e477b748698f269b03880d648f27d1750.png");
        couponGroupCardDTO.setBackgroundImage("https://p0.meituan.net/ingee/84a611f9e9422e39eb5d562d11dff0442846.png");
        couponGroupCardDTO.setCouponTitle("生活玩乐通用券");
        couponGroupCardDTO.setLatestExpireTime(DateUtil.getEarliestDate(validCouponDetailRecordDOList.stream()
                .map(PostpurchaseCouponDetailRecordDO::getEndUseTime).collect(Collectors.toList())));

        ButtonContent buttonContent = ButtonContent.builder().textContentList(
                        Lists.newArrayList(TextContent.builder().text("去使用").build()))
                .jumpUrl(getDealChannelJumpUrl(pageSource, qrClientType)).build();
        couponGroupCardDTO.setButtonContentList(Lists.newArrayList(buttonContent));
        couponGroupCardDTO.setEffectiveTime(DateUtil.getEarliestDate(detailRecordDOList.stream()
                .map(PostpurchaseCouponDetailRecordDO::getBeginUseTime).collect(Collectors.toList())).getTime() / 1000);
    }

    private static void handleGroupChannel(CouponGroupCardDTO couponGroupCardDTO, List<PostpurchaseCouponDetailRecordDO> validCouponDetailRecordDOList,
                                           Map<String, String> extMap, QueryCouponResultInfo queryCouponResultInfo) {
        String remainToUseAmount = calculateTotalAmount(validCouponDetailRecordDOList);
        couponGroupCardDTO.setRemainToUseAmount(remainToUseAmount);
        couponGroupCardDTO.setLatestExpireTime(DateUtil.getEarliestDate(validCouponDetailRecordDOList.stream()
                .map(PostpurchaseCouponDetailRecordDO::getEndUseTime).collect(Collectors.toList())));
        couponGroupCardDTO.setExtMap(extMap);
        queryCouponResultInfo.setCouponList(convertAndSortCouponList(validCouponDetailRecordDOList));
    }

    private static String calculateTotalAmount(List<PostpurchaseCouponDetailRecordDO> detailRecordDOList) {
        return detailRecordDOList.stream()
                .filter(record -> record.getCouponAmount() != null)
                .map(PostpurchaseCouponDetailRecordDO::getCouponAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .toString();
    }

    private static String getDealChannelJumpUrl(Integer pageSource, Integer qrClientType) {
        switch (QRClientType.getByCode(qrClientType)) {
            case MT_APP:
                if (PostPurchaseCouponPageSourceEnum.fromCode(pageSource).equals(PostPurchaseCouponPageSourceEnum.HOMEPAGE)) {
                    return String.format(PostPurchaseDealChannelSourceConstant.DEAL_CHANNEL_URL_MT_APP, PostPurchaseDealChannelSourceConstant.HOMEPAGE_MT_APP);
                }
                if (PostPurchaseCouponPageSourceEnum.fromCode(pageSource).equals(PostPurchaseCouponPageSourceEnum.ORDER_DETAIL_PAGE) ||
                        PostPurchaseCouponPageSourceEnum.fromCode(pageSource).equals(PostPurchaseCouponPageSourceEnum.ORDER_VERIFY_PAGE)) {
                    return String.format(PostPurchaseDealChannelSourceConstant.DEAL_CHANNEL_URL_MT_APP, PostPurchaseDealChannelSourceConstant.ORDER_MT_APP);
                }
                break;
            case MT_WE_CHAT_APPLET:
                if (PostPurchaseCouponPageSourceEnum.fromCode(pageSource).equals(PostPurchaseCouponPageSourceEnum.HOMEPAGE)) {
                    return String.format(PostPurchaseDealChannelSourceConstant.DEAL_CHANNEL_URL_MT_WECHAT_APPLET, PostPurchaseDealChannelSourceConstant.HOMEPAGE_MT_WECHAT_APPLET);
                }
                if (PostPurchaseCouponPageSourceEnum.fromCode(pageSource).equals(PostPurchaseCouponPageSourceEnum.ORDER_DETAIL_PAGE) ||
                        PostPurchaseCouponPageSourceEnum.fromCode(pageSource).equals(PostPurchaseCouponPageSourceEnum.ORDER_VERIFY_PAGE)) {
                    return String.format(PostPurchaseDealChannelSourceConstant.DEAL_CHANNEL_URL_MT_WECHAT_APPLET, PostPurchaseDealChannelSourceConstant.ORDER_MT_WECHAT_APPLET);
                }
                break;
        }
        return "";
    }

    private static List<ScanCouponInfo> convertAndSortCouponList(List<PostpurchaseCouponDetailRecordDO> detailRecordDOList) {
        List<ScanCouponInfo> scanCouponInfoList = detailRecordDOList.stream()
                .filter(Objects::nonNull)
                .map(record -> convert2ScanCouponInfo(record)).collect(Collectors.toList());

        Comparator<ScanCouponInfo> comparator = Comparator
                // 首先按使用门槛由低到高排序
                .comparing(ScanCouponInfo::getLimitAmount, Comparator.comparing(BigDecimal::new))
                // 最后按amount降序排序
                .thenComparing(ScanCouponInfo::getAmount, (s1, s2) -> new BigDecimal(s2).compareTo(new BigDecimal(s1)));

        scanCouponInfoList.sort(comparator);
        return scanCouponInfoList;
    }

    private static ScanCouponInfo convert2ScanCouponInfo(PostpurchaseCouponDetailRecordDO postpurchaseCouponDetailRecordDO) {
        if (postpurchaseCouponDetailRecordDO == null) {
            return null;
        }
        ScanCouponInfo scanCouponInfo = new ScanCouponInfo();
        scanCouponInfo.setAmount(postpurchaseCouponDetailRecordDO.getCouponAmount().stripTrailingZeros().toPlainString());
        scanCouponInfo.setLimitAmount(postpurchaseCouponDetailRecordDO.getCouponLimitAmount().stripTrailingZeros().toPlainString());
        scanCouponInfo.setTitle(postpurchaseCouponDetailRecordDO.getCouponTitle());
        scanCouponInfo.setDesc(postpurchaseCouponDetailRecordDO.getCouponDesc());
        scanCouponInfo.setStartTime(postpurchaseCouponDetailRecordDO.getBeginUseTime());
        scanCouponInfo.setEndTime(postpurchaseCouponDetailRecordDO.getEndUseTime());
        scanCouponInfo.setUnifiedCouponId(postpurchaseCouponDetailRecordDO.getUnifiedCouponId());
        scanCouponInfo.setUnifiedCouponGroupId(postpurchaseCouponDetailRecordDO.getUnifiedCouponGroupId());
        scanCouponInfo.setCouponType(CouponTypeEnum.OFFLINE_CODE_POST_PURCHASE.getCode());
        scanCouponInfo.setApplicableText("部分商品适用");
        return scanCouponInfo;
    }

    public static PostPurchaseCouponPersonaMessage convert2Message(PostpurchaseCouponRecordDO recordDO) {
        if (recordDO == null) {
            return null;
        }
        return PostPurchaseCouponPersonaMessage.builder()
                .userId(recordDO.getUserId())
                .platform(recordDO.getPlatform())
                .couponAllUnvalid(recordDO.getCouponAllUnvalid())
                .rePurchaseOrderNum(recordDO.getRePurchaseOrderNum())
                .endUseTime(recordDO.getEndUseTime()).build();
    }
}
