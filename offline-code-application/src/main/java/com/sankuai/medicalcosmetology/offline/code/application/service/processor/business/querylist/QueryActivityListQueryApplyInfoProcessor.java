package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querylist;

import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.*;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.model.DpAccountBaseInfo;
import com.sankuai.medicalcosmetology.offline.code.application.service.OperationConfigForBApplicationService;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DpAccountBaseInfoCacheUtils;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PoiAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 查询活动列表-查询店铺报名信息
 */
@Slf4j
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, subScene = ProcessorSubSceneConstants.QUERY_APPLY_SHOP)
public class QueryActivityListQueryApplyInfoProcessor implements BaseProcessor<QueryActivityListProcessorContext> {

    @Resource
    private OperationConfigForBApplicationService operationConfigForBApplicationService;

    @Resource
    private PoiAclService poiAclService;

    @Resource
    @Qualifier("rebateActivityCommonThreadPool")
    private ExecutorService rebateActivityCommonThreadPool;

    @Override
    public void execute(QueryActivityListProcessorContext context) {
        List<RebateActivityConfigDO> rebateActivityConfigDOList = context.getRebateActivityConfigDOList();
        Integer activityType = context.getActivityType();
        Long currentDpAccountId = context.getDpAccountId();
        Long currentDpShopId = context.getDpShopId();

        AtomicBoolean queryApplyShopInfoResult = new AtomicBoolean(true);
        List<CompletableFuture<ActivityModuleResponse>> completableFutureList = rebateActivityConfigDOList.stream().map(rebateActivityConfigDO -> CompletableFuture.supplyAsync(() -> {
            try {
                String activityViewId = ResourceEncoder.encodeResourceId(rebateActivityConfigDO.getId());
                //查询当前活动是否有门店可报名
                ApplyShopInfoResponse applyShopInfoResponse = operationConfigForBApplicationService.queryApplyShopInfo(activityType, activityViewId, currentDpAccountId);
                Map<String, ApplyShopInfoResponse> applyShopInfoMap = context.getApplyShopInfoMap();
                applyShopInfoMap.put(activityViewId, applyShopInfoResponse);
                Map<String, List<ShopInfoResponse>> appliableShopMap = applyShopInfoResponse.getAppliableShopMap();
                //如果有可报名店铺 或者已报名店铺，代表此活动对当前账号可见，组装活动信息
                if (MapUtils.isNotEmpty(appliableShopMap) || MapUtils.isNotEmpty(applyShopInfoResponse.getAppliedShopMap())) {

                    ActivityModuleResponse activityModuleResponse = new ActivityModuleResponse();
                    activityModuleResponse.setId(activityViewId);
                    activityModuleResponse.setActivityStatus(context.getStatus());
                    activityModuleResponse.setStartTime(rebateActivityConfigDO.getStartTime().getTime());
                    activityModuleResponse.setEndTime(rebateActivityConfigDO.getEndTime().getTime());
                    activityModuleResponse.setTitle(rebateActivityConfigDO.getOperationName());
                    activityModuleResponse.setOrder(rebateActivityConfigDO.getOrder());
                    activityModuleResponse.setDescription(rebateActivityConfigDO.getDescription());
                    activityModuleResponse.setRealStatus(rebateActivityConfigDO.getStatus());

                    List<Long> appliableShopIdList = appliableShopMap.values().stream().flatMap(List::stream).map(ShopInfoResponse::getDpShopIdLong).collect(Collectors.toList());
                    activityModuleResponse.setAppliableShopIdList(appliableShopIdList);

                    Map<String, List<ShopInfoResponse>> appliedShopMap = applyShopInfoResponse.getAppliedShopMap();
                    //查询已报名门店信息和账号信息
                    if (MapUtils.isNotEmpty(appliedShopMap)) {
                        List<ShopInfoResponse> appliedShopInfoResponseList = appliedShopMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
                        List<Long> appliedDpShopIdList = appliedShopInfoResponseList.stream().map(ShopInfoResponse::getDpShopIdLong).collect(Collectors.toList());

                        //查询店铺信息
                        Map<Long, String> shopNameMap;
                        DpAccountBaseInfo dpAccountBaseInfo = DpAccountBaseInfoCacheUtils.getDpAccountBaseInfo(currentDpAccountId);
                        if (dpAccountBaseInfo != null && MapUtils.isNotEmpty(dpAccountBaseInfo.getShopFullNameMap())) {
                            shopNameMap = dpAccountBaseInfo.getShopFullNameMap();
                        } else {
                            shopNameMap = poiAclService.batchGetPoiFullNameByDpShopId(appliedDpShopIdList);
                        }

                        List<AppliedInfoResponse> appliedInfo = new ArrayList<>();
                        //key:accountId, value:报名的店铺
                        Map<Long, List<AppliedShopInfoResponse>> appliedAccountShopMap = new HashMap<>();
                        for (ShopInfoResponse shopInfo : appliedShopInfoResponseList) {
                            AppliedShopInfoResponse appliedShopInfoResponse = new AppliedShopInfoResponse();
                            appliedShopInfoResponse.setActivityRecordViewId(activityViewId);
                            appliedShopInfoResponse.setDpShopId(shopInfo.getDpShopIdLong());
                            appliedShopInfoResponse.setShopName(shopNameMap.get(shopInfo.getDpShopIdLong()));
                            appliedShopInfoResponse.setDpAccountId(shopInfo.getDpAccountId());
                            if (appliedAccountShopMap.containsKey(shopInfo.getDpAccountId())) {
                                appliedAccountShopMap.get(shopInfo.getDpAccountId()).add(appliedShopInfoResponse);
                            } else {
                                appliedAccountShopMap.put(shopInfo.getDpAccountId(), Lists.newArrayList(appliedShopInfoResponse));
                            }
                        }
                        appliedAccountShopMap.forEach((dpAccountId, appliedShopInfoDTOList) -> {
                            AppliedInfoResponse appliedInfoResponse = new AppliedInfoResponse();
                            appliedInfoResponse.setApplyShops(appliedShopInfoDTOList);
                            appliedInfo.add(appliedInfoResponse);
                        });
                        ApplyInfoResponse applyInfoResponse = new ApplyInfoResponse();
                        applyInfoResponse.setShowApplyInfo(CollectionUtils.isNotEmpty(appliedInfo));
                        applyInfoResponse.setAppliedInfo(appliedInfo);
                        activityModuleResponse.setApplyInfo(applyInfoResponse);
                    }
                    Integer hideDetailButton = calculateHideDetailButton(currentDpAccountId, currentDpShopId, activityModuleResponse);
                    activityModuleResponse.setShowButton(hideDetailButton);
                    return activityModuleResponse;
                }
                return null;
            } catch (Exception e) {
                queryApplyShopInfoResult.set(false);
                log.error("QueryActivityListQueryApplyInfoProcessor_asyncQueryFailed,activityId:{},dpAccountId:{}", rebateActivityConfigDO.getId(), currentDpAccountId, e);
                return null;
            }
        }, rebateActivityCommonThreadPool)).collect(Collectors.toList());

        List<ActivityModuleResponse> canApplyActivityList = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).thenApply(val -> completableFutureList.stream().map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList())).join();
        AssertUtil.isTrue(queryApplyShopInfoResult.get(), "查询活动报名信息失败，请稍后重试");
        context.setActivityModuleList(canApplyActivityList);
    }

    private Integer calculateHideDetailButton(Long accountId, Long dpShopId, ActivityModuleResponse activityDTO) {
        if (activityDTO != null && activityDTO.getApplyInfo() != null
                && CollectionUtils.isNotEmpty(activityDTO.getApplyInfo().getAppliedInfo())) {
            for (AppliedInfoResponse appliedInfo : activityDTO.getApplyInfo().getAppliedInfo()) {
                if (CollectionUtils.isNotEmpty(appliedInfo.getApplyShops())) {
                    for (AppliedShopInfoResponse appliedShopInfoResponse : appliedInfo.getApplyShops()) {
                        if (appliedShopInfoResponse.getDpShopId().equals(dpShopId) && accountId.equals(appliedShopInfoResponse.getDpAccountId())) {
                            return 0;
                        }
                    }
                }
            }
        }
        return 1;
    }

}
