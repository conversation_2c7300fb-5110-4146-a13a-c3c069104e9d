package com.sankuai.medicalcosmetology.offline.code.application.config;

import lombok.Data;

/**
 * 活动并行处理配置
 */
@Data
public class ActivityParallelProcessConfig {

    /**
     * 查询bu分片大小
     */
    private Integer shopPartitionSizeWithBathQueryBu;

    /**
     * 查询门店分片大小
     */
    private Integer shopPartitionSizeWithBatchQueryPoi;

    /**
     * 批量查询钱包余额分片大小
     */
    private Integer batchQueryWalletRemainSize;

    /**
     * 批量查询账单分片大小
     */
    private Integer batchQueryBillSize;
}
