package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.sankuai.medicalcosmetology.offline.code.api.dto.TitleAndDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryModel implements Serializable {
    private static final long serialVersionUID = 4415439681957L;

    private List<TitleAndDataDTO> data;

    private int status;
}
