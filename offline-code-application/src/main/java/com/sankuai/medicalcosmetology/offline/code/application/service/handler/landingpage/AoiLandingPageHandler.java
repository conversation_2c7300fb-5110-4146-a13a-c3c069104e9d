package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.application.service.PromoCodeLandingPageUrlService;
import com.sankuai.medicalcosmetology.offline.code.application.utils.LandingPageSwitchFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/8
 * @Description:
 */
@Service
@Slf4j
public class AoiLandingPageHandler implements PromoCodeLandingPageIface {

    @Autowired
    private LandingPageSwitchFilter landingPageSwitchFilter;

    @Autowired
    private PromoCodeLandingPageUrlService promoCodeLandingPageUrlService;

    public static final String DEFAULT_MT_PREFIX = "imeituan://www.meituan.com/web?notitlebar=1&_page_new=1&_speed_mode=1&url=";
    public static final String DEFAULT_WX_PREFIX = "/wp-h5-pages/pages/h5/index?f_ci=1&f_token=1&f_openIdCipher=1&f_openId=1&f_finger=1&f_fingerg=1&app_version=1&f_pos=1&f_systemInfo=1&f_marsPos=1&f_localCi=1&f_userId=1&weburl=";
    public static final String DEFAULT_WX_H5_URL = "https://awp.meituan.com/snfe/msc-life-circle/pages/index/index?subSource=tuanActivity&activitySource=dzLive&activityPoiid=%s&hostEnv=wechat&future=2";
    public static final String DEFAULT_MT_H5_URL = "https://awp.meituan.com/snfe/msc-life-circle/pages/index/index?subSource=tuanActivity&activitySource=dzLive&activityPoiid=%s&hostEnv=group&future=2";

    /**
     * poiid字段是否需要加密
     */
    private boolean needEncrypt = true;

    @Override
    public boolean support(PromoCodeLandingPageRequest request) {
        return request.getSource().equals(LandingPageSourceEnum.AOI_EMPTY.getCode());
    }

    @Override
    public String queryPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        String url = "";
        String mtUrlPrefix = DEFAULT_MT_PREFIX;
        String wxUrlPrefix = DEFAULT_WX_PREFIX;
        String mtH5Url = DEFAULT_MT_H5_URL;
        String wxH5Url = DEFAULT_WX_H5_URL;
        Long poiId = request.getPoiId();
        if (poiId == null || request.getCodeType() == null) {
            return url;
        }
        Map<String, String> pageUrlMap = Lion.getMap(Environment.getAppName(), LionConstant.AOI_LANDING_URL, String.class);
        if (MapUtils.isNotEmpty(pageUrlMap)) {
            mtUrlPrefix = pageUrlMap.getOrDefault("mt-prefix", DEFAULT_MT_PREFIX);
            wxUrlPrefix = pageUrlMap.getOrDefault("wx-prefix", DEFAULT_WX_PREFIX);
            mtH5Url = pageUrlMap.getOrDefault("mt-h5-url", DEFAULT_MT_H5_URL);
            wxH5Url = pageUrlMap.getOrDefault("wx-h5-url", DEFAULT_WX_H5_URL);
        }
        QRClientType qrClientType = QRClientType.getByCode(request.getQrClientType());

        try {
            if (qrClientType != null) {
                switch (qrClientType) {
                    case MT_APP:
                        url = mtUrlPrefix + URLEncoder.encode(String.format(mtH5Url, poiId), "UTF-8");
                        break;
                    case MT_WE_CHAT_APPLET:
                        url = wxUrlPrefix + URLEncoder.encode(String.format(wxH5Url, poiId), "UTF-8");
                        break;
                    default:
                        url = "https://cube.dianping.com/cube/block/00b0776bd631/332249/index.html";
                        break;
                }
            }
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".queryPromoCodeLandingPageUrl error, request is {}, exception is", request, e);
        }
        return url;
    }

    @Override
    public PromoCodeLandingUrlDTO queryAllPlatformPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        PromoCodeLandingUrlDTO promoCodeLandingUrlDTO = new PromoCodeLandingUrlDTO();

        String mtUrlPrefix = DEFAULT_MT_PREFIX;
        String wxUrlPrefix = DEFAULT_WX_PREFIX;
        String mtH5Url = DEFAULT_MT_H5_URL;
        String wxH5Url = DEFAULT_WX_H5_URL;
        Long poiId = request.getPoiId();

        Map<String, String> pageUrlMap = Lion.getMap(Environment.getAppName(), LionConstant.AOI_LANDING_URL, String.class);
        if (MapUtils.isNotEmpty(pageUrlMap)) {
            mtUrlPrefix = pageUrlMap.getOrDefault("mt-prefix", DEFAULT_MT_PREFIX);
            wxUrlPrefix = pageUrlMap.getOrDefault("wx-prefix", DEFAULT_WX_PREFIX);
            mtH5Url = pageUrlMap.getOrDefault("mt-h5-url", DEFAULT_MT_H5_URL);
            wxH5Url = pageUrlMap.getOrDefault("wx-h5-url", DEFAULT_WX_H5_URL);
        }

        try {
            String mtUrl = mtUrlPrefix + URLEncoder.encode(String.format(mtH5Url, poiId), "UTF-8");
            String wxUrl = wxUrlPrefix + URLEncoder.encode(String.format(wxH5Url, poiId), "UTF-8");
            promoCodeLandingUrlDTO.setMtLandingUrl(mtUrl);
            promoCodeLandingUrlDTO.setWxLandingUrl(wxUrl);
        }
        catch (Exception e) {
            log.error(getClass().getSimpleName() + ".queryAllPlatformPromoCodeLandingPageUrl error, request is {}, exception is", request, e);
        }

        return promoCodeLandingUrlDTO;
    }
}
