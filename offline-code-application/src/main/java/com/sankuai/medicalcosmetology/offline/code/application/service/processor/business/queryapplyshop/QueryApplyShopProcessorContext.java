package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.queryapplyshop;

import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.ProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 查询活动报名门店处理器上下文
 */
@Data
public class QueryApplyShopProcessorContext implements ProcessorContext {

    /**
     * 活动加密id
     */
    private String activityViewId;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 点评账号id
     */
    private Long dpAccountId;

    /**
     * 查询结果
     */
    private ApplyShopInfoResponse applyShopInfoResponse;

    /**
     * 门店信息,key:门店id，value：门店信息
     */
    private Map<Long, DpPoiDTO> poiDTOMap;

    /**
     * 报名记录
     */
    private List<RebateActivityRecordDO> rebateActivityRecordDOList;

    /**
     * 账号关联的店铺id
     */
    private List<Long> dpShopListByAccount;

    /**
     * 活动
     */
    private RebateActivityConfigDO rebateActivityConfigDO;
}
