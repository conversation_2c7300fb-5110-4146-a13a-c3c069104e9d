package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.lion.Environment;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.dto.EmptyCodeBindInfoDTO;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeBindKeyTypeEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeBindStatusEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeBizTypeEnum;
import com.sankuai.carnation.distribution.empty.code.prebind.enums.EmptyCodeSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.fileload.FileLoadSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.application.model.sso.User;
import com.sankuai.medicalcosmetology.offline.code.application.utils.DateUtil;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ExcelUtil;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ElephantPushAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.EmptyCodeAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.QRCodeAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.wrapper.S3Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/1/23
 * @Description:
 */
@Slf4j
@Component
public class EmptyCodeBindStatusExportHandler implements FileUploadProcessHandlerIface<String, User> {

    private static final ThreadPoolExecutor executorService = Rhino.newThreadPool("bindStatusExport", DefaultThreadPoolProperties.Setter()
            .withCoreSize(5).withMaxSize(10).withBlockingQueue(new LinkedBlockingQueue<>(100))).getExecutor();

    @Resource
    private EmptyCodeAclService emptyCodeAclService;

    @Resource
    private S3Wrapper s3Wrapper;

    @Resource
    private ElephantPushAclService elephantPushAclService;

    @Resource
    private QRCodeAclService qrCodeAclService;

    private static final String EMPTY_CODE_KEY_COLUMN = "空码key值";

    private static final Integer LAST_RAW_MAX_NUM = 5001;

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    @Override
    public boolean support(Integer sceneType) {
        return FileLoadSceneEnum.MATERIAL_EMPTYCODE_BIND_STATUS.getType().equals(sceneType);
    }

    /**
     * 执行文件上传处理并返回结果
     *
     * @param inputStream
     * @param user
     * @return 处理结果的字符串表示
     */
    @Override
    public String executeWithResult(InputStream inputStream, User user) throws IOException {
        List<String> fileData = readFileAsBean(inputStream, user);
        if (CollectionUtils.isEmpty(fileData)) {
            return "解析数据为空";
        }
        executorService.submit(() -> processData(fileData, user));
        return String.format("成功解析%d条数据，对应绑定状态稍后将以大象通知发出，请注意查收", fileData.size());
    }

    /**
     * 将文件项列表读取为对象列表
     *
     * @param inputStream
     * @param user
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    @Override
    public List<String> readFileAsBean(InputStream inputStream, User user) throws IOException {
        validateInputs(inputStream, user);
        Workbook workbook = createAndValidateWorkbook(inputStream);
        Sheet sheet = getAndValidateFirstSheet(workbook);
        validateRowCount(sheet);

        int keyColumnIndex = findKeyColumnIndex(sheet);
        return extractKeyValues(sheet, keyColumnIndex);
    }

    private void validateInputs(InputStream inputStream, User user) throws IOException {
        if (inputStream.available() == 0 || user == null) {
            throw new IllegalArgumentException("InputStream or User is null or empty.");
        }
    }

    private Workbook createAndValidateWorkbook(InputStream inputStream) throws IOException {
        Workbook workbook = WorkbookFactory.create(inputStream);
        if (workbook == null) {
            throw new IllegalArgumentException("Workbook is null.");
        }
        return workbook;
    }

    private Sheet getAndValidateFirstSheet(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet == null) {
            throw new IllegalArgumentException("Sheet is null.");
        }
        return sheet;
    }

    private void validateRowCount(Sheet sheet) {
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum >= LAST_RAW_MAX_NUM) {
            throw new IllegalArgumentException(String.format("上传条数超过最大值:%d条", LAST_RAW_MAX_NUM - 1));
        }
    }

    private int findKeyColumnIndex(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            throw new IllegalArgumentException("表头行不能为空");
        }

        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null && EMPTY_CODE_KEY_COLUMN.equals(cell.getStringCellValue())) {
                return i;
            }
        }

        throw new IllegalArgumentException("未找到" + EMPTY_CODE_KEY_COLUMN + "列");
    }

    private List<String> extractKeyValues(Sheet sheet, int keyColumnIndex) {
        List<String> keyValues = new ArrayList<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                Cell cell = row.getCell(keyColumnIndex);
                if (cell == null || StringUtils.isBlank(cell.getStringCellValue())) {
                    throw new IllegalArgumentException(String.format("%d行 '%s'不能为空", i + 1, EMPTY_CODE_KEY_COLUMN));
                }
                keyValues.add(cell.getStringCellValue());
            }
        }

        return keyValues;
    }

    /**
     * 处理解析后的数据对象列表
     *
     * @param modelList 解析后的数据对象列表，每个对象代表一个文件的内容
     * @param user
     */
    @Override
    public void processData(List<String> modelList, User user) {
        if (CollectionUtils.isEmpty(modelList)) {
            return;
        }
        Map<String, EmptyCodeBindInfoDTO> emptyCodeInfoMap = new HashMap<>();
        for (String key : modelList) {
            EmptyCodeBindInfoDTO emptyCodeBindInfoDTO = emptyCodeAclService
                    .queryBindInfo(EmptyCodeSourceEnum.OFFLINE_CODE.getCode(), key);
            if (emptyCodeBindInfoDTO == null) {
                EmptyCodeBindInfoDTO aoiCodeBindInfoDTO = emptyCodeAclService
                        .queryBindInfo(EmptyCodeSourceEnum.AOI_CODE.getCode(), key);
                if (aoiCodeBindInfoDTO != null) {
                    emptyCodeInfoMap.put(key, aoiCodeBindInfoDTO);
                }
                continue;
            }
            emptyCodeInfoMap.put(key, emptyCodeBindInfoDTO);
        }
        String userId = user.getLogin();
        String fileName = generateFileName();
        File file = generateMultiSheetExcelFile(modelList, emptyCodeInfoMap, fileName);
        uploadAndNotify(file, Collections.singletonList(userId));
    }

    private File generateMultiSheetExcelFile(List<String> modelList, Map<String, EmptyCodeBindInfoDTO> emptyCodeInfoMap,
            String fileName) {
        List<ExcelUtil.SheetData> sheetDataList = new ArrayList<>();
        ExcelUtil.SheetData shopCodeSheet = createShopCodeSheet(modelList, emptyCodeInfoMap);
        if (CollectionUtils.isNotEmpty(shopCodeSheet.getDataList())) {
            sheetDataList.add(shopCodeSheet);
        }
        ExcelUtil.SheetData staffCodeSheet = createStaffCodeSheet(modelList, emptyCodeInfoMap);
        if (CollectionUtils.isNotEmpty(staffCodeSheet.getDataList())) {
            sheetDataList.add(staffCodeSheet);
        }
        ExcelUtil.SheetData goodsCodeSheet = createGoodsCodeSheet(modelList, emptyCodeInfoMap);
        if (CollectionUtils.isNotEmpty(goodsCodeSheet.getDataList())) {
            sheetDataList.add(goodsCodeSheet);
        }
        ExcelUtil.SheetData unboundSheet = createUnboundSheet(modelList, emptyCodeInfoMap);
        if (CollectionUtils.isNotEmpty(unboundSheet.getDataList())) {
            sheetDataList.add(unboundSheet);
        }
        ExcelUtil.SheetData aoiCodeSheet = createAoiCodeSheet(modelList, emptyCodeInfoMap);
        if (CollectionUtils.isNotEmpty(aoiCodeSheet.getDataList())) {
            sheetDataList.add(aoiCodeSheet);
        }
        return ExcelUtil.generateMultiSheetExcelFile(sheetDataList, fileName, "xlsx");
    }

    private ExcelUtil.SheetData createShopCodeSheet(List<String> modelList,
            Map<String, EmptyCodeBindInfoDTO> emptyCodeInfoMap) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("no", "编号");
        headers.put("url", "链接");
        headers.put("key", "空码key值");
        headers.put("sourceUrl", "源码链接");
        headers.put("bindType", "绑定类型");
        headers.put("dpShopId", "dpShopId");
        headers.put("bindTime", "绑定时间");

        List<Map<String, Object>> dataList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);

        for (String key : modelList) {
            EmptyCodeBindInfoDTO info = emptyCodeInfoMap.get(key);
            if (info != null && EmptyCodeBindKeyTypeEnum.SHOP_CODE.getCode() == info.getBindKeyType()) {
                Map<String, Object> data = new HashMap<>();
                data.put("no", index.getAndIncrement());
                data.put("url", info.getCodeImage());
                data.put("key", key);
                data.put("sourceUrl", info.getCodeLink());
                data.put("bindType", "门店码");
                data.put("dpShopId", info.getBindKey());
                data.put("bindTime", DateUtil.format(info.getUpdateTime(), "yyyy年MM月dd日"));
                dataList.add(data);
            }
        }
        return ExcelUtil.SheetData.builder().sheetName("门店码").headerAlias(headers).dataList(dataList).build();
    }

    private ExcelUtil.SheetData createStaffCodeSheet(List<String> modelList,
            Map<String, EmptyCodeBindInfoDTO> emptyCodeInfoMap) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("no", "编号");
        headers.put("url", "链接");
        headers.put("key", "空码key值");
        headers.put("sourceUrl", "源码链接");
        headers.put("bindType", "绑定类型");
        headers.put("dpShopId", "dpShopId");
        headers.put("staffName", "职人姓名");
        headers.put("staffId", "职人id");
        headers.put("bindTime", "绑定时间");

        List<Map<String, Object>> dataList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);

        for (String key : modelList) {
            EmptyCodeBindInfoDTO info = emptyCodeInfoMap.get(key);
            if (info != null && EmptyCodeBindKeyTypeEnum.STAFF_CODE.getCode() == info.getBindKeyType()) {
                Map<String, Object> data = new HashMap<>();
                data.put("no", index.getAndIncrement());
                data.put("url", info.getCodeImage());
                data.put("key", key);
                data.put("sourceUrl", getSourceUrlByKey(key));
                data.put("bindType", "职人码");
                String[] bindKeyParts = info.getBindKey().split("_");
                if (bindKeyParts.length == 2) {
                    data.put("dpShopId", bindKeyParts[0]);
                    StaffCodeDTO staffCodeDTO = qrCodeAclService.queryStaffCodeByCode(bindKeyParts[1]);
                    if (staffCodeDTO != null) {
                        data.put("staffName", staffCodeDTO.getName());
                        data.put("staffId", staffCodeDTO.getTechnicianId());
                    }
                }
                data.put("bindTime", DateUtil.format(info.getUpdateTime(), "yyyy年MM月dd日"));
                dataList.add(data);
            }
        }
        return ExcelUtil.SheetData.builder().sheetName("职人码").headerAlias(headers).dataList(dataList).build();
    }

    private ExcelUtil.SheetData createGoodsCodeSheet(List<String> modelList,
            Map<String, EmptyCodeBindInfoDTO> emptyCodeInfoMap) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("no", "编号");
        headers.put("url", "链接");
        headers.put("key", "空码key值");
        headers.put("sourceUrl", "源码链接");
        headers.put("bindType", "绑定类型");
        headers.put("dpShopId", "dpShopId");
        headers.put("dealGroupId", "点评团单id");
        headers.put("bindTime", "绑定时间");

        List<Map<String, Object>> dataList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);

        for (String key : modelList) {
            EmptyCodeBindInfoDTO info = emptyCodeInfoMap.get(key);
            if (info != null && EmptyCodeBindKeyTypeEnum.GOODS_CODE.getCode() == info.getBindKeyType()) {
                Map<String, Object> data = new HashMap<>();
                data.put("no", index.getAndIncrement());
                data.put("url", info.getCodeImage());
                data.put("key", key);
                data.put("sourceUrl", info.getCodeLink());
                data.put("bindType", "商品码");
                String[] bindKeyParts = info.getBindKey().split("_");
                if (bindKeyParts.length == 2) {
                    data.put("dpShopId", bindKeyParts[0]);
                    data.put("dealGroupId", bindKeyParts[1]);
                }
                data.put("bindTime", DateUtil.format(info.getUpdateTime(), "yyyy年MM月dd日"));
                dataList.add(data);
            }
        }
        return ExcelUtil.SheetData.builder().sheetName("商品码").headerAlias(headers).dataList(dataList).build();
    }

    private ExcelUtil.SheetData createUnboundSheet(List<String> modelList,
            Map<String, EmptyCodeBindInfoDTO> emptyCodeInfoMap) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("no", "编号");
        headers.put("url", "链接");
        headers.put("key", "空码key值");
        headers.put("sourceUrl", "源码链接");

        List<Map<String, Object>> dataList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);

        for (String key : modelList) {
            EmptyCodeBindInfoDTO info = emptyCodeInfoMap.get(key);
            QRCodeConfigDTO qrCodeConfigDTO = getQrCodeConfigDTO(key);
            if (info == null || EmptyCodeBindStatusEnum.BOUND.getCode() != info.getStatus()) {
                Map<String, Object> data = new HashMap<>();
                data.put("no", index.getAndIncrement());
                data.put("url", qrCodeConfigDTO != null ? qrCodeConfigDTO.getImageUrl() : "");
                data.put("key", key);
                data.put("sourceUrl", qrCodeConfigDTO != null ? qrCodeConfigDTO.getCodeUrl() : "");
                dataList.add(data);
            }
        }

        return new ExcelUtil.SheetData("未绑定", headers, dataList);
    }

    private ExcelUtil.SheetData createAoiCodeSheet(List<String> modelList,
                                                   Map<String, EmptyCodeBindInfoDTO> emptyCodeInfoMap) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("no", "编号");
        headers.put("url", "链接");
        headers.put("key", "空码key值");
        headers.put("sourceUrl", "源码链接");
        headers.put("bindType", "绑定类型");
        headers.put("mtShopId", "mtShopId");
        headers.put("bindTime", "绑定时间");

        List<Map<String, Object>> dataList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);

        for (String key : modelList) {
            EmptyCodeBindInfoDTO info = emptyCodeInfoMap.get(key);
            if (info != null && EmptyCodeBizTypeEnum.AOI_CODE.getCode() == info.getBizType()) {
                Map<String, Object> data = new HashMap<>();
                data.put("no", index.getAndIncrement());
                data.put("url", info.getCodeImage());
                data.put("key", key);
                data.put("sourceUrl", info.getCodeLink());
                data.put("bindType", "AOI码");
                data.put("mtShopId", info.getBindKey());
                data.put("bindTime", DateUtil.format(info.getUpdateTime(), "yyyy年MM月dd日"));
                dataList.add(data);
            }
        }
        return ExcelUtil.SheetData.builder().sheetName("AOI码").headerAlias(headers).dataList(dataList).build();
    }

    private void uploadAndNotify(File file, List<String> users) {
        boolean uploadResult = s3Wrapper.uploadFile(file.getName(), file.getAbsolutePath());
        StringBuilder sb = new StringBuilder();
        sb.append("【").append(Environment.getEnvironment()).append("环境").append("】");
        if (uploadResult) {
            sb.append("  空码绑定状态导出成功，下载链接：").append(s3Wrapper.getDownloadUrl(file.getName()));
        } else {
            sb.append("  空码绑定状态Excel文件导出失败，请联系研发产品及研发同学! ");
        }
        elephantPushAclService.pushText(sb.toString(), users);
        file.delete();
    }

    private String getSourceUrlByKey(String codeKey) {
        QRCodeConfigDTO qrCodeConfigDTO = getQrCodeConfigDTO(codeKey);
        return qrCodeConfigDTO == null ? "" : qrCodeConfigDTO.getCodeUrl();
    }

    private QRCodeConfigDTO getQrCodeConfigDTO(String codeKey) {
        return qrCodeAclService.queryQrCodeConfigBySecretKeyAndBizTypes(codeKey,
                Lists.newArrayList(BizTypeEnum.OFFLINE_EMPTY_CODE.getType(), BizTypeEnum.H5_EMPTY_CODE.getType(), BizTypeEnum.H5_AOI_EMPTY_CODE.getType()));
    }

    private String generateFileName() {
        return "bindStatus-" + System.currentTimeMillis();
    }
}
