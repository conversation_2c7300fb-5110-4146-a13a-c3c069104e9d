package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querydetail;

import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateActivityDetailResponse;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.ProcessorContext;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询详情页上下文
 */
@Data
public class QueryDetailProcessorContext implements ProcessorContext {

    /**
     * 点评账号id
     */
    private Long accountId;

    /**
     * 活动加密ID
     */
    private String activityViewId;

    /**
     * 活动详情
     */
    private RebateActivityDetailResponse rebateActivityDetailResponse;

    /**
     * 活动记录
     */
    private List<RebateActivityRecordDO> rebateActivityRecords;

    /**
     * 已报名门店ID列表
     */
    private List<Long> appliedShopIdList = new ArrayList<>();
}
