package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querylist;

import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationPageQueryDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.OperationStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.converter.operation.OperationConverter;
import com.sankuai.medicalcosmetology.offline.code.application.service.OperationConfigForBApplicationService;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.OperationConfigQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityConfigDomainService;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityRecordDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.AccountAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询活动列表-准备数据
 */
@Slf4j
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, subScene = ProcessorSubSceneConstants.PREPARE_DATA)
public class QueryActivityListPrepareDataProcessor implements BaseProcessor<QueryActivityListProcessorContext> {

    @Resource
    private RebateActivityConfigDomainService rebateActivityConfigDomainService;

    @Resource
    private RebateActivityRecordDomainService rebateActivityRecordDomainService;

    @Resource
    private AccountAclService accountAclService;

    @Resource
    private OperationConfigForBApplicationService operationConfigForBApplicationService;

    @Override
    public void execute(QueryActivityListProcessorContext context) {
        //查询活动信息
        OperationPageQueryDTO queryDTO = new OperationPageQueryDTO();
        queryDTO.setActivityType(context.getActivityType());
        queryDTO.setStatus(context.getStatus());
        Long dpAccountId = context.getDpAccountId();
        OperationConfigQuery query = OperationConverter.convert2OperationConfigQuery(queryDTO);
        //B端活动查询不能分页
        query.setPageNo(null);
        query.setPageSize(null);
        //查询账号下关联的门店
        List<Long> dpShopList = accountAclService.getDpShopListByDpAccountId(dpAccountId);
        context.setDpShopListByAccount(dpShopList);

        //已结束的场景下，需要获取当前账号关联的店铺已报名的活动
        if (query.getTimeStatus() != null && query.getTimeStatus() == 2) {
            RebateActivityRecordQuery recordQuery = new RebateActivityRecordQuery();
            recordQuery.setDpShopAccountId(dpAccountId);
            List<RebateActivityRecordDO> applyRecordList = rebateActivityRecordDomainService.queryRebateActivityRecordList(recordQuery);

            if (CollectionUtils.isEmpty(applyRecordList)) {
                context.setRebateActivityConfigDOList(Lists.newArrayList());
                return;
            }
            List<Long> appliedActivityIdList = applyRecordList.stream().map(RebateActivityRecordDO::getActivityId).collect(Collectors.toList());
            query.setIds(appliedActivityIdList);
            List<RebateActivityConfigDO> list = rebateActivityConfigDomainService.pageQueryRebateActivityConfig(query);

            //手动下线的也需要展示
            OperationPageQueryDTO offlineQuery = new OperationPageQueryDTO();
            offlineQuery.setActivityType(context.getActivityType());
            offlineQuery.setStatus(OperationStatusEnum.OFFLINE.code);
            query = OperationConverter.convert2OperationConfigQuery(offlineQuery);
            query.setIds(appliedActivityIdList);
            List<RebateActivityConfigDO> offlineStatusActivityList = rebateActivityConfigDomainService.pageQueryRebateActivityConfig(query);
            if (CollectionUtils.isNotEmpty(offlineStatusActivityList)) {
                list.addAll(offlineStatusActivityList);
            }

            context.setRebateActivityConfigDOList(list);
            return;
        }
        List<RebateActivityConfigDO> rebateActivityConfigDOList = rebateActivityConfigDomainService.pageQueryRebateActivityConfig(query);

        context.setRebateActivityConfigDOList(rebateActivityConfigDOList);

        //如果活动不为空，查询账号基础信息，后续筛选可报名活动
        if (CollectionUtils.isNotEmpty(rebateActivityConfigDOList)) {
            operationConfigForBApplicationService.setDpAccountBaseInfoToCache(dpAccountId, dpShopList);
        }
    }
}
