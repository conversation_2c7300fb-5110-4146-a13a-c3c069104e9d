package com.sankuai.medicalcosmetology.offline.code.application.service.handler;

import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.ActivityDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.ActivityLiteDTO;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.ActivityPageRequest;
import com.dianping.gmkt.event.api.promoqrcode.dto.activity.ActivityPageResponse;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.application.converter.operation.OperationConverter;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.ResourceConfigDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PromoCodeActivityAclService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5
 * @Description:老发券活动配置相关服务
 */
@Service
public class OldCouponActivityService {

    @Resource
    private PromoCodeActivityAclService promoCodeActivityAclService;

    /**
     * 创建老的模型活动
     *
     * @param operationConfig 活动配置信息
     * @return 创建成功返回活动ID，失败返回-1
     *
     *         该方法用于创建新的彩虹活动。主要步骤包括：
     *         1. 重置活动ID和更新用户信息
     *         2. 调用远程服务创建活动
     *         3. 获取新创建的活动信息
     *         4. 更新活动状态为在线
     *
     *         注意：如果任何步骤失败，方法将返回-1
     */
    public Long createRainbowActivity(OperationInfoDTO operationConfig) {
        operationConfig.setActivityId(null);
        operationConfig.setUpdator(operationConfig.getCreator());
        PromoQRCodeResponse<Boolean> createResponse = promoCodeActivityAclService
                .createPromoCodeActivity(OperationConverter.convert2ActivityDTO(operationConfig));
        if (createResponse == null || !createResponse.isSuccess() || !createResponse.getData()) {
            return -1L;
        }
        ActivityLiteDTO activityDTO = null;
        int retryCount = 0;
        while (retryCount < 3 && (activityDTO == null)) {
            activityDTO = getActivityLiteDTO(buildActivityPageRequest(operationConfig));
            if (activityDTO != null) {
                // 0 下线
                // 1 在线
                boolean result = updateRainbowActivityStatus(operationConfig.getCreator(), 1,
                        activityDTO.getActivityId());
                if (result) {
                    return activityDTO.getActivityId();
                }
            }
            retryCount++;
        }
        return -1L;
    }

    public boolean updateRainbowActivityStatus(String creator, Integer status, Long id) {
        // 0 下线
        // 1 在线
        PromoQRCodeResponse<Boolean> result = promoCodeActivityAclService.updateRainbowCouponActivityStatus(id, status,
                creator);
        if (result == null || !result.isSuccess() || result.getData() == null) {
            return false;
        } else {
            return result.getData();
        }
    }

    /**
     * 根据条件查询老的资源配置列表的数量。
     *
     * @param query 查询条件
     * @return 返回符合条件的资源配置数量。如果查询失败，返回0。
     */
    public long queryResourceConfigListConditionalOldCount(ActivityPageRequest query) {
        query.setPageNum(1);
        query.setPageSize(1);
        PromoQRCodeResponse<ActivityPageResponse> pageResponse = promoCodeActivityAclService
                .queryPromoCodeActivityCondition(query);
        if (pageResponse == null || !pageResponse.isSuccess() || pageResponse.getData() == null) {
            return 0L;
        }
        return pageResponse.getData().getTotalNum();
    }

    /**
     * 根据条件查询老的资源配置列表。
     * 
     * 该方法通过传入的查询条件{@link ActivityPageRequest}，调用远程服务查询活动列表，
     * 然后将查询到的活动列表转换为资源配置列表{@link ResourceConfigDO}返回。
     * 如果远程服务调用失败或查询结果为空，则返回一个空列表。
     * 
     * @param query 查询条件，包含活动的各种筛选条件。
     * @return 返回符合条件的资源配置列表。如果查询失败或没有符合条件的配置，返回空列表。
     */
    public List<ActivityLiteDTO> queryResourceConfigListConditionalOld(ActivityPageRequest query) {
        PromoQRCodeResponse<ActivityPageResponse> pageResponse = promoCodeActivityAclService
                .queryPromoCodeActivityCondition(query);
        if (pageResponse == null || !pageResponse.isSuccess() || pageResponse.getData() == null) {
            return Collections.emptyList();
        }
        ActivityPageResponse activityPageResponse = pageResponse.getData();
        if (CollectionUtils.isEmpty(activityPageResponse.getActivityList())) {
            return Collections.emptyList();
        }
        return activityPageResponse.getActivityList();
    }

    public ActivityDTO getOldActivityById(Long activityId) {
        return promoCodeActivityAclService.queryCouponActivityConfigById(activityId);
    }

    public boolean updateRainbowActivity(OperationInfoDTO newConfig) {
        PromoQRCodeResponse<Boolean> createResponse = promoCodeActivityAclService
                .updateRainbowCouponActivity(OperationConverter.convert2ActivityDTO(newConfig));
        return createResponse != null && createResponse.isSuccess() && createResponse.getData();
    }

    public ActivityLiteDTO getActivityLiteDTO(ActivityPageRequest activityPageRequest) {
        PromoQRCodeResponse<ActivityPageResponse> pageResponse = promoCodeActivityAclService
                .queryPromoCodeActivityCondition(activityPageRequest);
        if (pageResponse == null || !pageResponse.isSuccess() || pageResponse.getData() == null
                || CollectionUtils.isEmpty(pageResponse.getData().getActivityList())) {
            return null;
        }
        return pageResponse.getData().getActivityList().get(0);
    }

    private static ActivityPageRequest buildActivityPageRequest(OperationInfoDTO operationConfig) {
        ActivityPageRequest activityPageRequest = new ActivityPageRequest();
        activityPageRequest.setActivityId(operationConfig.getActivityId());
        activityPageRequest.setActivityName(operationConfig.getActivityName());
        activityPageRequest.setCreator(operationConfig.getCreator());
        if (operationConfig.getBizType() != null) {
            activityPageRequest.setBizType(operationConfig.getBizType());
        }
        // 0不在线，1在线
        activityPageRequest.setStatus(0);
        activityPageRequest.setPageNum(1);
        activityPageRequest.setPageSize(1);
        return activityPageRequest;
    }
}
