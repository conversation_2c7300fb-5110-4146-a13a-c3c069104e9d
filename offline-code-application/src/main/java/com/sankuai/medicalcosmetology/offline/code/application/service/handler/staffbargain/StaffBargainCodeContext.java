package com.sankuai.medicalcosmetology.offline.code.application.service.handler.staffbargain;

import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.ProductDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.staffbargain.StaffBargainCodeDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.BargainStaffTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.StaffBargainBizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.StaffBargainCodeRequest;
import com.sankuai.medicalcosmetology.offline.code.domain.staffbargain.config.StaffBargainConfig;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.ContentAuditRecords;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.dal.entity.StaffBargainDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/12 15:12
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class StaffBargainCodeContext {

    //原始参数
    private Long staffId;
    private BargainStaffTypeEnum staffType;
    private List<ProductDTO> productList;
    private BigDecimal bargainPrice;
    private List<String> picUrlList;
    private String text;
    private StaffBargainEnv env;

    //中间数据
    /**
     * 美团dealId到点评dealId的映射
     */
    private Map<Long, Long> mt2DpDealIdMap;
    /**
     * 团单信息
     */
    private List<DealGroupDTO> dealInfoList;
    /**
     * 预付信息
     */
    private List<DealGroupDTO> preInfoList;

    private StaffBargainConfig staffbargainConfig;
    private BigDecimal minBargainPrice;
    private BigDecimal maxBargainPrice;
    private StaffBargainBizTypeEnum bizTypeEnum;

    private StaffBargainDetail staffBargainDetail;
    private List<ContentAuditRecords> picRecords;
    private ContentAuditRecords textRecord;

    private QRCodeConfigDTO qrCodeConfigDTO;

    //结果数据
    private StaffBargainCodeDTO staffBargainCodeDTO;


    public static StaffBargainCodeContext init(StaffBargainCodeRequest request) {
        StaffBargainCodeContext context = new StaffBargainCodeContext();
        context.setStaffId(request.getStaffId());
        context.setStaffType(BargainStaffTypeEnum.getByCode(request.getStaffType()));
        context.setProductList(request.getProductList());
        context.setBargainPrice(request.getBargainPriceBigDecimal());
        context.setPicUrlList(request.getPicUrlList());
        context.setText(request.getText());
        //环境参数
        StaffBargainEnv env = new StaffBargainEnv();
        env.setUserId(ObjectUtils.isEmpty(request.getUserId()) ? 0L : request.getUserId());
        env.setPlatform(PlatformEnum.fromCode(request.getPlatform()));
        context.setEnv(env);
        return context;
    }
}
