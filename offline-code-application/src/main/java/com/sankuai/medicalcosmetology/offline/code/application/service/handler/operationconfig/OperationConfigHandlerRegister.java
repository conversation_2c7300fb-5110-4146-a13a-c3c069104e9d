package com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig;

import com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.business.OperationConfigForBHandler;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.manage.OperationConfigForMHandler;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

@Component
public class OperationConfigHandlerRegister implements BeanPostProcessor {


    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof OperationConfigForMHandler) {
            OperationConfigFactory.registerForM((OperationConfigForMHandler) bean);
        }

        if (bean instanceof OperationConfigForBHandler) {
            OperationConfigFactory.registerForB((OperationConfigForBHandler) bean);
        }
        return bean;
    }
}
