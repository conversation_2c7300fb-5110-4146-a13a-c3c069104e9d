package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.idmapper.service.ShopMapperService;
import com.sankuai.carnation.distribution.common.enums.DistributionStatusEnum;
import com.sankuai.carnation.distribution.common.enums.DistributorBizTypeEnum;
import com.sankuai.carnation.distribution.common.enums.DistributorUserTypeEnum;
import com.sankuai.carnation.distribution.distributor.dto.BizDistributorDTO;
import com.sankuai.carnation.distribution.distributor.dto.operate.BizDistributorOperateDTO;
import com.sankuai.carnation.distribution.distributor.service.BizDistributorService;
import com.sankuai.carnation.distribution.groundpromotion.enums.UserTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ShortCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/12/26
 * @Description: 地推兼职落地页处理器
 */
@Service
@Slf4j
public class GPPTLandingPageHandler extends AbstractLandingPageHandler {

    @Autowired
    private ShopMapperService shopMapperService;

    @Autowired
    private BizDistributorService bizDistributorService;

    @Override
    public boolean support(PromoCodeLandingPageRequest request) {
        return request.getSource().equals(LandingPageSourceEnum.GROUND_PROMOTION_PART_TIME.getCode());
    }

    @Override
    protected boolean needEncrypt() {
        return true;
    }

    @Override
    protected void preprocessRequest(PromoCodeLandingPageRequest request) {
        // 转换codeKey：兼职code -> 地推分销参数
        Long dpShopId = shopMapperService.mt2dp(request.getPoiId());
        String codeKey = handleCodeKey(dpShopId, request.getCodeKey());
        request.setCodeKey(codeKey);
    }

    @Override
    protected String postprocessUrl(String pageUrl, PromoCodeLandingPageRequest request) {
        // 补充source参数
        return pageUrl + "&source=1";
    }

    @Override
    public PromoCodeLandingUrlDTO queryAllPlatformPromoCodeLandingPageUrl(PromoCodeLandingPageRequest request) {
        return null;
    }

    private String handleCodeKey(Long dpShopId, String codeKey) {
        RemoteResponse<List<BizDistributorDTO>> response = bizDistributorService.queryBizDistributorByUserId(codeKey, UserTypeEnum.PART_TIME.getCode());
        if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            BizDistributorOperateDTO bizDistributorOperateDTO = new BizDistributorOperateDTO();
            bizDistributorOperateDTO.setBizId(String.valueOf(dpShopId));
            bizDistributorOperateDTO.setBizType(DistributorBizTypeEnum.GROUND_PART_TIME_PROMOTION.getCode());
            bizDistributorOperateDTO.setStatus(DistributionStatusEnum.VALID.getCode());
            bizDistributorOperateDTO.setUserId(codeKey);
            bizDistributorOperateDTO.setUserType(DistributorUserTypeEnum.PART_TIME.getCode());
            String distributorCode = String.format("dt%s$$%s", ShortCodeUtil.gen(), dpShopId);
            bizDistributorOperateDTO.setDistributorCode(distributorCode);
            RemoteResponse<Long> bizResp = bizDistributorService.editBizDistributor(bizDistributorOperateDTO);

            if (!bizResp.isSuccess() || bizResp.getData() == null) {
                log.error(getClass().getSimpleName() + ".handlerCodeKey insert bizDistributor error, dpShopId is {}, codeKey is {}", dpShopId, codeKey);
                return "";
            }
            return distributorCode;
        } else {
            return response.getData().get(0).getDistributorCode();
        }
    }
}
