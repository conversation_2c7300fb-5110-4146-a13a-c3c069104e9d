package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.carnation.distribution.common.dto.QRCodeConfigDTO;
import com.sankuai.carnation.distribution.common.enums.QRCodeSourceEnum;
import com.sankuai.carnation.distribution.common.service.UniversalQRCodeGeneratorService;
import com.sankuai.medicalcosmetology.offline.code.api.dto.PromoCodeLandingUrlDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.LandingPageSourceEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PromoCodeType;
import com.sankuai.medicalcosmetology.offline.code.api.enums.QRClientType;
import com.sankuai.medicalcosmetology.offline.code.api.request.PromoCodeLandingPageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.Map;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/8/8
 * @Description: 风信落地页处理器
 */
@Service
@Slf4j
public class WindLandingPageHandler extends AbstractLandingPageHandler {

    @Override
    public boolean support(PromoCodeLandingPageRequest request) {
        return request.getSource().equals(LandingPageSourceEnum.EMPTY.getCode());
    }

    @Override
    protected boolean needEncrypt() {
        return false;
    }

    @Override
    protected String selectPageUrl(Map<String, String> pageUrlMap,
                                 LandingPageUrlContext.ClientTypeInfo clientType,
                                 LandingPageUrlContext.SwitchInfo switches,
                                 PromoCodeLandingPageRequest request) {
        if (request.getCodeType() == PromoCodeType.GOODS_CODE.code) {
            return handlerGoodsCodeUrl(pageUrlMap, clientType, switches, request);
        } else {
            return handlerDefaultCodeUrl(pageUrlMap, clientType, switches, request);
        }
    }

    private String handlerDefaultCodeUrl(Map<String, String> pageUrlMap, 
                                        LandingPageUrlContext.ClientTypeInfo clientType,
                                        LandingPageUrlContext.SwitchInfo switches,
                                        PromoCodeLandingPageRequest request) {
        // 中间页灰度
        boolean intermediateSwitch = urlContext.getIntermediateSwitch(request);

        String pageUrl = "";

        // 根据不同环境获取链接
        if (clientType.isXcx()) {
            // 小程序，不跳中间页，直接跳最终落地页
            pageUrl = pageUrlMap.get(switches.isH5NewSwitch() ? "mt-h5-new" : "mt");
        } else if (clientType.isH5()) {
            pageUrl = pageUrlMap.get(clientType.isMt() ?
                    (switches.isH5NewSwitch() ? "mt-h5-new" : "mt") :
                    (switches.isH5NewSwitch() ? "dp-h5-new" : "dp"));
            // 除小程序外其余站外h5场景，不跳中间页，直接跳最终落地页
        } else {
            // 站内
            pageUrl = pageUrlMap.get(intermediateSwitch ? "intermediate" : (clientType.isMt() ? "mt" : "dp"));
        }

        return pageUrl;
    }

    private String handlerGoodsCodeUrl(Map<String, String> pageUrlMap, 
                                      LandingPageUrlContext.ClientTypeInfo clientType,
                                      LandingPageUrlContext.SwitchInfo switches,
                                      PromoCodeLandingPageRequest request) {
        // 中间页灰度
        boolean intermediateSwitch = urlContext.getIntermediateSwitch(request);

        String pageUrl = "";

        // 根据不同环境获取链接
        if (clientType.isXcx()) {
            // 小程序，商品码会跳中间页
            pageUrl = pageUrlMap.get(intermediateSwitch ? "intermediate" : 
                (switches.isH5NewSwitch() ? "mt-h5-new" : "mt"));
        } else if (clientType.isH5()) {
            // 除小程序外其余站外h5场景，商品码会跳中间页
            pageUrl = pageUrlMap.get(intermediateSwitch ? "intermediate" : 
                (clientType.isMt() ? (switches.isH5NewSwitch() ? "mt-h5-new" : "mt") : 
                (switches.isH5NewSwitch() ? "dp-h5-new" : "dp")));
        } else {
            // 站内
            pageUrl = pageUrlMap.get(intermediateSwitch ? "intermediate" : (clientType.isMt() ? "mt" : "dp"));
        }

        return pageUrl;
    }

    @Override
    protected String fillUrlParameters(String pageUrl, 
                                 LandingPageUrlContext.ClientTypeInfo clientType,
                                 LandingPageUrlContext.SwitchInfo switches,
                                 PromoCodeLandingPageRequest request) {
        boolean intermediateSwitch = urlContext.getIntermediateSwitch(request);
        if (request.getCodeType() == PromoCodeType.GOODS_CODE.code) {
            if (intermediateSwitch) {
                // 补充中间页
                pageUrl = fillIntermediateUrl(pageUrl, request);
            } else {
                pageUrl = promoCodeLandingPageUrlService.fillLandingPageUrl(pageUrl, request, needEncrypt());
            }
            
            if (switches.isH5NewSwitch() && !intermediateSwitch && (clientType.isXcx() || clientType.isH5())) {
                // 小程序或其余站外h5，且命中新h5。由于风信直接绑定最终地址，因此需要补充额外参数
                pageUrl = promoCodeLandingPageUrlService.fillLandingPageUrlExt(pageUrl, request.getCodeType(), request.getPoiId());
            }
        } else {
            if (intermediateSwitch && !clientType.isXcx() && !clientType.isH5()) {
                // 补充中间页
                pageUrl = fillIntermediateUrl(pageUrl, request);
            } else {
                String codeKey = promoCodeLandingPageUrlService.queryOrInitCodeKey(request, QRCodeSourceEnum.SYSTEM.getCode(), getClass().getSimpleName());
                if (StringUtils.isNotEmpty(codeKey)) {
                    request.setCodeKey(codeKey);
                }
                pageUrl = promoCodeLandingPageUrlService.fillLandingPageUrl(pageUrl, request, needEncrypt());
            }
            
            if (switches.isH5NewSwitch() && (clientType.isXcx() || clientType.isH5())) {
                // 小程序或其余站外h5，且命中新h5。由于风信直接绑定最终地址，因此需要补充额外参数
                pageUrl = promoCodeLandingPageUrlService.fillLandingPageUrlExt(pageUrl, request.getCodeType(), request.getPoiId());
            }
        }
        return pageUrl;
    }

    @Override
    protected String encodeUrl(String pageUrl, Map<String, String> pageUrlMap,
                               LandingPageUrlContext.ClientTypeInfo clientType,
                               LandingPageUrlContext.SwitchInfo switches) throws Exception {
        if (clientType.isXcx()) {
            String prefixUrl = urlContext.getWxPrefixUrl(pageUrlMap);
            return prefixUrl + URLEncoder.encode(pageUrl, "UTF-8");
        }
        return pageUrl;
    }

    private String fillIntermediateUrl(String pageUrl, PromoCodeLandingPageRequest request) {
        PromoCodeType codeType = PromoCodeType.getByCode(request.getCodeType());
        if (!PromoCodeType.validateCode(request.getCodeType())) {
            return pageUrl;
        }

        pageUrl += "?scene=%s&q=%s";

        String codeKey = promoCodeLandingPageUrlService.queryOrInitCodeKey(request, QRCodeSourceEnum.SYSTEM.getCode(), getClass().getSimpleName());

        switch (codeType) {
            case SHOP_CODE:
                return String.format(pageUrl, "promo_shop", StringUtils.isNotEmpty(codeKey) ? codeKey : request.getPoiId());
            case GOODS_CODE:
                return String.format(pageUrl, "promo_goods", StringUtils.isNotEmpty(codeKey) ? codeKey : request.getGoodsId() + "_" + request.getPoiId());
            case STAFF_CODE:
                return String.format(pageUrl, "staff", StringUtils.isNotEmpty(codeKey) ? codeKey : request.getStaffCodeId());
            case BRAND_CODE:
                return String.format(pageUrl, "brand", StringUtils.isNotEmpty(codeKey) ? codeKey : request.getCustomerId());
            default:
                return pageUrl;
        }
    }
}
