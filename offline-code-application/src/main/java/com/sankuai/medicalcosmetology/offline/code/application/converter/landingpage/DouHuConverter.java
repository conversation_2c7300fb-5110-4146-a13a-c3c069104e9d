package com.sankuai.medicalcosmetology.offline.code.application.converter.landingpage;

import com.meituan.mtrace.Tracer;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.util.DouHuUtil;
import com.sankuai.medicalcosmetology.offline.code.api.dto.landingpage.QueryExpABInfoResultDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.MobileOSEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.PlatformEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.QueryExpABInfoRequest;
import org.springframework.stereotype.Service;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/9/12
 * @Description:
 */
public class DouHuConverter {

    public static DouHuRequest convert2DouHuRequest(QueryExpABInfoRequest request) {
        DouHuRequest douHuRequest = new DouHuRequest();
        douHuRequest.setExpId(request.getExpId());
        douHuRequest.setUserId(Tracer.getContext("userId"));
        douHuRequest.setDpid(Tracer.getContext("dpId"));
        douHuRequest.setUuid(Tracer.getContext("uuId"));
        douHuRequest.setUnionId(Tracer.getContext("unionId"));
        if (request.getPlatform() != null) {
            douHuRequest.setPlatform(PlatformEnum.fromCode(request.getPlatform()).name());
        }
        douHuRequest.setCityId(String.valueOf(request.getCityId()));
        douHuRequest.setAppId(request.getAppId());
        if (request.getMobileOS() != null) {
            douHuRequest.setOs(MobileOSEnum.fromCode(request.getMobileOS()).name());
        }
        douHuRequest.setAppClient(request.getVersion());
        return douHuRequest;
    }

    public static QueryExpABInfoResultDTO convert2QueryExpABInfo(DouHuResponse response) {
        QueryExpABInfoResultDTO queryExpABInfoResultDTO = new QueryExpABInfoResultDTO();
        queryExpABInfoResultDTO.setExpId(response.getExpId());
        queryExpABInfoResultDTO.setStrategyKey(response.getSk());
        queryExpABInfoResultDTO.setModuleAbInfo(DouHuUtil.extractExpABInfo4Front(response));
        return queryExpABInfoResultDTO;
    }
}
