package com.sankuai.medicalcosmetology.offline.code.application.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RebateOrderRecordWithdrawInfo {
    private Long id;
    private Long activityConfigId;
    private Long activityRecordId;
    private String unifiedOrderId;
    private Long longOrderId;
    private BigDecimal totalAmount;
    private BigDecimal actualPayAmount;
    private Long rebateAmount;
    private Long dpShopId;
    private Long mtShopId;
    private Long userId;
    private Byte platform;
    private Date scanTime;
    private Date orderTime;
    private Date verifyTime;
    private Integer status;
    private Date createTime;
    private Date updateTime;
    private String groupDifference;
    private Integer settleType;
    private Date withDrawTime;
    private Boolean hasWithDraw;

}
