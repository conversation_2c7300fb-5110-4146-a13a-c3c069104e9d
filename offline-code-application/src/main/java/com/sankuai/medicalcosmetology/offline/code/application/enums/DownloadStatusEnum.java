package com.sankuai.medicalcosmetology.offline.code.application.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum DownloadStatusEnum {

    SUCCESS(200, "下载完成"),
    DOWNLOAD_DOING(201, "下载中..."),
    RESULT_IS_EMPTY(202, "未查询到相关数据"),
    TIME_OUT(203, "下载超时");

    public final int code;
    public final String desc;
    DownloadStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        return Arrays.stream(values()).filter(t -> t.code == code).findFirst().map(DownloadStatusEnum::getDesc).orElse("");
    }

}
