package com.sankuai.medicalcosmetology.offline.code.application.model.audit;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/6/17 11:03
 */
@Data
public class ContentAuditDetail implements Serializable {
    /**
     * 审核结果的类型
     */
    private int verifyStatus;

    /**
     * 业务透传数据
     */
    private String bizData;

    /**
     * 审核”不通过“或”疑似“的明细理由
     */
    private List<Map<String, Object>> statusName;

    /**
     * 业务需要定制结果
     */
    private List<Map<String, Object>> extend;
}
