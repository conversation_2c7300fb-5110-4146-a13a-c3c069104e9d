package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.dianping.gmkt.event.api.promoqrcode.BatchUpdateExpressResponseDTO;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.UpdateExpressReqDTO;
import com.dianping.gmkt.event.api.promoqrcode.enums.ExpressEnum;
import com.dianping.lion.Environment;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.enums.fileload.FileLoadSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.application.model.sso.User;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ElephantPushAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PromoCodeActivityAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/20
 * @Description:商家物料管理-上传快递单号
 */
@Service
@Slf4j
public class PromoCodeExpressFileUploadHandler implements FileUploadProcessHandlerIface<UpdateExpressReqDTO, User> {

    private static final ThreadPoolExecutor executorService = Rhino.newThreadPool("promoCodeExpressUpload", DefaultThreadPoolProperties.Setter()
            .withCoreSize(5).withMaxSize(10).withBlockingQueue(new LinkedBlockingQueue<>(100))).getExecutor();

    /**
     * 最大上传excel条数
     */
    public final static int MAX_UPLOAD_EXCEL_SIZE = 1000;

    @Autowired
    private PromoCodeActivityAclService promoCodeActivityAclService;

    @Autowired
    private ElephantPushAclService elephantPushAclService;

    /**
     * 执行文件上传处理并返回结果
     *
     * @return 处理结果的字符串表示
     */
    @Override
    public String executeWithResult(InputStream inputStream, User user) {
        List<UpdateExpressReqDTO> fileData = readFileAsBean(inputStream, user);
        executorService.submit(() -> processData(fileData, user));
        return String.format("成功解析%d条数据，稍后上传结果将以大象通知发出，请注意查收", fileData.size());
    }

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    @Override
    public boolean support(Integer sceneType) {
        return FileLoadSceneEnum.MATERIAL_EXPRESS.getType().equals(sceneType);

    }

    /**
     * 将文件项列表读取为对象列表
     *
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    @Override
    public List<UpdateExpressReqDTO> readFileAsBean(InputStream inputStream, User user) {
        List<UpdateExpressReqDTO> updateExpressReqDTOList = new ArrayList<>();
        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            int rows = sheet.getPhysicalNumberOfRows();

            for (int i = 1; i < rows; i++) {
                Row row = sheet.getRow(i);
                validateRow(row, i);
                UpdateExpressReqDTO dto = createDTOFromRow(row, i);
                updateExpressReqDTOList.add(dto);
            }
        } catch (IOException e) {
            log.error(getClass().getSimpleName() + ".readFileAsBean error", e);
        }

        validateUploadSize(updateExpressReqDTOList);
        checkBatchUpdateExpressReqDTO(updateExpressReqDTOList);
        return updateExpressReqDTOList;
    }

    private void validateRow(Row row, int rowIndex) {
        if (invalidExcelCell(row.getCell(0)) || invalidExcelCell(row.getCell(1))
                || invalidExcelCell(row.getCell(2))) {
            throw new IllegalArgumentException(String.format("上传失败，第%s行存在空值，请删除后再试", rowIndex + 1));
        }
    }

    private UpdateExpressReqDTO createDTOFromRow(Row row, int rowIndex) {
        UpdateExpressReqDTO dto = new UpdateExpressReqDTO();

        dto.setPromoQRCodeId(getPromoQRCodeId(row, rowIndex));
        dto.setExpressName(getExpressName(row, rowIndex));
        dto.setExpressNo(getExpressNo(row, rowIndex));

        return dto;
    }

    private Long getPromoQRCodeId(Row row, int rowIndex) {
        Long promoQRCodeId = getLongFromCellValue(row.getCell(0));
        if (promoQRCodeId == null) {
            throw new IllegalArgumentException(String.format("上传失败，第%s行第1列期待整数类型", rowIndex + 1));
        }
        return promoQRCodeId;
    }

    private String getExpressName(Row row, int rowIndex) {
        String expressName = getCellValue(row.getCell(1)).toString().trim();
        ExpressEnum expressEnum = ExpressEnum.getByName(expressName);
        if (expressEnum == null) {
            throw new IllegalArgumentException(
                    String.format("上传失败：快递公司名称[%s]错误，请输入正确快递公司名称。仅支持【中通、申通、汇通、圆通、韵达、邮政包裹、ems】", expressName));
        }
        return expressEnum.code;
    }

    private String getExpressNo(Row row, int rowIndex) {
        Object cellValue = getCellValue(row.getCell(2));
        if (cellValue instanceof String && !cellValue.toString().contains(".")
                && StringUtils.isNotBlank(cellValue.toString())) {
            return cellValue.toString();
        } else {
            throw new IllegalArgumentException(String.format("上传失败，第%s行第3列期待非空文本类型", rowIndex + 1));
        }
    }

    private void validateUploadSize(List<UpdateExpressReqDTO> dtoList) {
        if (CollectionUtils.isNotEmpty(dtoList) && dtoList.size() > MAX_UPLOAD_EXCEL_SIZE) {
            throw new IllegalArgumentException(String.format("每次最多可上传%s条", MAX_UPLOAD_EXCEL_SIZE));
        }
    }

    /**
     * 处理解析后的数据对象列表
     *
     * @param modelList 解析后的数据对象列表，每个对象代表一个文件的内容
     */
    @Override
    public void processData(List<UpdateExpressReqDTO> modelList, User user) {
        List<String> receiveUserList = Arrays.asList("chenhaoyang02", "lipengyu04", "fei.pan", user.getLogin());
        StringBuilder sb = new StringBuilder();
        prepareMessageHeader(sb, user);
        BatchUpdateExpressResponseDTO result = new BatchUpdateExpressResponseDTO();
        processUpdateExpress(modelList, result, sb);
        appendResultToMessage(sb, result);
        elephantPushAclService.pushText(sb.toString(), receiveUserList);
    }

    private void prepareMessageHeader(StringBuilder sb, User user) {
        sb.append("【环境】: ").append(Environment.getEnvironment()).append(System.lineSeparator());
        sb.append("【操作】: 彩虹系统/优惠码/上传快递单号").append(System.lineSeparator());
        sb.append("【操作人】: ").append(user.getName()).append(System.lineSeparator());
        sb.append("【结果】:").append(System.lineSeparator());
    }

    private void processUpdateExpress(List<UpdateExpressReqDTO> modelList, BatchUpdateExpressResponseDTO result,
            StringBuilder sb) {
        List<List<UpdateExpressReqDTO>> partition = Lists.partition(modelList, 50);
        for (List<UpdateExpressReqDTO> updateExpressReqDTOS : partition) {
            try {
                PromoQRCodeResponse<BatchUpdateExpressResponseDTO> response = promoCodeActivityAclService
                        .updateExpressInfo(updateExpressReqDTOS);
                updateResultBasedOnResponse(response, result, updateExpressReqDTOS);
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException ie) {
                log.error(getClass().getSimpleName() + ".uploadExpress InterruptedException", ie);
            } catch (Exception e) {
                log.error(getClass().getSimpleName() + ".uploadExpress error", e);
                handleExceptionDuringProcessing(sb, e);
                break;
            }
        }
    }

    private void updateResultBasedOnResponse(PromoQRCodeResponse<BatchUpdateExpressResponseDTO> response,
            BatchUpdateExpressResponseDTO result, List<UpdateExpressReqDTO> updateExpressReqDTOS) {
        if (response != null && response.isSuccess()) {
            result.setAllCount(response.getData().getAllCount() + result.getAllCount());
            result.setFailCount(response.getData().getFailCount() + result.getFailCount());
            if (CollectionUtils.isNotEmpty(response.getData().getFailPromoQRCodeIds())) {
                result.getFailPromoQRCodeIds().addAll(response.getData().getFailPromoQRCodeIds());
            }
        } else {
            result.setAllCount(result.getAllCount() + updateExpressReqDTOS.size());
            result.setFailCount(result.getFailCount() + updateExpressReqDTOS.size());
            result.getFailPromoQRCodeIds().addAll(updateExpressReqDTOS.stream()
                    .map(UpdateExpressReqDTO::getPromoQRCodeId).collect(Collectors.toList()));
        }
    }

    private void appendResultToMessage(StringBuilder sb, BatchUpdateExpressResponseDTO result) {
        sb.append(" 总计上传数: ").append(result.getAllCount()).append(System.lineSeparator());
        sb.append(" 失败数: ").append(result.getFailCount()).append(System.lineSeparator());
        sb.append(" 失败promoQRCoIds: ").append(result.getFailPromoQRCodeIds()).append(System.lineSeparator());
    }

    private void handleExceptionDuringProcessing(StringBuilder sb, Exception e) {
        sb.append("发生异常，请联系开发").append(System.lineSeparator());
        sb.append("错误信息: ").append(e.getMessage());
    }

    private void checkBatchUpdateExpressReqDTO(List<UpdateExpressReqDTO> updateExpressReqDTOList) {
        if (CollectionUtils.isEmpty(updateExpressReqDTOList)) {
            return;
        }
        for (UpdateExpressReqDTO item : updateExpressReqDTOList) {
            if (item.getPromoQRCodeId() == null) {
                throw new IllegalArgumentException("promoQRCodeId不能为空");
            }
            if (StringUtils.isBlank(item.getExpressName())) {
                throw new IllegalArgumentException("快递公司不能为空");
            }
            if (StringUtils.isBlank(item.getExpressNo())) {
                throw new IllegalArgumentException("运单号码不能为空");
            }
        }
    }

    private Long getLongFromCellValue(Cell cell) {
        Object cellValue = getCellValue(cell);
        if (cellValue == null) {
            return null;
        }
        if (cellValue instanceof String) {
            return Long.parseLong((String)cellValue);
        } else if (cellValue instanceof Double) {
            return ((Double)cellValue).longValue();
        }
        return null;
    }

    private Object getCellValue(Cell cell) {
        if (cell.getCellType().equals(CellType.STRING)) {
            return cell.getStringCellValue();
        } else if (cell.getCellType().equals(CellType.NUMERIC)) {
            return cell.getNumericCellValue();
        } else if (cell.getCellType().equals(CellType.BOOLEAN)) {
            return cell.getBooleanCellValue();
        }

        return cell.getStringCellValue();
    }

    private boolean invalidExcelCell(Cell cell) {
        return cell == null || cell.getCellType().equals(CellType._NONE)
                || cell.getCellType().equals(CellType.BLANK) || cell.getCellType().equals(CellType.ERROR);
    }
}
