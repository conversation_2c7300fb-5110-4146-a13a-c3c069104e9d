package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querylist;

import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;

/**
 * B端-查询活动列表-参数校验
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_ACTIVITY_LIST_FOR_B, subScene = ProcessorSubSceneConstants.PARAM_CHECK)
public class QueryActivityListParamCheckProcessor implements BaseProcessor<QueryActivityListProcessorContext> {
    @Override
    public void execute(QueryActivityListProcessorContext context) {
        AssertUtil.notNull(context, "上下文不能为空");
        AssertUtil.notNull(context.getActivityType(), "活动类型不能为空");
        AssertUtil.notNull(context.getStatus(), "活动状态不能为空");
        AssertUtil.notNull(context.getDpShopId(), "店铺ID不能为空");
        AssertUtil.notNull(context.getDpAccountId(), "页码不能为空");
    }
}
