package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.querydetail;

import com.dianping.gmkt.event.api.promoqrcode.enums.QRRebateSettleTypeEnum;
import com.dianping.merchant.member.dto.ShopAccountModuleInfoDTO;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.AppliedInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.AppliedShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.RebateActivityDetailResponse;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityRecordStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.MerchantMemberService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PoiAclService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组装报名门店信息
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_DETAIL_FOR_B, subScene = ProcessorSubSceneConstants.QUERY_APPLY_SHOP)
public class QueryDetailAssembleShopInfoProcessor implements BaseProcessor<QueryDetailProcessorContext> {

    @Resource
    private PoiAclService poiAclService;

    @Resource
    private MerchantMemberService merchantMemberService;

    @Override
    public void execute(QueryDetailProcessorContext context) {
        RebateActivityDetailResponse rebateActivityDetailResponse = context.getRebateActivityDetailResponse();

        List<RebateActivityRecordDO> rebateActivityRecords = context.getRebateActivityRecords();
        if (CollectionUtils.isEmpty(rebateActivityRecords)) {
            return;
        }


        //店铺和报名记录映射，key：点评账号id，value：报名记录
        Map<Integer, List<RebateActivityRecordDO>> shopActivityRecordMap = new HashMap<>();
        for (RebateActivityRecordDO record : rebateActivityRecords) {
            if (!RebateActivityRecordStatusEnum.VALID.code.equals(record.getStatus())) {
                continue;
            }
            int currentDpAccountId = Math.toIntExact(record.getDpAccountId());
            if (shopActivityRecordMap.containsKey(currentDpAccountId)) {
                List<RebateActivityRecordDO> rebateActivityRecordDOList = shopActivityRecordMap.get(currentDpAccountId);
                rebateActivityRecordDOList.add(record);
            } else {
                shopActivityRecordMap.put(currentDpAccountId, Lists.newArrayList(record));
            }
        }


        List<Long> appliedShopIdList = new ArrayList<>();
        List<AppliedInfoResponse> appliedInfo = new ArrayList<>();
        //查询账号信息
        for (Integer dpAccountId : shopActivityRecordMap.keySet()) {
            List<RebateActivityRecordDO> records = shopActivityRecordMap.get(dpAccountId);
            if (CollectionUtils.isEmpty(records)) {
                continue;
            }
            AppliedInfoResponse appliedInfoResponse = new AppliedInfoResponse();
            List<ShopAccountModuleInfoDTO> shopAccountModuleInfoDTOList = merchantMemberService
                    .queryAllShopAccountModuleInfoByAccountId(Lists.newArrayList(Math.toIntExact(records.get(0).getDpAccountId())));
            if (CollectionUtils.isEmpty(shopAccountModuleInfoDTOList)) {
                continue;
            }
            appliedInfoResponse.setAccountName(shopAccountModuleInfoDTOList.get(0).getShopAccountDTO().getAccountName());
            appliedInfoResponse.setSettleType(QRRebateSettleTypeEnum.BUSINESS.getCode());
            List<Long> dpShopIdList = records.stream().map(RebateActivityRecordDO::getDpShopId).collect(Collectors.toList());
            //查询店铺信息
            Map<Long, String> poiFullNameMap = poiAclService.batchGetPoiFullNameByDpShopId(dpShopIdList);
            List<AppliedShopInfoResponse> applyShops = new ArrayList<>();
            for (Map.Entry<Long, String> entry : poiFullNameMap.entrySet()) {
                AppliedShopInfoResponse appliedShopInfoResponse = new AppliedShopInfoResponse();
                appliedShopInfoResponse.setDpShopId(entry.getKey());
                appliedShopInfoResponse.setShopName(entry.getValue());
                appliedShopInfoResponse.setDpAccountId(Long.valueOf(dpAccountId));
                applyShops.add(appliedShopInfoResponse);
                appliedShopIdList.add(entry.getKey());
            }
            appliedInfoResponse.setApplyShops(applyShops);
            appliedInfo.add(appliedInfoResponse);
        }
        ApplyInfoResponse accountApplyInfo = new ApplyInfoResponse();
        accountApplyInfo.setAppliedInfo(appliedInfo);
        accountApplyInfo.setShowApplyInfo(CollectionUtils.isNotEmpty(appliedInfo));
        rebateActivityDetailResponse.setApplyInfo(accountApplyInfo);
        context.setAppliedShopIdList(appliedShopIdList);
    }
}
