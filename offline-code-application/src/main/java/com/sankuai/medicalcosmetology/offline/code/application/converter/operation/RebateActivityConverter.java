package com.sankuai.medicalcosmetology.offline.code.application.converter.operation;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.medicalcosmetology.offline.code.api.dto.common.DictDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.DeliveryRestrictionDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateActivityDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RebateJoinConditionDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.rebate.RuleDTO;
import com.sankuai.medicalcosmetology.offline.code.api.enums.ActivityTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ApolloAuditConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.LionConstant;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationExtKeyEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.BusinessDepartmentConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.BusinessDepartmentConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

public class RebateActivityConverter {

    public static OperationInfoDTO convertRebateConfig2OperationInfoDTO(RebateActivityConfigDO rebateActivityConfigDO) {
        OperationInfoDTO operationInfoDTO = new OperationInfoDTO();
        operationInfoDTO.setActivityId(rebateActivityConfigDO.getId());
        if (null != rebateActivityConfigDO.getOperationType()) {
            operationInfoDTO.setActivityType(rebateActivityConfigDO.getOperationType().getCode());
        }

        operationInfoDTO.setActivityName(rebateActivityConfigDO.getOperationName());
        if (null != rebateActivityConfigDO.getStartTime()) {
            operationInfoDTO.setStartTime(rebateActivityConfigDO.getStartTime().getTime());
        }
        if (null != rebateActivityConfigDO.getEndTime()) {
            operationInfoDTO.setEndTime(rebateActivityConfigDO.getEndTime().getTime());
        }

        if (null != rebateActivityConfigDO.getBizType()) {
            operationInfoDTO.setBizType(rebateActivityConfigDO.getBizType().getCode());
        }
        operationInfoDTO.setRules(rebateActivityConfigDO.getActivityRuleDesc());
        operationInfoDTO.setDescription(rebateActivityConfigDO.getDescription());
        operationInfoDTO.setPlatform(rebateActivityConfigDO.getPlatform());
        operationInfoDTO.setPriority(rebateActivityConfigDO.getOrder());
        operationInfoDTO.setStatus(rebateActivityConfigDO.getStatus());
        operationInfoDTO.setCreator(rebateActivityConfigDO.getCreateUser());
        operationInfoDTO.setUpdator(rebateActivityConfigDO.getUpdateUser());
        operationInfoDTO.setCreateTime(rebateActivityConfigDO.getAddTime().getTime());
        operationInfoDTO.setUpdateTime(rebateActivityConfigDO.getUpdateTime().getTime());

        RebateActivityDTO rebateActivityDTO = new RebateActivityDTO();
        operationInfoDTO.setRebateActivityInfo(rebateActivityDTO);
        rebateActivityDTO.setId(rebateActivityConfigDO.getId());
        rebateActivityDTO.setOperationId(rebateActivityConfigDO.getOperationId());
        rebateActivityDTO.setRebateActivityType(rebateActivityConfigDO.getRebateActivityType());
        rebateActivityDTO.setActivityRuleDesc(rebateActivityConfigDO.getActivityRuleDesc());
        rebateActivityDTO.setCondition(JSON.parseObject(rebateActivityConfigDO.getCondition(), RebateJoinConditionDTO.class));
        rebateActivityDTO.setRule(JSON.parseObject(rebateActivityConfigDO.getRule(), RuleDTO.class));
        rebateActivityDTO.setPaymentConfig(rebateActivityConfigDO.getPaymentConfig());
        String extInfo = rebateActivityConfigDO.getExtInfo();
        if (StringUtils.isNotBlank(extInfo)) {
            Map<String, Object> map = JSON.parseObject(extInfo, new TypeReference<Map<String, Object>>() {
            });
            if (map.containsKey(OperationExtKeyEnum.AUDIT_ID.getKey())) {
                Map<String, String> auditUrlMap = Lion.getMap(Environment.getAppName(), LionConstant.APOLLO_AUDIT_URL, String.class);
                if (MapUtils.isNotEmpty(auditUrlMap)) {
                    rebateActivityDTO.setX1AuditUrl(auditUrlMap.get(ApolloAuditConstant.ACTIVITY_X1_AUDIT_URL) + map.get(OperationExtKeyEnum.AUDIT_ID.getKey()));
                    rebateActivityDTO.setFinanceAuditUrl(auditUrlMap.get(ApolloAuditConstant.ACTIVITY_FINANCE_BP_AUDIT_URL) + map.get(OperationExtKeyEnum.AUDIT_ID.getKey()));
                }

            }
        }
        operationInfoDTO.setDeliveryRestriction(new DeliveryRestrictionDTO());
        if (CollectionUtils.isNotEmpty(rebateActivityConfigDO.getLimitBuLine())) {
            List<DictDTO> dictDTOS = new ArrayList<>();
            for (BusinessDepartmentConfigDO businessDepartmentEnum : rebateActivityConfigDO.getLimitBuLine()) {
                DictDTO dicDTO = new DictDTO();
                dicDTO.setValue(String.valueOf(businessDepartmentEnum.getCode()));
                dicDTO.setName(businessDepartmentEnum.getName());
                dictDTOS.add(dicDTO);
            }
            operationInfoDTO.getDeliveryRestriction().setBuLines(dictDTOS);
        }
        if (CollectionUtils.isNotEmpty(rebateActivityConfigDO.getLimitFirstCategorys())) {
            operationInfoDTO.getDeliveryRestriction()
                    .setFirstCategories(rebateActivityConfigDO.getLimitFirstCategorys().stream().map(categoryId -> {
                        DictDTO dicDTO = new DictDTO();
                        dicDTO.setValue(String.valueOf(categoryId));
                        return dicDTO;
                    }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(rebateActivityConfigDO.getLimitSecondCategorys())) {
            operationInfoDTO.getDeliveryRestriction()
                    .setSecondCategories(rebateActivityConfigDO.getLimitSecondCategorys().stream().map(categoryId -> {
                        DictDTO dicDTO = new DictDTO();
                        dicDTO.setValue(String.valueOf(categoryId));
                        return dicDTO;
                    }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(rebateActivityConfigDO.getLimitCitys())) {
            operationInfoDTO.getDeliveryRestriction()
                    .setCityDict(rebateActivityConfigDO.getLimitCitys().stream().map(cityId -> {
                        DictDTO dicDTO = new DictDTO();
                        dicDTO.setValue(String.valueOf(cityId));
                        return dicDTO;
                    }).collect(Collectors.toList()));
            operationInfoDTO.getDeliveryRestriction().setCities(rebateActivityConfigDO.getLimitCitys().stream().map(String::valueOf).collect(Collectors.joining(",")));
            operationInfoDTO.getDeliveryRestriction().setLimitCity(true);
        }
        return operationInfoDTO;

    }

    public static RebateActivityConfigDO convert2RebateActivityConfigDO(OperationInfoDTO operationInfoDTO) {
        if (null == operationInfoDTO) {
            return null;
        }
        RebateActivityConfigDO rebateActivityConfigDO = new RebateActivityConfigDO();
        rebateActivityConfigDO.setId(operationInfoDTO.getRebateActivityInfo().getId());
        rebateActivityConfigDO.setOperationId(operationInfoDTO.getActivityId());
        rebateActivityConfigDO.setOperationName(operationInfoDTO.getActivityName());
        if (Objects.nonNull(operationInfoDTO.getActivityType())) {
            rebateActivityConfigDO.setOperationType(OperationConfigTypeEnum.getByCode(Objects
                    .requireNonNull(ActivityTypeEnum.getByCode(operationInfoDTO.getActivityType())).getInnerCode()));
        }
        rebateActivityConfigDO.setDescription(operationInfoDTO.getDescription());
        rebateActivityConfigDO.setTags(operationInfoDTO.getTags());
        if (Objects.nonNull(operationInfoDTO.getStartTime())) {
            rebateActivityConfigDO.setStartTime(new Date(operationInfoDTO.getStartTime()));
        }
        if (Objects.nonNull(operationInfoDTO.getEndTime())) {
            rebateActivityConfigDO.setEndTime(new Date(operationInfoDTO.getEndTime()));
        }
        if (operationInfoDTO.getPlatform() != null) {
            rebateActivityConfigDO.setPlatform(operationInfoDTO.getPlatform());
        }
        if (operationInfoDTO.getBizType() != null) {
            rebateActivityConfigDO.setBizType(BizTypeEnum.fromCode(operationInfoDTO.getBizType()));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getBuLines())) {
            rebateActivityConfigDO.setLimitBuLine(operationInfoDTO.getDeliveryRestriction().getBuLines().stream()
                    .map(buStr -> BusinessDepartmentConfigService.getByCode(Integer.valueOf(buStr.getValue())))
                    .collect(Collectors.toList()));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getFirstCategories())) {
            Set<Integer> firstCategories = operationInfoDTO.getDeliveryRestriction().getFirstCategories().stream()
                    .filter(Objects::nonNull)
                    .filter(fc -> StringUtils.isNotBlank(fc.getValue()) && NumberUtils.isCreatable(fc.getValue()))
                    .map(fc -> Integer.parseInt(fc.getValue())).collect(Collectors.toSet());
            rebateActivityConfigDO.setLimitFirstCategorys(new ArrayList<>(firstCategories));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getSecondCategories())) {
            Set<Integer> secondCategories = operationInfoDTO.getDeliveryRestriction().getSecondCategories().stream()
                    .filter(Objects::nonNull)
                    .filter(sc -> StringUtils.isNotBlank(sc.getValue()) && NumberUtils.isCreatable(sc.getValue()))
                    .map(sc -> Integer.parseInt(sc.getValue())).collect(Collectors.toSet());
            rebateActivityConfigDO.setLimitSecondCategorys(new ArrayList<>(secondCategories));
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && CollectionUtils.isNotEmpty(operationInfoDTO.getDeliveryRestriction().getCityDict())) {
            Set<Integer> cityIds = operationInfoDTO.getDeliveryRestriction().getCityDict().stream()
                    .filter(Objects::nonNull)
                    .filter(city -> StringUtils.isNotBlank(city.getValue()) && NumberUtils.isCreatable(city.getValue()))
                    .map(city -> Integer.parseInt(city.getValue())).collect(Collectors.toSet());
            rebateActivityConfigDO.setLimitCitys(new ArrayList<>(cityIds));
        }
        // 1：手动输入（shopIds），2：楼层导入（floorIds）
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && StringUtils.isNotBlank(operationInfoDTO.getDeliveryRestriction().getShopIds())
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() != null
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() == 1) {
            rebateActivityConfigDO.setLimitShops(Arrays
                    .stream(operationInfoDTO.getDeliveryRestriction().getShopIds().split(","))
                    .filter(NumberUtils::isCreatable).map(Long::parseLong).distinct().collect(Collectors.toList()));
            rebateActivityConfigDO.setLimitFloorId(-1L);
        } else if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && StringUtils.isNotBlank(operationInfoDTO.getDeliveryRestriction().getFloorIds())
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() != null
                && operationInfoDTO.getDeliveryRestriction().getShopWhiteType() == 2) {
            rebateActivityConfigDO
                    .setLimitFloorId(Arrays.stream(operationInfoDTO.getDeliveryRestriction().getFloorIds().split(","))
                            .filter(NumberUtils::isCreatable).map(Long::parseLong).distinct()
                            .collect(Collectors.toList()).get(0));
            rebateActivityConfigDO.setLimitShops(new ArrayList<>());
        }
        if (Objects.nonNull(operationInfoDTO.getDeliveryRestriction())
                && StringUtils.isNotBlank(operationInfoDTO.getDeliveryRestriction().getDealIds())) {
            rebateActivityConfigDO.setLimitDealFloorId(
                    Arrays.stream(operationInfoDTO.getDeliveryRestriction().getDealIds().split(","))
                            .filter(NumberUtils::isCreatable).map(Long::parseLong).distinct()
                            .collect(Collectors.toList()).get(0));
        }
        RebateActivityDTO rebateActivityDTO = operationInfoDTO.getRebateActivityInfo();
        if (rebateActivityDTO != null) {
            rebateActivityConfigDO.setOperationId(rebateActivityDTO.getOperationId());
            rebateActivityConfigDO.setRebateActivityType(rebateActivityDTO.getRebateActivityType());
            rebateActivityConfigDO.setActivityRuleDesc(operationInfoDTO.getRules());
            rebateActivityConfigDO.setPaymentConfig(rebateActivityDTO.getPaymentConfig());
            rebateActivityConfigDO.setRule(JSONObject.toJSONString(rebateActivityDTO.getRule()));
            rebateActivityConfigDO.setCondition(JSONObject.toJSONString(rebateActivityDTO.getCondition()));
        }
        rebateActivityConfigDO.setStatus(operationInfoDTO.getStatus());
        rebateActivityConfigDO.setCreateUser(operationInfoDTO.getCreator());
        if (MapUtils.isNotEmpty(operationInfoDTO.getExtInfo())) {
            rebateActivityConfigDO.setExtInfo(JSONObject.toJSONString(operationInfoDTO.getExtInfo()));
        }
        rebateActivityConfigDO.setUpdateUser(operationInfoDTO.getUpdator());
        rebateActivityConfigDO.setOrder(operationInfoDTO.getPriority());
        return rebateActivityConfigDO;
    }
}
