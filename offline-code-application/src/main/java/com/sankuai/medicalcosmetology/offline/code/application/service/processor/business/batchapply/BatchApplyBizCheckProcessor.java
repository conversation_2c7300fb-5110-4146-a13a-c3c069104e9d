package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchapply;

import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.offline.code.api.enums.OperationStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityRecordStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.api.request.operation.rebate.ApplyActivityRequest;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ApplyShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.api.response.rebate.ShopInfoResponse;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.OperationConfigForBApplicationService;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.enums.OperationConfigTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.OperationConfigQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordQuery;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityConfigDomainService;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityRecordDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PoiAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * B端-批量报名-参数校验
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.BATCH_APPLY_FOR_B, subScene = ProcessorSubSceneConstants.BIZ_CHECK)
public class BatchApplyBizCheckProcessor implements BaseProcessor<BatchApplyProcessorContext> {

    @Resource
    private RebateActivityRecordDomainService rebateActivityRecordDomainService;

    @Resource
    private RebateActivityConfigDomainService rebateActivityConfigDomainService;

    @Resource
    private PoiAclService poiAclService;

    @Resource
    private OperationConfigForBApplicationService operationConfigForBApplicationService;

    @Override
    public void execute(BatchApplyProcessorContext context) {
        RebateActivityConfigDO rebateActivityConfigDO = context.getRebateActivityConfigDO();
        Long activityId = rebateActivityConfigDO.getId();
        ApplyActivityRequest request = context.getRequest();
        List<Long> selectedDpShopIds = request.getDpShopIds();

        ApplyShopInfoResponse applyShopInfoResponse = operationConfigForBApplicationService.queryApplyShopInfo(rebateActivityConfigDO.getOperationType().getCode(), request.getActivityViewId(), request.getAccountId());
        AssertUtil.notNull(applyShopInfoResponse, "查询可报名门店信息失败");
        Map<String, List<ShopInfoResponse>> appliableShopMap = applyShopInfoResponse.getAppliableShopMap();
        AssertUtil.isTrue(MapUtils.isNotEmpty(appliableShopMap), "无可报名门店");
        List<Long> appliableShopIdList = appliableShopMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()).stream().map(ShopInfoResponse::getDpShopIdLong).collect(Collectors.toList());
        List<Long> notAppliableShopList = selectedDpShopIds.stream().filter(shopId -> !appliableShopIdList.contains(shopId)).collect(Collectors.toList());
        AssertUtil.isTrue(CollectionUtils.isEmpty(notAppliableShopList), "所选门店不可报名");

        Date endTime = rebateActivityConfigDO.getEndTime();
        AssertUtil.isTrue(new Date().before(endTime), "活动已结束");

        boolean isOffline = rebateActivityConfigDO.getStatus() == OperationStatusEnum.OFFLINE.code;
        AssertUtil.isTrue(!isOffline, "活动已下线");

        //是否已报名其他神券活动
        RebateActivityRecordQuery query1 = new RebateActivityRecordQuery();
        query1.setDpShopIdList(selectedDpShopIds);
        query1.setRebateActivityType(rebateActivityConfigDO.getOperationType().getCode());
        query1.setStatus(RebateActivityRecordStatusEnum.VALID.code);
        List<RebateActivityRecordDO> alreadyApplyOtherActivityRecordList = rebateActivityRecordDomainService.queryRebateActivityRecordList(query1);

        String alreadyApplyShopNames = "";
        if (CollectionUtils.isNotEmpty(alreadyApplyOtherActivityRecordList)) {
            //查询已报名的其他活动是否已经过期
            List<Long> appliedOtherActivityIdList = alreadyApplyOtherActivityRecordList.stream().map(RebateActivityRecordDO::getActivityId).collect(Collectors.toList());
            OperationConfigQuery endQuery = new OperationConfigQuery();
            endQuery.setIds(appliedOtherActivityIdList);
            endQuery.setStatus(OperationStatusEnum.ONGOING.code);
            endQuery.setTimeStatus(2);

            //查询已报名的其他活动是否已经下线
            OperationConfigQuery offlineQuery = new OperationConfigQuery();
            offlineQuery.setIds(appliedOtherActivityIdList);
            offlineQuery.setStatus(OperationStatusEnum.OFFLINE.code);
            List<RebateActivityConfigDO> offlineActivityList = rebateActivityConfigDomainService.pageQueryRebateActivityConfig(offlineQuery);

            //已过期的活动
            List<RebateActivityConfigDO> invalidActivityList = rebateActivityConfigDomainService.pageQueryRebateActivityConfig(endQuery);
            if (CollectionUtils.isNotEmpty(invalidActivityList) || CollectionUtils.isNotEmpty(offlineActivityList)) {
                Set<Long> appliedInvalidActivityIdList = invalidActivityList.stream().map(RebateActivityConfigDO::getId).collect(Collectors.toSet());
                List<Long> appliedOfflineActivityIdList = offlineActivityList.stream().map(RebateActivityConfigDO::getId).collect(Collectors.toList());
                //过滤掉已过期的活动
                List<RebateActivityRecordDO> rebateActivityRecordDOList = alreadyApplyOtherActivityRecordList.stream().filter(rebateActivityRecordDO -> !appliedInvalidActivityIdList.contains(rebateActivityRecordDO.getActivityId())).collect(Collectors.toList());
                //过滤掉下线的活动
                rebateActivityRecordDOList = rebateActivityRecordDOList.stream().filter(rebateActivityRecordDO -> !appliedOfflineActivityIdList.contains(rebateActivityRecordDO.getActivityId())).collect(Collectors.toList());
                alreadyApplyShopNames = getAlreadyApplyShopName(rebateActivityRecordDOList, alreadyApplyShopNames);
                AssertUtil.isTrue(StringUtils.isBlank(alreadyApplyShopNames), alreadyApplyShopNames + "已报名其他神券活动，若要报名新活动，需先取消原活动报名");
            } else {
                alreadyApplyShopNames = getAlreadyApplyShopName(alreadyApplyOtherActivityRecordList, alreadyApplyShopNames);
                AssertUtil.isTrue(StringUtils.isBlank(alreadyApplyShopNames), alreadyApplyShopNames + "已报名其他神券活动，若要报名新活动，需先取消原活动报名");
            }
        }

        //是否已报名当前活动
        RebateActivityRecordQuery query2 = new RebateActivityRecordQuery();
        query2.setActivityConfigIdList(Lists.newArrayList(activityId));
        query2.setDpShopIdList(selectedDpShopIds);
        query2.setStatus(RebateActivityRecordStatusEnum.VALID.code);
        List<RebateActivityRecordDO> alreadyApplyRecordList = rebateActivityRecordDomainService.queryRebateActivityRecordList(query2);

        alreadyApplyShopNames = getAlreadyApplyShopName(alreadyApplyRecordList, alreadyApplyShopNames);
        AssertUtil.isTrue(StringUtils.isBlank(alreadyApplyShopNames), alreadyApplyShopNames + "已经报名过当前神券活动");
    }

    private String getAlreadyApplyShopName(List<RebateActivityRecordDO> alreadyApplyOtherActivityList, String alreadyApplyShopNames) {
        if (CollectionUtils.isNotEmpty(alreadyApplyOtherActivityList)) {
            List<Long> alreadyApplyDpShopIdList = alreadyApplyOtherActivityList.stream().map(RebateActivityRecordDO::getDpShopId).collect(Collectors.toList());
            Map<Long, String> alreadyApplyDpShopNameMap = poiAclService.batchGetPoiFullNameByDpShopId(alreadyApplyDpShopIdList);
            if (MapUtils.isNotEmpty(alreadyApplyDpShopNameMap)) {
                alreadyApplyShopNames = StringUtils.join(alreadyApplyDpShopNameMap.values(), "、");
            }
        }
        return alreadyApplyShopNames;
    }
}
