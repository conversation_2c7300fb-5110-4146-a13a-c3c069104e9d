package com.sankuai.medicalcosmetology.offline.code.application.service.handler.operationconfig.manage;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationPageQueryDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.operation.OperationPageResultDTO;

public interface OperationConfigForMHandler {

    String getActivityType();

    /**
     * 分页查询活动列表
     *
     * @param pageQueryDTO 查询条件封装的DTO，包含分页参数及筛选条件
     * @return 返回分页查询结果，包括分页信息和活动列表数据
     */
    OperationPageResultDTO activityPageQuery(OperationPageQueryDTO pageQueryDTO);


    /**
     * 查询活动的详细信息
     *
     * @param activityId 活动ID，用于唯一标识一个发券活动
     * @return 返回远程响应，包含活动的详细信息，如活动名称、活动时间、参与条件等
     */
    OperationInfoDTO activityDetailQuery(Long activityId, Integer activityType);

    /**
     * 活动创建
     *
     * @param activityInfo 活动信息
     * @return 返回操作结果，成功为true，失败为false
     */
    Boolean activityCreate(OperationInfoDTO activityInfo);

    /**
     * 活动上下线
     * 1：上线 2：下线
     */
    void activityUpdateOnlineStatus(Long activityId, Integer activityType, Integer onlineStatus);

    /**
     * 活动编辑
     *
     * @param activityInfo 活动信息
     */
    void activityUpdate(OperationInfoDTO activityInfo);
}
