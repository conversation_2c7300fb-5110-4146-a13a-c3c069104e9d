package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.carnation.distribution.common.enums.BizTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.dto.EmptyCodeInfoDTO;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.FileExportContext;
import com.sankuai.medicalcosmetology.offline.code.api.enums.fileload.FileLoadSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.api.service.PromoCodeCommonService;
import com.sankuai.medicalcosmetology.offline.code.application.model.sso.User;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ExcelUtil;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ElephantPushAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.wrapper.S3Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/20
 * @Description:AOI空码导出
 */
@Service
@Slf4j
public class AoiEmptyCodeExportHandler implements FileExportProcessHandlerIface<EmptyCodeInfoDTO, User> {

    private static final ThreadPoolExecutor executorService = Rhino.newThreadPool("AoiEmptyCodeExport", DefaultThreadPoolProperties.Setter()
            .withCoreSize(5).withMaxSize(10).withBlockingQueue(new LinkedBlockingQueue<>(100))).getExecutor();

    @Autowired
    private PromoCodeCommonService promoCodeCommonService;

    @Autowired
    private S3Wrapper s3Wrapper;

    @Autowired
    private ElephantPushAclService elephantPushAclService;

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    @Override
    public boolean support(Integer sceneType) {
        return FileLoadSceneEnum.MATERIAL_AOI_EMPTYCODE_EXPORT.getType().equals(sceneType);
    }

    public String executeWithResult(FileExportContext context, User param) {
        if (context == null || context.getParam() == null || !NumberUtils.isCreatable(context.getParam().toString())) {
            throw new IllegalArgumentException("参数错误");
        }
        Integer number = (Integer)context.getParam();
        Validate.isTrue(number != null && number > 0, "下载数量不能为空且必须大于0");
        Validate.isTrue(number <= 5000, "下载数量需在1到5000之间");
        executorService.submit(() -> {
            try {
                List<EmptyCodeInfoDTO> beanList = readAsBean(context, param);
                processData(beanList, context, param);
            } catch (Exception e) {
                log.error(getClass().getSimpleName() + ".executeWithResult error", e);
            }
        });
        return getResult(null, null);
    };

    /**
     * 获取处理结果
     *
     * @param beanList 处理后的数据对象列表
     * @param param 额外的参数
     * @return 处理结果的字符串表示，可能是生成的报告、统计信息或其他格式化输出
     */
    @Override
    public String getResult(List<EmptyCodeInfoDTO> beanList, User param) {
        return "下载任务提交成功，稍后下载链接将以大象通知发出，请注意查收。";
    }

    /**
     * 获取要导出的数据
     *
     * @param param
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    @Override
    public List<EmptyCodeInfoDTO> readAsBean(FileExportContext context, User param) {
        Integer number = (Integer)context.getParam();
        List<EmptyCodeInfoDTO> emptyCodeList = new ArrayList<>();
        for (int i = 0; i < number; i++) {
            RemoteResponse<EmptyCodeInfoDTO> response = promoCodeCommonService.generateEmptyCode(BizTypeEnum.H5_AOI_EMPTY_CODE.getType(), true, true);
            if (response == null || !response.isSuccess() || response.getData() == null) {
                log.error(getClass().getSimpleName() + "readFileAsBean failed response:{}", response);
                continue;
            }
            emptyCodeList.add(buildEmptyCodeInfoDTO(response.getData()));
        }
        return emptyCodeList;
    }

    private EmptyCodeInfoDTO buildEmptyCodeInfoDTO(EmptyCodeInfoDTO emptyCodeConfigInfoDTO) {
        EmptyCodeInfoDTO emptyCodeInfoDTO = new EmptyCodeInfoDTO();
        emptyCodeInfoDTO.setQrCodeUrl(emptyCodeConfigInfoDTO.getQrCodeUrl());
        emptyCodeInfoDTO.setOriginalUrl(emptyCodeConfigInfoDTO.getOriginalUrl());
        emptyCodeInfoDTO.setCodeKey(emptyCodeConfigInfoDTO.getCodeKey());
        return emptyCodeInfoDTO;
    }

    /**
     * 处理解析后的数据对象列表
     *
     * @param emptyCodeList 解析后的数据对象列表，每个对象代表一个文件的内容
     * @param req
     * @param param 额外的参数
     */
    @Override
    public void processData(List<EmptyCodeInfoDTO> emptyCodeList, FileExportContext req, User param) {
        List<String> users = getUsers(param);
        String fileName = generateFileName();
        try {
            File file = generateExcelFile(emptyCodeList, fileName);
            uploadAndNotify(file, users);
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + "processData error", e);
            logErrorAndNotify(emptyCodeList, users, e);
        }
    }

    private List<String> getUsers(User param) {
        List<String> users = Lion.getList(Environment.getAppName(), "emptycode.receivers", String.class,
                new ArrayList<>());
        users.add(param.getLogin());
        return users;
    }

    private String generateFileName() {
        return "AoiEmptyCodeLinks-" + System.currentTimeMillis();
    }

    private File generateExcelFile(List<EmptyCodeInfoDTO> emptyCodeList, String fileName) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("no", "编号");
        headers.put("url", "链接");
        headers.put("key", "空码key值");
        headers.put("originalUrl", "源码链接");
        AtomicInteger index = new AtomicInteger(1);
        List<Map<String, Object>> dataList = emptyCodeList.stream().map(d -> {
            Map<String, Object> map = new HashMap<>();
            map.put("no", index.getAndIncrement());
            map.put("url", d.getQrCodeUrl());
            map.put("key", d.getCodeKey());
            map.put("originalUrl", d.getOriginalUrl());
            return map;
        }).sorted(Comparator.comparingInt(m -> (Integer)m.get("no"))).collect(Collectors.toList());

        return ExcelUtil.generateExcelFile(headers, dataList, fileName, "xlsx");
    }

    private void uploadAndNotify(File file, List<String> users) {
        boolean uploadResult = s3Wrapper.uploadFile(file.getName(), file.getAbsolutePath());
        StringBuilder sb = new StringBuilder();
        sb.append("【").append(Environment.getEnvironment()).append("环境").append("】");
        if (uploadResult) {
            sb.append("  AOI空码导出成功，下载链接：").append(s3Wrapper.getDownloadUrl(file.getName()));
        } else {
            sb.append("  AOI空码Excel文件导出失败，请联系研发产品及研发同学! ");
        }
        elephantPushAclService.pushText(sb.toString(), users);
        file.delete();
    }

    private void logErrorAndNotify(List<EmptyCodeInfoDTO> emptyCodeList, List<String> users, Exception e) {
        log.error(getClass().getSimpleName() + ".processData Exception, emptyCodeList:{}",
                JSONObject.toJSONString(emptyCodeList), e);
        elephantPushAclService.pushText("系统异常！AOI空码Excel文件导出失败，请联系研发产品及研发同学! ", users);
    }

}
