package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.FileExportContext;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/20
 * @Description:文件导出处理器
 */
public interface FileExportProcessHandlerIface<E, P> {

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    boolean support(Integer sceneType);

    /**
     * 执行文件导出处理并返回结果
     * 
     * @return 处理结果的字符串表示
     */
    String executeWithResult(FileExportContext req, P param);

    /**
     * 获取处理结果
     *
     * @param beanList 处理后的数据对象列表
     * @param param 额外的参数
     * @return 处理结果的字符串表示，可能是生成的报告、统计信息或其他格式化输出
     */
    String getResult(List<E> beanList, P param);

    /**
     * 获取要导出的数据
     *
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    List<E> readAsBean(FileExportContext req, P param);

    /**
     * 处理解析后的数据对象列表
     *
     * @param modelList 解析后的数据对象列表，每个对象代表一个文件的内容
     * @param param 额外的参数
     */
    void processData(List<E> modelList, FileExportContext req, P param);
}
