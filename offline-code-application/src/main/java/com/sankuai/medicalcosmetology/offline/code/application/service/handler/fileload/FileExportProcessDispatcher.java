package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.FileExportContext;
import com.sankuai.medicalcosmetology.offline.code.api.dto.promocode.manage.MerchantMaterialPageQueryDTO;
import com.sankuai.medicalcosmetology.offline.code.application.service.handler.exception.NotSupportSceneException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FileExportProcessDispatcher<P> {

    @Autowired
    private List<FileExportProcessHandlerIface> ifaceList;

    public String dispatch(FileExportContext req, Integer sceneType, P param) {
        return findIface(sceneType).executeWithResult(req, param);
    }

    private FileExportProcessHandlerIface findIface(Integer sceneType) {
        List<FileExportProcessHandlerIface> hitIfaceList = ifaceList.stream().filter(iface -> iface.support(sceneType))
                .collect(Collectors.toList());
        validateIfaceList(hitIfaceList, sceneType);
        return hitIfaceList.get(0);
    }

    private void validateIfaceList(List<FileExportProcessHandlerIface> hitIfaceList, Integer sceneType) {
        if (CollectionUtils.isEmpty(hitIfaceList)) {
            throw new NotSupportSceneException("暂未支持该场景文件导出");
        }
    }
}
