package com.sankuai.medicalcosmetology.offline.code.application.model.order;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @date 2023/9/14
 **/
@Data
public class OrderPaymentDetailBO {

    /**
     * 支付详情ID
     */
    private String paymentDetailId;

    /**
     * 金额唯一id
     */
    private String amountId;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 金额类型
     * @see com.dianping.pay.order.common.enums.AmountType
     */
    private int amountType;

    /**
     * sku唯一id
     */
    private String skuId;

    /**
     * 支付凭证id
     */
    private String paymentReceiptId;

    /**
     * 订单支付唯一id
     */
    private String orderPaymentId;

    /**
     * 支付平台
     * @see com.dianping.pay.common.enums.PayPlatform
     */
    private int payPlatform;

    /**
     * 支付时间
     */
    private Date addTime;
}
