package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.queryapplyshop;

import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.application.utils.ResourceEncoder;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.utils.AssertUtil;

/**
 * 查询报名店铺参数校验
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.QUERY_APPLY_SHOP_FOR_B, subScene = ProcessorSubSceneConstants.PARAM_CHECK)
public class QueryApplyShopCheckParamProcessor implements BaseProcessor<QueryApplyShopProcessorContext> {
    @Override
    public void execute(QueryApplyShopProcessorContext context) {
        String activityViewId = context.getActivityViewId();
        Long dpAccountId = context.getDpAccountId();
        AssertUtil.notNull(dpAccountId, "点评账号id不能为空");
        AssertUtil.notEmpty(activityViewId, "活动id不能为空");
        AssertUtil.isTrue(ResourceEncoder.checkValid(activityViewId), "活动id不合法");
        Long activityId = ResourceEncoder.decodeResourceViewId(activityViewId);
        context.setActivityId(activityId);
    }
}
