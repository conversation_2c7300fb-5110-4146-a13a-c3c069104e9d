package com.sankuai.medicalcosmetology.offline.code.application.service.handler.fileload;

import com.dianping.gmkt.event.api.enums.PromoCodeSourceType;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeDTO;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.lion.Environment;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.sankuai.medicalcosmetology.offline.code.api.enums.fileload.FileLoadSceneEnum;
import com.sankuai.medicalcosmetology.offline.code.application.model.sso.User;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ElephantPushAclService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.PromoCodeActivityAclService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.Validate;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024/9/20
 * @Description:商家物料管理-上传报表
 */
@Service
@Slf4j
public class PromoCodeStatsFileUploadHandler implements FileUploadProcessHandlerIface<PromoQRCodeDTO, User> {

    private static final ThreadPoolExecutor executorService = Rhino.newThreadPool("promoCodeUpload", DefaultThreadPoolProperties.Setter()
            .withCoreSize(5).withMaxSize(10).withBlockingQueue(new LinkedBlockingQueue<>(100))).getExecutor();

    private static final int BATCH_CREATE_PROMOQRCODE_MAX_NUM = 5000;

    @Autowired
    private ElephantPushAclService elephantPushAclService;

    @Autowired
    private PromoCodeActivityAclService promoCodeActivityAclService;

    /**
     * 执行文件上传处理并返回结果
     *
     * @return 处理结果的字符串表示
     */
    @Override
    public String executeWithResult(InputStream inputStream, User user) throws IOException {
        List<PromoQRCodeDTO> fileData = readFileAsBean(inputStream, user);
        processData(fileData, user);
        return String.format("成功解析%d条数据，稍后上传结果将以大象通知发出，请注意查收", fileData.size());
    }

    /**
     * 判断当前处理器是否支持给定的场景类型。
     *
     * @param sceneType 场景类型的标识符，用于区分不同的处理场景。
     * @return 如果支持给定的场景类型，则返回true；否则返回false。
     */
    @Override
    public boolean support(Integer sceneType) {
        return FileLoadSceneEnum.MATERIAL_STATS.getType().equals(sceneType);
    }

    /**
     * 将文件项列表读取为对象列表
     *
     * @return 解析后的对象列表，每个对象代表一个文件的内容
     */
    @Override
    public List<PromoQRCodeDTO> readFileAsBean(InputStream inputStream, User user) throws IOException {
        validateInputStreamAndUser(inputStream, user);
        Workbook wb = WorkbookFactory.create(inputStream);
        validateWorkbook(wb);
        Sheet sheet = wb.getSheetAt(0);
        validateSheet(sheet);
        validateRowCount(sheet.getLastRowNum());

        List<PromoQRCodeDTO> dtoList = new ArrayList<>();
        Set<Long> shopIdSet = new HashSet<>();

        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            validateRow(row, i);
            PromoQRCodeDTO dto = createPromoQRCodeDTOFromRow(row, user.getId());
            validateShopIdNotRepeated(shopIdSet, dto.getShopIdLong());
            shopIdSet.add(dto.getShopIdLong());
            dtoList.add(dto);
        }
        return dtoList;
    }

    private void validateInputStreamAndUser(InputStream inputStream, User user) throws IOException {
        if (inputStream.available() == 0 || user == null) {
            throw new IllegalArgumentException("InputStream or User is null or empty.");
        }
    }

    private void validateWorkbook(Workbook wb) {
        if (wb == null) {
            throw new IllegalArgumentException("Workbook is null.");
        }
    }

    private void validateSheet(Sheet sheet) {
        if (sheet == null) {
            throw new IllegalArgumentException("Sheet is null.");
        }
    }

    private void validateRowCount(int lastRowNum) {
        if (lastRowNum >= BATCH_CREATE_PROMOQRCODE_MAX_NUM) {
            throw new IllegalArgumentException(String.format("上传条数超过最大值:%d条", BATCH_CREATE_PROMOQRCODE_MAX_NUM));
        }
    }

    private void validateRow(Row row, int rowIndex) {
        if (row == null || row.getCell(0) == null || StringUtils.isBlank(row.getCell(0).getStringCellValue())) {
            throw new IllegalArgumentException(String.format("%d行 不能存在空值", rowIndex + 1));
        }
    }

    private PromoQRCodeDTO createPromoQRCodeDTOFromRow(Row row, long customerId) {
        PromoQRCodeDTO dto = new PromoQRCodeDTO();
        dto.setShopIdLong(Long.valueOf(row.getCell(6).getStringCellValue()));
        dto.setApplyName(row.getCell(7).getStringCellValue());
        dto.setApplyMobileNo(row.getCell(8).getStringCellValue());
        dto.setAddress(row.getCell(10).getStringCellValue());
        dto.setRegion(row.getCell(9).getStringCellValue());
        dto.setCustomerId((int)customerId);
        dto.setFirstTag(getCellStringValue(row.getCell(11)));
        dto.setSecondTag(getCellStringValue(row.getCell(12)));
        dto.setSourceType(PromoCodeSourceType.getByDesc(row.getCell(5).getStringCellValue()).getCode());
        checkShopPhoneNumber(dto.getApplyMobileNo(), dto.getShopIdLong());
        return dto;
    }

    private void validateShopIdNotRepeated(Set<Long> shopIdSet, Long shopId) {
        if (shopIdSet.contains(shopId)) {
            throw new IllegalArgumentException(String.format("shopId: %d, 重复", shopId));
        }
    }

    private String getCellStringValue(Cell cell) {
        return cell != null && cell.getCellTypeEnum() == CellType.STRING ? cell.getStringCellValue() : "";
    }

    /**
     * 处理解析后的数据对象列表
     *
     * @param modelList 解析后的数据对象列表，每个对象代表一个文件的内容
     */
    @Override
    public void processData(List<PromoQRCodeDTO> modelList, User user) {
        executorService.submit(() -> batchCreatePromoQRCode(modelList, user.getLogin()));
    }

    public void batchCreatePromoQRCode(List<PromoQRCodeDTO> promoQRCodeDTOList, String currentUser) {
        Map<String, List<Long>> failShopIdMap = new HashMap<>();
        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();

        for (PromoQRCodeDTO dto : promoQRCodeDTOList) {
            processSinglePromoQRCode(dto, failShopIdMap).ifPresent(result -> {
                if (result) {
                    successCount.getAndIncrement();
                } else {
                    failCount.getAndIncrement();
                }
            });
        }

        String resultMessage = buildResultMessage(promoQRCodeDTOList.size(), successCount.get(), failCount.get(),
                failShopIdMap, currentUser);
        log.info(getClass().getSimpleName() + ".batchCreatePromoQRCode done,result:{}", resultMessage);
        notifyResult(resultMessage, currentUser);
    }

    private Optional<Boolean> processSinglePromoQRCode(PromoQRCodeDTO dto, Map<String, List<Long>> failShopIdMap) {
        try {
            PromoQRCodeResponse<Boolean> response = promoCodeActivityAclService.createPromoCodeFromRainbow(dto);
            Thread.sleep(50); // 模拟处理时间
            if (response == null || !response.isSuccess()) {
                recordFailure(dto.getShopIdLong(), response != null ? response.getMsg() : "系统错误", failShopIdMap);
                return Optional.of(false);
            }
            return Optional.of(true);
        } catch (Exception e) {
            log.error(getClass().getSimpleName() + ".batchCreatePromoQRCode ,fail on create {}", dto, e);
            recordFailure(dto.getShopIdLong(), "系统错误", failShopIdMap);
            return Optional.of(false);
        }
    }

    private void recordFailure(Long shopId, String message, Map<String, List<Long>> failShopIdMap) {
        failShopIdMap.computeIfAbsent(message, k -> new LinkedList<>()).add(shopId);
    }

    private String buildResultMessage(int total, int successCount, int failCount, Map<String, List<Long>> failShopIdMap,
            String currentUser) {
        StringBuilder sb = new StringBuilder();
        sb.append("【环境】: ").append(Environment.getEnvironment()).append(System.lineSeparator())
                .append("【操作】: 彩虹系统/优惠码/上传美团问卷导出的excel文件并批量创建优惠码").append(System.lineSeparator()).append("【操作人】: ")
                .append(currentUser).append(System.lineSeparator()).append("【结果】:").append(System.lineSeparator())
                .append(" 总计上传数: ").append(total).append(System.lineSeparator()).append(" 成功数: ").append(successCount)
                .append(System.lineSeparator()).append(" 失败数: ").append(failCount).append(System.lineSeparator());
        if (failCount > 0) {
            sb.append(" 失败详情: ").append(System.lineSeparator());
            failShopIdMap.forEach((key, value) -> sb.append(" ").append(key).append(".  shopIds: ").append(value)
                    .append(System.lineSeparator()));
        }
        return sb.toString();
    }

    private void notifyResult(String message, String currentUser) {
        List<String> receiveUserList = Arrays.asList("chenhaoyang02", "lipengyu04", "fei.pan",
                currentUser);
        elephantPushAclService.pushText(message, receiveUserList);
    }

    private void checkShopPhoneNumber(String phoneNumber, Long dpShopId) {
        Validate.isTrue(StringUtils.isNotBlank(phoneNumber), String.format("shopId: %d, 手机号不能为空", dpShopId));
        Validate.isTrue(NumberUtils.isDigits(phoneNumber), String.format("shopId: %d, 手机号含有非数字字符", dpShopId));
        Validate.isTrue(phoneNumber.length() == 11, String.format("shopId: %d, 手机号长度不正确", dpShopId));
    }
}
