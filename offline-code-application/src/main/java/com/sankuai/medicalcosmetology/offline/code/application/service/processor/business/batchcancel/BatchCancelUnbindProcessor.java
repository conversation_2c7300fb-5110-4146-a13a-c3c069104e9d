package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchcancel;

import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityRecordStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityRecordDomainService;

import javax.annotation.Resource;
import java.util.List;

/**
 * B端-批量取消报名-解绑活动
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.BATCH_CANCEL_APPLY_FOR_B, subScene = ProcessorSubSceneConstants.UNBIND_ACTIVITY)
public class BatchCancelUnbindProcessor implements BaseProcessor<BatchCancelApplyProcessorContext> {

    @Resource
    private RebateActivityRecordDomainService rebateActivityRecordDomainService;

    @Override
    public void execute(BatchCancelApplyProcessorContext context) {
        List<RebateActivityRecordDO> rebateActivityRecordDOList = context.getRebateActivityRecordDOList();
        for (RebateActivityRecordDO rebateActivityRecordDO : rebateActivityRecordDOList) {
            rebateActivityRecordDO.setStatus(RebateActivityRecordStatusEnum.UN_BIND.code);
            rebateActivityRecordDomainService.updateRebateActivityRecord(rebateActivityRecordDO);
        }
    }
}
