package com.sankuai.medicalcosmetology.offline.code.application.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/18
 * @Description:
 */
@Getter
public enum PromoCodeBindTypeEnum {

    WIND(1, "风信"),
    OFFLINE_CODE(2, "线下码"),
    AOI_CODE(3, "AOI码");
    
    private final int code;

    private final String desc;

    PromoCodeBindTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static PromoCodeBindTypeEnum fromCode(int code) {
        for (PromoCodeBindTypeEnum enumValue : PromoCodeBindTypeEnum.values()) {
            if (enumValue.getCode() == code)
                return enumValue;
        }
        return null;
    }

}
