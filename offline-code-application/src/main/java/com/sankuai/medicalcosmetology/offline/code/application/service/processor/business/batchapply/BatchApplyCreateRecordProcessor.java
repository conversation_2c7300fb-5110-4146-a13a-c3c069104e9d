package com.sankuai.medicalcosmetology.offline.code.application.service.processor.business.batchapply;

import com.alibaba.fastjson.JSON;
import com.sankuai.carnation.distribution.wallet.distribute.platform.dto.CreateActivityAccountDTO;
import com.sankuai.carnation.distribution.wallet.distribute.platform.request.CreateActivityAccountRequest;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletAccountActivitySourceEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.enums.WalletPayTypeEnum;
import com.sankuai.carnation.distribution.wallet.fundamental.request.WalletPayStrategyRequest;
import com.sankuai.medicalcosmetology.offline.code.api.enums.RegistrationActivityTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateActivityRecordStatusEnum;
import com.sankuai.medicalcosmetology.offline.code.api.enums.rebate.RebateSettleTypeEnum;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorBizTypeConstant;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorMainSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.constants.ProcessorSubSceneConstants;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.BaseProcessor;
import com.sankuai.medicalcosmetology.offline.code.application.service.processor.annotations.Processor;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityConfigDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.model.RebateActivityRecordDO;
import com.sankuai.medicalcosmetology.offline.code.domain.activity.service.RebateActivityRecordDomainService;
import com.sankuai.medicalcosmetology.offline.code.infrastructure.proxy.ActivityWalletService;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * B端-批量报名-参数校验
 */
@Processor(bizType = ProcessorBizTypeConstant.GOD_COUPON_REBATE_ACTIVITY, mainScene = ProcessorMainSceneConstants.BATCH_APPLY_FOR_B, subScene = ProcessorSubSceneConstants.CREATE_RECORD)
public class BatchApplyCreateRecordProcessor implements BaseProcessor<BatchApplyProcessorContext> {

    @Resource
    private ActivityWalletService activityWalletService;

    @Resource
    private RebateActivityRecordDomainService rebateActivityRecordDomainService;

    @Override
    public void execute(BatchApplyProcessorContext context) {
        List<Long> selectedDpShopIds = context.getRequest().getDpShopIds();
        RebateActivityConfigDO rebateActivityConfigDO = context.getRebateActivityConfigDO();
        Long activityConfigId = rebateActivityConfigDO.getId();
        Long accountId = context.getRequest().getAccountId();

        List<Long> successDpShopIds = new ArrayList<>();
        //创建报名记录
        for (Long dpShopId : selectedDpShopIds) {


            RebateActivityRecordDO rebateActivityRecordDO = new RebateActivityRecordDO();
            rebateActivityRecordDO.setRegistrationType((byte) RegistrationActivityTypeEnum.PROMO_CODE_SHOP_REBATE.code);
            rebateActivityRecordDO.setActivityId(activityConfigId);
            rebateActivityRecordDO.setRebateActivityType(rebateActivityConfigDO.getOperationType().getCode());
            rebateActivityRecordDO.setBizType((byte) rebateActivityConfigDO.getBizType().getCode());
            if (CollectionUtils.isNotEmpty(rebateActivityConfigDO.getLimitBuLine())) {
                rebateActivityRecordDO.setBizLine(JSON.toJSONString(rebateActivityConfigDO.getLimitBuLine()));
            }
            rebateActivityRecordDO.setSettleType((byte) RebateSettleTypeEnum.BUSINESS.getCode());
            rebateActivityRecordDO.setDpShopId(dpShopId);
            rebateActivityRecordDO.setDpAccountId(accountId);
            rebateActivityRecordDO.setDistributorType(0);
            rebateActivityRecordDO.setDistributorId(0L);
            rebateActivityRecordDO.setRule(rebateActivityConfigDO.getRule());
            rebateActivityRecordDO.setMtUserId(0L);
            rebateActivityRecordDO.setRebateOrderCnt(0);
            rebateActivityRecordDO.setRebateUserCnt(0);
            rebateActivityRecordDO.setRebateAmount(0L);
            rebateActivityRecordDO.setStatus(RebateActivityRecordStatusEnum.VALID.code);
            Long recordId = rebateActivityRecordDomainService.create(rebateActivityRecordDO);

            //创建钱包
            CreateActivityAccountRequest createActivityAccountRequest = new CreateActivityAccountRequest();
            createActivityAccountRequest.setActivitySource(WalletAccountActivitySourceEnum.MAGIC_COUPON_SHOP_ACTIVITY.getCode());
            createActivityAccountRequest.setActivityId(recordId);
            WalletPayStrategyRequest payStrategyRequest = new WalletPayStrategyRequest();
            payStrategyRequest.setPayType(WalletPayTypeEnum.BUSINESS_ACCOUNT.getCode());
            payStrategyRequest.setCustomerId(accountId);
            createActivityAccountRequest.setPayStrategy(payStrategyRequest);
            createActivityAccountRequest.setShopId(dpShopId);
            CreateActivityAccountDTO activityAccount = activityWalletService.createActivityAccount(createActivityAccountRequest);
            if (activityAccount != null) {
                rebateActivityRecordDO.setWalletActivityAccountId(activityAccount.getWalletActivityAccountId());
                rebateActivityRecordDO.setWalletAccountId(activityAccount.getWalletAccountId());
                rebateActivityRecordDO.setId(recordId);
                rebateActivityRecordDomainService.updateRebateActivityRecord(rebateActivityRecordDO);
            }
            successDpShopIds.add(dpShopId);
        }
        context.setApplySuccessShopIdList(successDpShopIds);
    }
}
