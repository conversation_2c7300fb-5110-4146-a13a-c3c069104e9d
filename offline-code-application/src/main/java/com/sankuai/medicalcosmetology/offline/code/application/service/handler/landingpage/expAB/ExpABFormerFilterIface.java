package com.sankuai.medicalcosmetology.offline.code.application.service.handler.landingpage.expAB;

import com.sankuai.medicalcosmetology.offline.code.api.request.landingpage.QueryExpABInfoRequest;
import com.sankuai.medicalcosmetology.offline.code.application.model.ExpABFormerFilterField;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/12/18
 * @Description:
 */
public interface ExpABFormerFilterIface {

    boolean support(String key);

    boolean queryFilterResult(QueryExpABInfoRequest request, String filterRule);
}
