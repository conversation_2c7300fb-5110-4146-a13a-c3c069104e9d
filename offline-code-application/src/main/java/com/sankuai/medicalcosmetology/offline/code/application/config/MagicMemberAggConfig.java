package com.sankuai.medicalcosmetology.offline.code.application.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13
 * @Description:
 */
@Data
public class MagicMemberAggConfig implements Serializable {

    /**
     * 是否全量
     */
    private Boolean openAll;

    /**
     * 灰度用户ID列表,逗号分隔
     */
    private String mtUserIds;

    /**
     * 是否使用促销
     */
    private Boolean usePromo;

    public boolean doAggregate(Long mtUserId) {
        if (isOpenForAll()) {
            return true;
        }
        return isUserInWhitelist(mtUserId);
    }

    private boolean isOpenForAll() {
        return openAll;
    }

    private boolean isUserInWhitelist(Long mtUserId) {
        if (StringUtils.isBlank(mtUserIds)) {
            return false;
        }
        List<String> whitelistUsers = Arrays.asList(mtUserIds.split(","));
        return whitelistUsers.contains(String.valueOf(mtUserId));
    }

    public boolean isUsePromo() {
        return usePromo;
    }
}
