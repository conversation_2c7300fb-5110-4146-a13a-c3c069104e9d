package com.sankuai.medicalcosmetology.offline.code.application.enums;

/**
 * @Author: chenhaoyang02
 * @Date: 2024/4/24
 * @Description:
 */
public enum GrayTypeEnum {

    OFF(0, "关闭"),
    ALL(1, "全量"),
    WHITE_DP_SHOP_ID_LIST(2, "点评shopId白名单"),
    WHITE_BACK_FIRST_CATEGORY(3, "后台一级类目"),
    WHITE_CUSTOMER_ID_LIST(4, "统一客户id白名单");

    Integer code;
    String desc;

    GrayTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GrayTypeEnum getByCode(Integer code) {
        for (GrayTypeEnum grayType : GrayTypeEnum.values()) {
            if (grayType.code.equals(code)) {
                return grayType;
            }
        }
        throw new IllegalArgumentException("错误的灰度策略");
    }
}
