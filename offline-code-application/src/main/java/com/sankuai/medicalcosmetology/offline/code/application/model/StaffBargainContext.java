package com.sankuai.medicalcosmetology.offline.code.application.model;

import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.medicalcosmetology.offline.code.api.request.staffbargain.StaffBargainCodeRequest;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/6/24 14:55
 */
@Data
public class StaffBargainContext {
    private StaffBargainCodeRequest request;
    private DealGroupDTO dealGroupDTO;
    private Long staffId;
    private Long dpShopId;
    private BigDecimal minBargainPrice;
    private BigDecimal maxBargainPrice;
    private Integer bizType;
}
